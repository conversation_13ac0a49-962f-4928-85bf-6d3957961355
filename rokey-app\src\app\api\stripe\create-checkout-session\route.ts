import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-02-24.acacia',
});

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(req: NextRequest) {
  try {
    const { priceId, userId, userEmail, tier, signup, pendingUserData } = await req.json();

    // For signup flow, we don't need userId yet
    if (signup) {
      // Validate required fields for signup
      if (!priceId || !userEmail || !tier || !pendingUserData) {
        return NextResponse.json(
          { error: 'Missing required fields for signup: priceId, userEmail, tier, pendingUserData' },
          { status: 400 }
        );
      }
    } else {
      // Validate required fields for existing user
      if (!priceId || !userId || !userEmail || !tier) {
        return NextResponse.json(
          { error: 'Missing required fields: priceId, userId, userEmail, tier' },
          { status: 400 }
        );
      }
    }

    // Validate tier
    if (!['starter', 'professional', 'enterprise'].includes(tier)) {
      return NextResponse.json(
        { error: 'Invalid tier. Must be starter, professional, or enterprise' },
        { status: 400 }
      );
    }

    // Validate price ID matches tier
    const expectedPriceId = getPriceIdForTier(tier);
    if (priceId !== expectedPriceId) {
      return NextResponse.json(
        { error: 'Price ID does not match selected tier' },
        { status: 400 }
      );
    }

    // Check if user already has an active subscription (only for existing users)
    if (!signup && userId) {
      const { data: existingSubscription } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'active')
        .single();

      if (existingSubscription) {
        return NextResponse.json(
          { error: 'User already has an active subscription' },
          { status: 400 }
        );
      }
    }

    // Create or retrieve Stripe customer
    let customer: Stripe.Customer;
    
    // First, try to find existing customer by email
    const existingCustomers = await stripe.customers.list({
      email: userEmail,
      limit: 1,
    });

    if (existingCustomers.data.length > 0) {
      customer = existingCustomers.data[0];
    } else {
      // Create new customer
      customer = await stripe.customers.create({
        email: userEmail,
        metadata: {
          user_id: userId,
        },
      });
    }

    // Create checkout session
    const sessionParams: any = {
      customer: customer.id,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://roukey.online'}/auth/callback?session_id={CHECKOUT_SESSION_ID}&payment_success=true&redirectTo=${encodeURIComponent('/dashboard')}`,
      cancel_url: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://roukey.online'}/pricing?plan=${tier}&payment_cancelled=true`,
      metadata: {
        user_id: userId || 'pending_signup',
        tier: tier,
        signup: signup ? 'true' : 'false',
        pending_user_data: signup ? JSON.stringify(pendingUserData) : undefined,
      },
      subscription_data: {
        metadata: {
          user_id: userId || 'pending_signup',
          tier: tier,
          signup: signup ? 'true' : 'false',
          pending_user_data: signup ? JSON.stringify(pendingUserData) : undefined,
        },
      },
      allow_promotion_codes: true,
      billing_address_collection: 'required',
      customer_update: {
        address: 'auto',
        name: 'auto',
      },
    };

    const session = await stripe.checkout.sessions.create(sessionParams);

    return NextResponse.json({ 
      sessionId: session.id,
      url: session.url 
    });

  } catch (error) {
    console.error('Error creating checkout session:', error);
    
    if (error instanceof Stripe.errors.StripeError) {
      return NextResponse.json(
        { error: `Stripe error: ${error.message}` },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function getPriceIdForTier(tier: string): string {
  switch (tier) {
    case 'starter':
      return process.env.STRIPE_STARTER_PRICE_ID!;
    case 'professional':
      return process.env.STRIPE_PROFESSIONAL_PRICE_ID!;
    case 'enterprise':
      return process.env.STRIPE_ENTERPRISE_PRICE_ID!;
    default:
      throw new Error(`Invalid tier: ${tier}`);
  }
}

// Helper function to get tier display names
function getTierDisplayName(tier: string): string {
  switch (tier) {
    case 'starter':
      return 'Starter';
    case 'professional':
      return 'Professional';
    case 'enterprise':
      return 'Enterprise';
    default:
      return 'Unknown';
  }
}

// Helper function to get tier prices
function getTierPrice(tier: string): string {
  switch (tier) {
    case 'starter':
      return '$29';
    case 'professional':
      return '$99';
    case 'enterprise':
      return '$299';
    default:
      return 'Unknown';
  }
}

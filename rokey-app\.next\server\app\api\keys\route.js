(()=>{var e={};e.id=1666,e.ids=[1666],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{Q:()=>a,x:()=>o});var s=r(34386),i=r(44999);async function o(){let e=await (0,i.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}function a(e){return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,r){},remove(e,t){}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56534:(e,t,r)=>{"use strict";r.d(t,{Y:()=>d,w:()=>u});var s=r(55511),i=r.n(s);let o="aes-256-gcm",a=process.env.ROKEY_ENCRYPTION_KEY;if(!a||64!==a.length)throw Error("Invalid ROKEY_ENCRYPTION_KEY. Please set a 64-character hex string (32 bytes) for ROKEY_ENCRYPTION_KEY in your .env.local file.");let n=Buffer.from(a,"hex");function u(e){if("string"!=typeof e||0===e.length)throw Error("Encryption input must be a non-empty string.");let t=i().randomBytes(12),r=i().createCipheriv(o,n,t),s=r.update(e,"utf8","hex");s+=r.final("hex");let a=r.getAuthTag();return`${t.toString("hex")}:${a.toString("hex")}:${s}`}function d(e){if("string"!=typeof e||0===e.length)throw Error("Decryption input must be a non-empty string.");let t=e.split(":");if(3!==t.length)throw Error("Invalid encrypted text format. Expected iv:authTag:encryptedData");let r=Buffer.from(t[0],"hex"),s=Buffer.from(t[1],"hex"),a=t[2];if(12!==r.length)throw Error("Invalid IV length. Expected 12 bytes.");if(16!==s.length)throw Error("Invalid authTag length. Expected 16 bytes.");let u=i().createDecipheriv(o,n,r);u.setAuthTag(s);let d=u.update(a,"hex","utf8");return d+u.final("utf8")}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88502:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>g,serverHooks:()=>y,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{GET:()=>f,POST:()=>l,PUT:()=>m});var i=r(96559),o=r(48088),a=r(37719),n=r(32190),u=r(2507),d=r(56534),p=r(55511),c=r.n(p);async function l(e){let t=(0,u.Q)(e),{data:{session:r},error:s}=await t.auth.getSession();if(s||!r?.user)return n.NextResponse.json({error:"Unauthorized: You must be logged in to create API keys."},{status:401});r.user;try{let{custom_api_config_id:r,provider:s,predefined_model_id:i,api_key_raw:o,label:a,temperature:u=1}=await e.json();if(!r||!s||!i||!o||!a)return n.NextResponse.json({error:"Missing required fields: custom_api_config_id, provider, predefined_model_id, api_key_raw, label"},{status:400});if(u<0||u>2)return n.NextResponse.json({error:"Temperature must be between 0.0 and 2.0"},{status:400});if("string"!=typeof o||0===o.trim().length)return n.NextResponse.json({error:"API key cannot be empty."},{status:400});let p=(0,d.w)(o),l=c().createHash("sha256").update(o).digest("hex"),{data:f,error:m}=await t.from("api_keys").insert({custom_api_config_id:r,provider:s,predefined_model_id:i,encrypted_api_key:p,label:a,api_key_hash:l,status:"active",is_default_general_chat_model:!1,temperature:u}).select().single();if(m){if("23503"===m.code)return n.NextResponse.json({error:"Invalid custom_api_config_id or predefined_model_id.",details:m.message},{status:400});if("23505"===m.code){if(m.message.includes("unique_model_per_config"))return n.NextResponse.json({error:"This model is already configured in this setup. Each model can only be used once per configuration, but you can use the same API key with different models.",details:m.message},{status:409});return n.NextResponse.json({error:"A unique constraint was violated.",details:m.message},{status:409})}return n.NextResponse.json({error:"Failed to save API key",details:m.message},{status:500})}if(f){let{data:e,error:s}=await t.from("api_keys").select("id").eq("custom_api_config_id",r).eq("is_default_general_chat_model",!0).neq("id",f.id).limit(1);if(s);else if(!e||0===e.length){let{data:e,error:r}=await t.from("api_keys").update({is_default_general_chat_model:!0}).eq("id",f.id).select().single();if(!r)return n.NextResponse.json(e,{status:201})}}return n.NextResponse.json(f,{status:201})}catch(e){if("SyntaxError"===e.name)return n.NextResponse.json({error:"Invalid request body: Malformed JSON."},{status:400});if(e.message.includes("Invalid ROKEY_ENCRYPTION_KEY")||e.message.includes("Encryption input must be a non-empty string"))return n.NextResponse.json({error:"Server-side encryption error",details:e.message},{status:500});return n.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}async function f(e){let t=(0,u.Q)(e),{data:{session:r},error:s}=await t.auth.getSession();if(s||!r?.user)return n.NextResponse.json({error:"Unauthorized: You must be logged in to view API keys."},{status:401});r.user;let{searchParams:i}=new URL(e.url),o=i.get("custom_config_id");if(!o)return n.NextResponse.json({error:"custom_config_id query parameter is required"},{status:400});try{let{data:e,error:r}=await t.from("api_keys").select("id, custom_api_config_id, provider, predefined_model_id, label, status, temperature, created_at, last_used_at").eq("custom_api_config_id",o).order("created_at",{ascending:!1});if(r)return n.NextResponse.json({error:"Failed to fetch API keys",details:r.message},{status:500});let s=(e||[]).map(e=>({id:e.id,custom_api_config_id:e.custom_api_config_id,provider:e.provider,predefined_model_id:e.predefined_model_id,label:e.label,status:e.status,temperature:e.temperature,created_at:e.created_at,last_used_at:e.last_used_at}));return n.NextResponse.json(s,{status:200})}catch(e){return n.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}async function m(e){let t=(0,u.Q)(e),{data:{session:r},error:s}=await t.auth.getSession();if(s||!r?.user)return n.NextResponse.json({error:"Unauthorized: You must be logged in to update API keys."},{status:401});r.user;let{searchParams:i}=new URL(e.url),o=i.get("id");if(!o)return n.NextResponse.json({error:"id query parameter is required"},{status:400});try{let{temperature:r,predefined_model_id:s}=await e.json(),i={};if(void 0!==r){if(r<0||r>2)return n.NextResponse.json({error:"Temperature must be between 0.0 and 2.0"},{status:400});i.temperature=r}if(void 0!==s){if("string"!=typeof s||0===s.trim().length)return n.NextResponse.json({error:"Model ID must be a non-empty string"},{status:400});i.predefined_model_id=s}if(0===Object.keys(i).length)return n.NextResponse.json({error:"No valid fields provided for update"},{status:400});let{data:a,error:u}=await t.from("api_keys").update(i).eq("id",o).select("id, custom_api_config_id, provider, predefined_model_id, label, status, temperature, created_at, last_used_at").single();if(u){if("23505"===u.code&&u.message.includes("unique_model_per_config"))return n.NextResponse.json({error:"This model is already configured in this setup. Each model can only be used once per configuration.",details:u.message},{status:409});return n.NextResponse.json({error:"Failed to update API key",details:u.message},{status:500})}return n.NextResponse.json(a,{status:200})}catch(e){if("SyntaxError"===e.name)return n.NextResponse.json({error:"Invalid request body: Malformed JSON."},{status:400});return n.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}let g=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/keys/route",pathname:"/api/keys",filename:"route",bundlePath:"app/api/keys/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\keys\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:_,workUnitAsyncStorage:h,serverHooks:y}=g;function x(){return(0,a.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:h})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,3410],()=>r(88502));module.exports=s})();
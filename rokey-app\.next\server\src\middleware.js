(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[550],{3:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Headers:()=>a,Request:()=>o,Response:()=>l,default:()=>n,fetch:()=>s});var i=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==r.g)return r.g;throw Error("unable to locate global object")}();let s=i.fetch,n=i.fetch.bind(i),a=i.Headers,o=i.Request,l=i.Response},35:(e,t)=>{"use strict";var r=Array.isArray,i=Symbol.for("react.transitional.element"),s=Symbol.for("react.portal"),n=(Symbol.for("react.fragment"),Symbol.for("react.strict_mode"),Symbol.for("react.profiler"),Symbol.for("react.forward_ref"),Symbol.for("react.suspense"),Symbol.for("react.memo"),Symbol.for("react.lazy")),a=Symbol.iterator;Object.prototype.hasOwnProperty,Object.assign;var o=/\/+/g;function l(e,t){var r,i;return"object"==typeof e&&null!==e&&null!=e.key?(r=""+e.key,i={"=":"=0",":":"=2"},"$"+r.replace(/[=:]/g,function(e){return i[e]})):t.toString(36)}function u(){}},128:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_HEADERS=void 0;let i=r(203);t.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${i.version}`}},196:(e,t,r)=>{"use strict";e.exports=r(35)},201:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getTestReqInfo:function(){return a},withRequest:function(){return n}});let i=new(r(521)).AsyncLocalStorage;function s(e,t){let r=t.header(e,"next-test-proxy-port");if(!r)return;let i=t.url(e);return{url:i,proxyPort:Number(r),testData:t.header(e,"next-test-data")||""}}function n(e,t,r){let n=s(e,t);return n?i.run(n,r):r()}function a(e,t){let r=i.getStore();return r||(e&&t?s(e,t):void 0)}},203:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.version=void 0,t.version="0.0.0-automated"},279:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let s=i(r(3)),n=i(r(784));class a{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=s.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let r=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async e=>{var t,r,i;let s=null,a=null,o=null,l=e.status,u=e.statusText;if(e.ok){if("HEAD"!==this.method){let t=await e.text();""===t||(a="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}let i=null==(t=this.headers.Prefer)?void 0:t.match(/count=(exact|planned|estimated)/),n=null==(r=e.headers.get("content-range"))?void 0:r.split("/");i&&n&&n.length>1&&(o=parseInt(n[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(a)&&(a.length>1?(s={code:"PGRST116",details:`Results contain ${a.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},a=null,o=null,l=406,u="Not Acceptable"):a=1===a.length?a[0]:null)}else{let t=await e.text();try{s=JSON.parse(t),Array.isArray(s)&&404===e.status&&(a=[],s=null,l=200,u="OK")}catch(r){404===e.status&&""===t?(l=204,u="No Content"):s={message:t}}if(s&&this.isMaybeSingle&&(null==(i=null==s?void 0:s.details)?void 0:i.includes("0 rows"))&&(s=null,l=200,u="OK"),s&&this.shouldThrowOnError)throw new n.default(s)}return{error:s,data:a,count:o,status:l,statusText:u}});return this.shouldThrowOnError||(r=r.catch(e=>{var t,r,i;return{error:{message:`${null!=(t=null==e?void 0:e.name)?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!=(r=null==e?void 0:e.stack)?r:""}`,hint:"",code:`${null!=(i=null==e?void 0:e.code)?i:""}`},data:null,count:null,status:0,statusText:""}})),r.then(e,t)}returns(){return this}overrideTypes(){return this}}t.default=a},280:(e,t,r)=>{var i;(()=>{var s={226:function(s,n){!function(a,o){"use strict";var l="function",u="undefined",c="object",h="string",d="major",p="model",f="name",g="type",m="vendor",b="version",v="architecture",w="console",y="mobile",_="tablet",S="smarttv",E="wearable",k="embedded",O="Amazon",T="Apple",x="ASUS",P="BlackBerry",C="Browser",R="Chrome",j="Firefox",I="Google",A="Huawei",N="Microsoft",L="Motorola",$="Opera",M="Samsung",D="Sharp",U="Sony",q="Xiaomi",B="Zebra",z="Facebook",V="Chromium OS",W="Mac OS",F=function(e,t){var r={};for(var i in e)t[i]&&t[i].length%2==0?r[i]=t[i].concat(e[i]):r[i]=e[i];return r},H=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},G=function(e,t){return typeof e===h&&-1!==K(t).indexOf(K(e))},K=function(e){return e.toLowerCase()},J=function(e,t){if(typeof e===h)return e=e.replace(/^\s\s*/,""),typeof t===u?e:e.substring(0,350)},X=function(e,t){for(var r,i,s,n,a,u,h=0;h<t.length&&!a;){var d=t[h],p=t[h+1];for(r=i=0;r<d.length&&!a&&d[r];)if(a=d[r++].exec(e))for(s=0;s<p.length;s++)u=a[++i],typeof(n=p[s])===c&&n.length>0?2===n.length?typeof n[1]==l?this[n[0]]=n[1].call(this,u):this[n[0]]=n[1]:3===n.length?typeof n[1]!==l||n[1].exec&&n[1].test?this[n[0]]=u?u.replace(n[1],n[2]):void 0:this[n[0]]=u?n[1].call(this,u,n[2]):void 0:4===n.length&&(this[n[0]]=u?n[3].call(this,u.replace(n[1],n[2])):o):this[n]=u||o;h+=2}},Y=function(e,t){for(var r in t)if(typeof t[r]===c&&t[r].length>0){for(var i=0;i<t[r].length;i++)if(G(t[r][i],e))return"?"===r?o:r}else if(G(t[r],e))return"?"===r?o:r;return e},Q={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Z={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[b,[f,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[b,[f,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[f,b],[/opios[\/ ]+([\w\.]+)/i],[b,[f,$+" Mini"]],[/\bopr\/([\w\.]+)/i],[b,[f,$]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[f,b],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[b,[f,"UC"+C]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[b,[f,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[b,[f,"WeChat"]],[/konqueror\/([\w\.]+)/i],[b,[f,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[b,[f,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[b,[f,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[f,/(.+)/,"$1 Secure "+C],b],[/\bfocus\/([\w\.]+)/i],[b,[f,j+" Focus"]],[/\bopt\/([\w\.]+)/i],[b,[f,$+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[b,[f,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[b,[f,"Dolphin"]],[/coast\/([\w\.]+)/i],[b,[f,$+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[b,[f,"MIUI "+C]],[/fxios\/([-\w\.]+)/i],[b,[f,j]],[/\bqihu|(qi?ho?o?|360)browser/i],[[f,"360 "+C]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[f,/(.+)/,"$1 "+C],b],[/(comodo_dragon)\/([\w\.]+)/i],[[f,/_/g," "],b],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[f,b],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[f],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[f,z],b],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[f,b],[/\bgsa\/([\w\.]+) .*safari\//i],[b,[f,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[b,[f,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[b,[f,R+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[f,R+" WebView"],b],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[b,[f,"Android "+C]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[f,b],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[b,[f,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[b,f],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[f,[b,Y,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[f,b],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[f,"Netscape"],b],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[b,[f,j+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[f,b],[/(cobalt)\/([\w\.]+)/i],[f,[b,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[v,"amd64"]],[/(ia32(?=;))/i],[[v,K]],[/((?:i[346]|x)86)[;\)]/i],[[v,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[v,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[v,"armhf"]],[/windows (ce|mobile); ppc;/i],[[v,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[v,/ower/,"",K]],[/(sun4\w)[;\)]/i],[[v,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[v,K]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[p,[m,M],[g,_]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[p,[m,M],[g,y]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[p,[m,T],[g,y]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[p,[m,T],[g,_]],[/(macintosh);/i],[p,[m,T]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[p,[m,D],[g,y]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[p,[m,A],[g,_]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[p,[m,A],[g,y]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[p,/_/g," "],[m,q],[g,y]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[p,/_/g," "],[m,q],[g,_]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[p,[m,"OPPO"],[g,y]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[p,[m,"Vivo"],[g,y]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[p,[m,"Realme"],[g,y]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[p,[m,L],[g,y]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[p,[m,L],[g,_]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[p,[m,"LG"],[g,_]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[p,[m,"LG"],[g,y]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[p,[m,"Lenovo"],[g,_]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[p,/_/g," "],[m,"Nokia"],[g,y]],[/(pixel c)\b/i],[p,[m,I],[g,_]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[p,[m,I],[g,y]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[p,[m,U],[g,y]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[p,"Xperia Tablet"],[m,U],[g,_]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[p,[m,"OnePlus"],[g,y]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[p,[m,O],[g,_]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[p,/(.+)/g,"Fire Phone $1"],[m,O],[g,y]],[/(playbook);[-\w\),; ]+(rim)/i],[p,m,[g,_]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[p,[m,P],[g,y]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[p,[m,x],[g,_]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[p,[m,x],[g,y]],[/(nexus 9)/i],[p,[m,"HTC"],[g,_]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[m,[p,/_/g," "],[g,y]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[p,[m,"Acer"],[g,_]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[p,[m,"Meizu"],[g,y]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[m,p,[g,y]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[m,p,[g,_]],[/(surface duo)/i],[p,[m,N],[g,_]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[p,[m,"Fairphone"],[g,y]],[/(u304aa)/i],[p,[m,"AT&T"],[g,y]],[/\bsie-(\w*)/i],[p,[m,"Siemens"],[g,y]],[/\b(rct\w+) b/i],[p,[m,"RCA"],[g,_]],[/\b(venue[\d ]{2,7}) b/i],[p,[m,"Dell"],[g,_]],[/\b(q(?:mv|ta)\w+) b/i],[p,[m,"Verizon"],[g,_]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[p,[m,"Barnes & Noble"],[g,_]],[/\b(tm\d{3}\w+) b/i],[p,[m,"NuVision"],[g,_]],[/\b(k88) b/i],[p,[m,"ZTE"],[g,_]],[/\b(nx\d{3}j) b/i],[p,[m,"ZTE"],[g,y]],[/\b(gen\d{3}) b.+49h/i],[p,[m,"Swiss"],[g,y]],[/\b(zur\d{3}) b/i],[p,[m,"Swiss"],[g,_]],[/\b((zeki)?tb.*\b) b/i],[p,[m,"Zeki"],[g,_]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[m,"Dragon Touch"],p,[g,_]],[/\b(ns-?\w{0,9}) b/i],[p,[m,"Insignia"],[g,_]],[/\b((nxa|next)-?\w{0,9}) b/i],[p,[m,"NextBook"],[g,_]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[m,"Voice"],p,[g,y]],[/\b(lvtel\-)?(v1[12]) b/i],[[m,"LvTel"],p,[g,y]],[/\b(ph-1) /i],[p,[m,"Essential"],[g,y]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[p,[m,"Envizen"],[g,_]],[/\b(trio[-\w\. ]+) b/i],[p,[m,"MachSpeed"],[g,_]],[/\btu_(1491) b/i],[p,[m,"Rotor"],[g,_]],[/(shield[\w ]+) b/i],[p,[m,"Nvidia"],[g,_]],[/(sprint) (\w+)/i],[m,p,[g,y]],[/(kin\.[onetw]{3})/i],[[p,/\./g," "],[m,N],[g,y]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[p,[m,B],[g,_]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[p,[m,B],[g,y]],[/smart-tv.+(samsung)/i],[m,[g,S]],[/hbbtv.+maple;(\d+)/i],[[p,/^/,"SmartTV"],[m,M],[g,S]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[m,"LG"],[g,S]],[/(apple) ?tv/i],[m,[p,T+" TV"],[g,S]],[/crkey/i],[[p,R+"cast"],[m,I],[g,S]],[/droid.+aft(\w)( bui|\))/i],[p,[m,O],[g,S]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[p,[m,D],[g,S]],[/(bravia[\w ]+)( bui|\))/i],[p,[m,U],[g,S]],[/(mitv-\w{5}) bui/i],[p,[m,q],[g,S]],[/Hbbtv.*(technisat) (.*);/i],[m,p,[g,S]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[m,J],[p,J],[g,S]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[g,S]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[m,p,[g,w]],[/droid.+; (shield) bui/i],[p,[m,"Nvidia"],[g,w]],[/(playstation [345portablevi]+)/i],[p,[m,U],[g,w]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[p,[m,N],[g,w]],[/((pebble))app/i],[m,p,[g,E]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[p,[m,T],[g,E]],[/droid.+; (glass) \d/i],[p,[m,I],[g,E]],[/droid.+; (wt63?0{2,3})\)/i],[p,[m,B],[g,E]],[/(quest( 2| pro)?)/i],[p,[m,z],[g,E]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[m,[g,k]],[/(aeobc)\b/i],[p,[m,O],[g,k]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[p,[g,y]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[p,[g,_]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[g,_]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[g,y]],[/(android[-\w\. ]{0,9});.+buil/i],[p,[m,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[b,[f,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[b,[f,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[f,b],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[b,f]],os:[[/microsoft (windows) (vista|xp)/i],[f,b],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[f,[b,Y,Q]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[f,"Windows"],[b,Y,Q]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[b,/_/g,"."],[f,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[f,W],[b,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[b,f],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[f,b],[/\(bb(10);/i],[b,[f,P]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[b,[f,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[b,[f,j+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[b,[f,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[b,[f,"watchOS"]],[/crkey\/([\d\.]+)/i],[b,[f,R+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[f,V],b],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[f,b],[/(sunos) ?([\w\.\d]*)/i],[[f,"Solaris"],b],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[f,b]]},ee=function(e,t){if(typeof e===c&&(t=e,e=o),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof a!==u&&a.navigator?a.navigator:o,i=e||(r&&r.userAgent?r.userAgent:""),s=r&&r.userAgentData?r.userAgentData:o,n=t?F(Z,t):Z,w=r&&r.userAgent==i;return this.getBrowser=function(){var e,t={};return t[f]=o,t[b]=o,X.call(t,i,n.browser),t[d]=typeof(e=t[b])===h?e.replace(/[^\d\.]/g,"").split(".")[0]:o,w&&r&&r.brave&&typeof r.brave.isBrave==l&&(t[f]="Brave"),t},this.getCPU=function(){var e={};return e[v]=o,X.call(e,i,n.cpu),e},this.getDevice=function(){var e={};return e[m]=o,e[p]=o,e[g]=o,X.call(e,i,n.device),w&&!e[g]&&s&&s.mobile&&(e[g]=y),w&&"Macintosh"==e[p]&&r&&typeof r.standalone!==u&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[p]="iPad",e[g]=_),e},this.getEngine=function(){var e={};return e[f]=o,e[b]=o,X.call(e,i,n.engine),e},this.getOS=function(){var e={};return e[f]=o,e[b]=o,X.call(e,i,n.os),w&&!e[f]&&s&&"Unknown"!=s.platform&&(e[f]=s.platform.replace(/chrome os/i,V).replace(/macos/i,W)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return i},this.setUA=function(e){return i=typeof e===h&&e.length>350?J(e,350):e,this},this.setUA(i),this};ee.VERSION="1.0.35",ee.BROWSER=H([f,b,d]),ee.CPU=H([v]),ee.DEVICE=H([p,m,g,w,y,S,_,E,k]),ee.ENGINE=ee.OS=H([f,b]),typeof n!==u?(s.exports&&(n=s.exports=ee),n.UAParser=ee):r.amdO?void 0===(i=(function(){return ee}).call(t,r,t,e))||(e.exports=i):typeof a!==u&&(a.UAParser=ee);var et=typeof a!==u&&(a.jQuery||a.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},n={};function a(e){var t=n[e];if(void 0!==t)return t.exports;var r=n[e]={exports:{}},i=!0;try{s[e].call(r.exports,r,r.exports,a),i=!1}finally{i&&delete n[e]}return r.exports}a.ab="//",e.exports=a(226)})()},355:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.PostgrestError=t.PostgrestBuilder=t.PostgrestTransformBuilder=t.PostgrestFilterBuilder=t.PostgrestQueryBuilder=t.PostgrestClient=void 0;let s=i(r(729));t.PostgrestClient=s.default;let n=i(r(665));t.PostgrestQueryBuilder=n.default;let a=i(r(373));t.PostgrestFilterBuilder=a.default;let o=i(r(861));t.PostgrestTransformBuilder=o.default;let l=i(r(279));t.PostgrestBuilder=l.default;let u=i(r(784));t.PostgrestError=u.default,t.default={PostgrestClient:s.default,PostgrestQueryBuilder:n.default,PostgrestFilterBuilder:a.default,PostgrestTransformBuilder:o.default,PostgrestBuilder:l.default,PostgrestError:u.default}},356:e=>{"use strict";e.exports=require("node:buffer")},373:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let s=i(r(861));class n extends s.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){let r=Array.from(new Set(t)).map(e=>"string"==typeof e&&RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${r})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:r,type:i}={}){let s="";"plain"===i?s="pl":"phrase"===i?s="ph":"websearch"===i&&(s="w");let n=void 0===r?"":`(${r})`;return this.url.searchParams.append(e,`${s}fts${n}.${t}`),this}match(e){return Object.entries(e).forEach(([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)}),this}not(e,t,r){return this.url.searchParams.append(e,`not.${t}.${r}`),this}or(e,{foreignTable:t,referencedTable:r=t}={}){let i=r?`${r}.or`:"or";return this.url.searchParams.append(i,`(${e})`),this}filter(e,t,r){return this.url.searchParams.append(e,`${t}.${r}`),this}}t.default=n},521:e=>{"use strict";e.exports=require("node:async_hooks")},552:(e,t,r)=>{"use strict";var i=r(356).Buffer;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleFetch:function(){return o},interceptFetch:function(){return l},reader:function(){return n}});let s=r(201),n={url:e=>e.url,header:(e,t)=>e.headers.get(t)};async function a(e,t){let{url:r,method:s,headers:n,body:a,cache:o,credentials:l,integrity:u,mode:c,redirect:h,referrer:d,referrerPolicy:p}=t;return{testData:e,api:"fetch",request:{url:r,method:s,headers:[...Array.from(n),["next-test-stack",function(){let e=(Error().stack??"").split("\n");for(let t=1;t<e.length;t++)if(e[t].length>0){e=e.slice(t);break}return(e=(e=(e=e.filter(e=>!e.includes("/next/dist/"))).slice(0,5)).map(e=>e.replace("webpack-internal:///(rsc)/","").trim())).join("    ")}()]],body:a?i.from(await t.arrayBuffer()).toString("base64"):null,cache:o,credentials:l,integrity:u,mode:c,redirect:h,referrer:d,referrerPolicy:p}}}async function o(e,t){let r=(0,s.getTestReqInfo)(t,n);if(!r)return e(t);let{testData:o,proxyPort:l}=r,u=await a(o,t),c=await e(`http://localhost:${l}`,{method:"POST",body:JSON.stringify(u),next:{internal:!0}});if(!c.ok)throw Object.defineProperty(Error(`Proxy request failed: ${c.status}`),"__NEXT_ERROR_CODE",{value:"E146",enumerable:!1,configurable:!0});let h=await c.json(),{api:d}=h;switch(d){case"continue":return e(t);case"abort":case"unhandled":throw Object.defineProperty(Error(`Proxy request aborted [${t.method} ${t.url}]`),"__NEXT_ERROR_CODE",{value:"E145",enumerable:!1,configurable:!0})}let{status:p,headers:f,body:g}=h.response;return new Response(g?i.from(g,"base64"):null,{status:p,headers:new Headers(f)})}function l(e){return r.g.fetch=function(t,r){var i;return(null==r||null==(i=r.next)?void 0:i.internal)?e(t,r):o(e,new Request(t,r))},()=>{r.g.fetch=e}}},554:(e,t)=>{"use strict";t.qg=function(e,t){let r=new o,i=e.length;if(i<2)return r;let s=t?.decode||c,n=0;do{let t=e.indexOf("=",n);if(-1===t)break;let a=e.indexOf(";",n),o=-1===a?i:a;if(t>o){n=e.lastIndexOf(";",t-1)+1;continue}let c=l(e,n,t),h=u(e,t,c),d=e.slice(c,h);if(void 0===r[d]){let i=l(e,t+1,o),n=u(e,o,i),a=s(e.slice(i,n));r[d]=a}n=o+1}while(n<i);return r},t.lK=function(e,t,o){let l=o?.encode||encodeURIComponent;if(!r.test(e))throw TypeError(`argument name is invalid: ${e}`);let u=l(t);if(!i.test(u))throw TypeError(`argument val is invalid: ${t}`);let c=e+"="+u;if(!o)return c;if(void 0!==o.maxAge){if(!Number.isInteger(o.maxAge))throw TypeError(`option maxAge is invalid: ${o.maxAge}`);c+="; Max-Age="+o.maxAge}if(o.domain){if(!s.test(o.domain))throw TypeError(`option domain is invalid: ${o.domain}`);c+="; Domain="+o.domain}if(o.path){if(!n.test(o.path))throw TypeError(`option path is invalid: ${o.path}`);c+="; Path="+o.path}if(o.expires){var h;if(h=o.expires,"[object Date]"!==a.call(h)||!Number.isFinite(o.expires.valueOf()))throw TypeError(`option expires is invalid: ${o.expires}`);c+="; Expires="+o.expires.toUTCString()}if(o.httpOnly&&(c+="; HttpOnly"),o.secure&&(c+="; Secure"),o.partitioned&&(c+="; Partitioned"),o.priority)switch("string"==typeof o.priority?o.priority.toLowerCase():void 0){case"low":c+="; Priority=Low";break;case"medium":c+="; Priority=Medium";break;case"high":c+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${o.priority}`)}if(o.sameSite)switch("string"==typeof o.sameSite?o.sameSite.toLowerCase():o.sameSite){case!0:case"strict":c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"none":c+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${o.sameSite}`)}return c};let r=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,i=/^[\u0021-\u003A\u003C-\u007E]*$/,s=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,n=/^[\u0020-\u003A\u003D-\u007E]*$/,a=Object.prototype.toString,o=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function l(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function u(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function c(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},665:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let s=i(r(373));class n{constructor(e,{headers:t={},schema:r,fetch:i}){this.url=e,this.headers=t,this.schema=r,this.fetch=i}select(e,{head:t=!1,count:r}={}){let i=!1,n=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!i?"":('"'===e&&(i=!i),e)).join("");return this.url.searchParams.set("select",n),r&&(this.headers.Prefer=`count=${r}`),new s.default({method:t?"HEAD":"GET",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t,defaultToNull:r=!0}={}){let i=[];if(this.headers.Prefer&&i.push(this.headers.Prefer),t&&i.push(`count=${t}`),r||i.push("missing=default"),this.headers.Prefer=i.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new s.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:r=!1,count:i,defaultToNull:n=!0}={}){let a=[`resolution=${r?"ignore":"merge"}-duplicates`];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&a.push(this.headers.Prefer),i&&a.push(`count=${i}`),n||a.push("missing=default"),this.headers.Prefer=a.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new s.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){let r=[];return this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push(`count=${t}`),this.headers.Prefer=r.join(","),new s.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){let t=[];return e&&t.push(`count=${e}`),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new s.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}t.default=n},724:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,n={};function a(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),i=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?i:`${i}; ${r.join("; ")}`}function o(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[i,s]=[r.slice(0,e),r.slice(e+1)];try{t.set(i,decodeURIComponent(null!=s?s:"true"))}catch{}}return t}function l(e){if(!e)return;let[[t,r],...i]=o(e),{domain:s,expires:n,httponly:a,maxage:l,path:h,samesite:d,secure:p,partitioned:f,priority:g}=Object.fromEntries(i.map(([e,t])=>[e.toLowerCase().replace(/-/g,""),t]));{var m,b,v={name:t,value:decodeURIComponent(r),domain:s,...n&&{expires:new Date(n)},...a&&{httpOnly:!0},..."string"==typeof l&&{maxAge:Number(l)},path:h,...d&&{sameSite:u.includes(m=(m=d).toLowerCase())?m:void 0},...p&&{secure:!0},...g&&{priority:c.includes(b=(b=g).toLowerCase())?b:void 0},...f&&{partitioned:!0}};let e={};for(let t in v)v[t]&&(e[t]=v[t]);return e}}((e,r)=>{for(var i in r)t(e,i,{get:r[i],enumerable:!0})})(n,{RequestCookies:()=>h,ResponseCookies:()=>d,parseCookie:()=>o,parseSetCookie:()=>l,stringifyCookie:()=>a}),e.exports=((e,n,a,o)=>{if(n&&"object"==typeof n||"function"==typeof n)for(let l of i(n))s.call(e,l)||l===a||t(e,l,{get:()=>n[l],enumerable:!(o=r(n,l))||o.enumerable});return e})(t({},"__esModule",{value:!0}),n);var u=["strict","lax","none"],c=["low","medium","high"],h=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of o(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let i="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===i).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,i=this._parsed;return i.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(i).map(([e,t])=>a(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>a(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},d=class{constructor(e){var t,r,i;this._parsed=new Map,this._headers=e;let s=null!=(i=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?i:[];for(let e of Array.isArray(s)?s:function(e){if(!e)return[];var t,r,i,s,n,a=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,n=!1;l();)if(","===(r=e.charAt(o))){for(i=o,o+=1,l(),s=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(n=!0,o=s,a.push(e.substring(t,i)),t=o):o=i+1}else o+=1;(!n||o>=e.length)&&a.push(e.substring(t,e.length))}return a}(s)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let i="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===i)}has(e){return this._parsed.has(e)}set(...e){let[t,r,i]=1===e.length?[e[0].name,e[0].value,e[0]]:e,s=this._parsed;return s.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...i})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=a(r);t.append("set-cookie",e)}}(s,this._headers),this}delete(...e){let[t,r]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0]];return this.set({...r,name:t,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a).join("; ")}}},729:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let s=i(r(665)),n=i(r(373)),a=r(128);class o{constructor(e,{headers:t={},schema:r,fetch:i}={}){this.url=e,this.headers=Object.assign(Object.assign({},a.DEFAULT_HEADERS),t),this.schemaName=r,this.fetch=i}from(e){let t=new URL(`${this.url}/${e}`);return new s.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(e){return new o(this.url,{headers:this.headers,schema:e,fetch:this.fetch})}rpc(e,t={},{head:r=!1,get:i=!1,count:s}={}){let a,o,l=new URL(`${this.url}/rpc/${e}`);r||i?(a=r?"HEAD":"GET",Object.entries(t).filter(([e,t])=>void 0!==t).map(([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`]).forEach(([e,t])=>{l.searchParams.append(e,t)})):(a="POST",o=t);let u=Object.assign({},this.headers);return s&&(u.Prefer=`count=${s}`),new n.default({method:a,url:l,headers:u,schema:this.schemaName,body:o,fetch:this.fetch,allowEmpty:!1})}}t.default=o},784:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});class r extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}}t.default=r},802:e=>{(()=>{"use strict";var t={993:e=>{var t=Object.prototype.hasOwnProperty,r="~";function i(){}function s(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function n(e,t,i,n,a){if("function"!=typeof i)throw TypeError("The listener must be a function");var o=new s(i,n||e,a),l=r?r+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],o]:e._events[l].push(o):(e._events[l]=o,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new i:delete e._events[t]}function o(){this._events=new i,this._eventsCount=0}Object.create&&(i.prototype=Object.create(null),(new i).__proto__||(r=!1)),o.prototype.eventNames=function(){var e,i,s=[];if(0===this._eventsCount)return s;for(i in e=this._events)t.call(e,i)&&s.push(r?i.slice(1):i);return Object.getOwnPropertySymbols?s.concat(Object.getOwnPropertySymbols(e)):s},o.prototype.listeners=function(e){var t=r?r+e:e,i=this._events[t];if(!i)return[];if(i.fn)return[i.fn];for(var s=0,n=i.length,a=Array(n);s<n;s++)a[s]=i[s].fn;return a},o.prototype.listenerCount=function(e){var t=r?r+e:e,i=this._events[t];return i?i.fn?1:i.length:0},o.prototype.emit=function(e,t,i,s,n,a){var o=r?r+e:e;if(!this._events[o])return!1;var l,u,c=this._events[o],h=arguments.length;if(c.fn){switch(c.once&&this.removeListener(e,c.fn,void 0,!0),h){case 1:return c.fn.call(c.context),!0;case 2:return c.fn.call(c.context,t),!0;case 3:return c.fn.call(c.context,t,i),!0;case 4:return c.fn.call(c.context,t,i,s),!0;case 5:return c.fn.call(c.context,t,i,s,n),!0;case 6:return c.fn.call(c.context,t,i,s,n,a),!0}for(u=1,l=Array(h-1);u<h;u++)l[u-1]=arguments[u];c.fn.apply(c.context,l)}else{var d,p=c.length;for(u=0;u<p;u++)switch(c[u].once&&this.removeListener(e,c[u].fn,void 0,!0),h){case 1:c[u].fn.call(c[u].context);break;case 2:c[u].fn.call(c[u].context,t);break;case 3:c[u].fn.call(c[u].context,t,i);break;case 4:c[u].fn.call(c[u].context,t,i,s);break;default:if(!l)for(d=1,l=Array(h-1);d<h;d++)l[d-1]=arguments[d];c[u].fn.apply(c[u].context,l)}}return!0},o.prototype.on=function(e,t,r){return n(this,e,t,r,!1)},o.prototype.once=function(e,t,r){return n(this,e,t,r,!0)},o.prototype.removeListener=function(e,t,i,s){var n=r?r+e:e;if(!this._events[n])return this;if(!t)return a(this,n),this;var o=this._events[n];if(o.fn)o.fn!==t||s&&!o.once||i&&o.context!==i||a(this,n);else{for(var l=0,u=[],c=o.length;l<c;l++)(o[l].fn!==t||s&&!o[l].once||i&&o[l].context!==i)&&u.push(o[l]);u.length?this._events[n]=1===u.length?u[0]:u:a(this,n)}return this},o.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&a(this,t)):(this._events=new i,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=r,o.EventEmitter=o,e.exports=o},213:e=>{e.exports=(e,t)=>(t=t||(()=>{}),e.then(e=>new Promise(e=>{e(t())}).then(()=>e),e=>new Promise(e=>{e(t())}).then(()=>{throw e})))},574:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){let i=0,s=e.length;for(;s>0;){let n=s/2|0,a=i+n;0>=r(e[a],t)?(i=++a,s-=n+1):s=n}return i}},821:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});let i=r(574);class s{constructor(){this._queue=[]}enqueue(e,t){let r={priority:(t=Object.assign({priority:0},t)).priority,run:e};if(this.size&&this._queue[this.size-1].priority>=t.priority)return void this._queue.push(r);let s=i.default(this._queue,r,(e,t)=>t.priority-e.priority);this._queue.splice(s,0,r)}dequeue(){let e=this._queue.shift();return null==e?void 0:e.run}filter(e){return this._queue.filter(t=>t.priority===e.priority).map(e=>e.run)}get size(){return this._queue.length}}t.default=s},816:(e,t,r)=>{let i=r(213);class s extends Error{constructor(e){super(e),this.name="TimeoutError"}}let n=(e,t,r)=>new Promise((n,a)=>{if("number"!=typeof t||t<0)throw TypeError("Expected `milliseconds` to be a positive number");if(t===1/0)return void n(e);let o=setTimeout(()=>{if("function"==typeof r){try{n(r())}catch(e){a(e)}return}let i="string"==typeof r?r:`Promise timed out after ${t} milliseconds`,o=r instanceof Error?r:new s(i);"function"==typeof e.cancel&&e.cancel(),a(o)},t);i(e.then(n,a),()=>{clearTimeout(o)})});e.exports=n,e.exports.default=n,e.exports.TimeoutError=s}},r={};function i(e){var s=r[e];if(void 0!==s)return s.exports;var n=r[e]={exports:{}},a=!0;try{t[e](n,n.exports,i),a=!1}finally{a&&delete r[e]}return n.exports}i.ab="//";var s={};(()=>{Object.defineProperty(s,"__esModule",{value:!0});let e=i(993),t=i(816),r=i(821),n=()=>{},a=new t.TimeoutError;class o extends e{constructor(e){var t,i,s,a;if(super(),this._intervalCount=0,this._intervalEnd=0,this._pendingCount=0,this._resolveEmpty=n,this._resolveIdle=n,!("number"==typeof(e=Object.assign({carryoverConcurrencyCount:!1,intervalCap:1/0,interval:0,concurrency:1/0,autoStart:!0,queueClass:r.default},e)).intervalCap&&e.intervalCap>=1))throw TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${null!=(i=null==(t=e.intervalCap)?void 0:t.toString())?i:""}\` (${typeof e.intervalCap})`);if(void 0===e.interval||!(Number.isFinite(e.interval)&&e.interval>=0))throw TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${null!=(a=null==(s=e.interval)?void 0:s.toString())?a:""}\` (${typeof e.interval})`);this._carryoverConcurrencyCount=e.carryoverConcurrencyCount,this._isIntervalIgnored=e.intervalCap===1/0||0===e.interval,this._intervalCap=e.intervalCap,this._interval=e.interval,this._queue=new e.queueClass,this._queueClass=e.queueClass,this.concurrency=e.concurrency,this._timeout=e.timeout,this._throwOnTimeout=!0===e.throwOnTimeout,this._isPaused=!1===e.autoStart}get _doesIntervalAllowAnother(){return this._isIntervalIgnored||this._intervalCount<this._intervalCap}get _doesConcurrentAllowAnother(){return this._pendingCount<this._concurrency}_next(){this._pendingCount--,this._tryToStartAnother(),this.emit("next")}_resolvePromises(){this._resolveEmpty(),this._resolveEmpty=n,0===this._pendingCount&&(this._resolveIdle(),this._resolveIdle=n,this.emit("idle"))}_onResumeInterval(){this._onInterval(),this._initializeIntervalIfNeeded(),this._timeoutId=void 0}_isIntervalPaused(){let e=Date.now();if(void 0===this._intervalId){let t=this._intervalEnd-e;if(!(t<0))return void 0===this._timeoutId&&(this._timeoutId=setTimeout(()=>{this._onResumeInterval()},t)),!0;this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0}return!1}_tryToStartAnother(){if(0===this._queue.size)return this._intervalId&&clearInterval(this._intervalId),this._intervalId=void 0,this._resolvePromises(),!1;if(!this._isPaused){let e=!this._isIntervalPaused();if(this._doesIntervalAllowAnother&&this._doesConcurrentAllowAnother){let t=this._queue.dequeue();return!!t&&(this.emit("active"),t(),e&&this._initializeIntervalIfNeeded(),!0)}}return!1}_initializeIntervalIfNeeded(){this._isIntervalIgnored||void 0!==this._intervalId||(this._intervalId=setInterval(()=>{this._onInterval()},this._interval),this._intervalEnd=Date.now()+this._interval)}_onInterval(){0===this._intervalCount&&0===this._pendingCount&&this._intervalId&&(clearInterval(this._intervalId),this._intervalId=void 0),this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0,this._processQueue()}_processQueue(){for(;this._tryToStartAnother(););}get concurrency(){return this._concurrency}set concurrency(e){if(!("number"==typeof e&&e>=1))throw TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${e}\` (${typeof e})`);this._concurrency=e,this._processQueue()}async add(e,r={}){return new Promise((i,s)=>{let n=async()=>{this._pendingCount++,this._intervalCount++;try{let n=void 0===this._timeout&&void 0===r.timeout?e():t.default(Promise.resolve(e()),void 0===r.timeout?this._timeout:r.timeout,()=>{(void 0===r.throwOnTimeout?this._throwOnTimeout:r.throwOnTimeout)&&s(a)});i(await n)}catch(e){s(e)}this._next()};this._queue.enqueue(n,r),this._tryToStartAnother(),this.emit("add")})}async addAll(e,t){return Promise.all(e.map(async e=>this.add(e,t)))}start(){return this._isPaused&&(this._isPaused=!1,this._processQueue()),this}pause(){this._isPaused=!0}clear(){this._queue=new this._queueClass}async onEmpty(){if(0!==this._queue.size)return new Promise(e=>{let t=this._resolveEmpty;this._resolveEmpty=()=>{t(),e()}})}async onIdle(){if(0!==this._pendingCount||0!==this._queue.size)return new Promise(e=>{let t=this._resolveIdle;this._resolveIdle=()=>{t(),e()}})}get size(){return this._queue.size}sizeBy(e){return this._queue.filter(e).length}get pending(){return this._pendingCount}get isPaused(){return this._isPaused}get timeout(){return this._timeout}set timeout(e){this._timeout=e}}s.default=o})(),e.exports=s})()},815:(e,t,r)=>{"use strict";let i,s;r.r(t),r.d(t,{default:()=>iY});var n,a,o,l,u,c,h,d,p,f,g,m={};async function b(){return"_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&await _ENTRIES.middleware_instrumentation}r.r(m),r.d(m,{config:()=>iG,middleware:()=>iH});let v=null;async function w(){if("phase-production-build"===process.env.NEXT_PHASE)return;v||(v=b());let e=await v;if(null==e?void 0:e.register)try{await e.register()}catch(e){throw e.message=`An error occurred while loading instrumentation hook: ${e.message}`,e}}async function y(...e){let t=await b();try{var r;await (null==t||null==(r=t.onRequestError)?void 0:r.call(t,...e))}catch(e){console.error("Error in instrumentation.onRequestError:",e)}}let _=null;function S(){return _||(_=w()),_}function E(e){return`The edge runtime does not support Node.js '${e}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}process!==r.g.process&&(process.env=r.g.process.env,r.g.process=process),Object.defineProperty(globalThis,"__import_unsupported",{value:function(e){let t=new Proxy(function(){},{get(t,r){if("then"===r)return{};throw Object.defineProperty(Error(E(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},construct(){throw Object.defineProperty(Error(E(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},apply(r,i,s){if("function"==typeof s[0])return s[0](t);throw Object.defineProperty(Error(E(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}});return new Proxy({},{get:()=>t})},enumerable:!1,configurable:!1}),S();class k extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class O extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class T extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}let x="_N_T_",P={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"};function C(e){var t,r,i,s,n,a=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,n=!1;l();)if(","===(r=e.charAt(o))){for(i=o,o+=1,l(),s=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(n=!0,o=s,a.push(e.substring(t,i)),t=o):o=i+1}else o+=1;(!n||o>=e.length)&&a.push(e.substring(t,e.length))}return a}function R(e){let t={},r=[];if(e)for(let[i,s]of e.entries())"set-cookie"===i.toLowerCase()?(r.push(...C(s)),t[i]=1===r.length?r[0]:r):t[i]=s;return t}function j(e){try{return String(new URL(String(e)))}catch(t){throw Object.defineProperty(Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}({...P,GROUP:{builtinReact:[P.reactServerComponents,P.actionBrowser],serverOnly:[P.reactServerComponents,P.actionBrowser,P.instrument,P.middleware],neutralTarget:[P.apiNode,P.apiEdge],clientOnly:[P.serverSideRendering,P.appPagesBrowser],bundled:[P.reactServerComponents,P.actionBrowser,P.serverSideRendering,P.appPagesBrowser,P.shared,P.instrument,P.middleware],appPages:[P.reactServerComponents,P.serverSideRendering,P.appPagesBrowser,P.actionBrowser]}});let I=Symbol("response"),A=Symbol("passThrough"),N=Symbol("waitUntil");class L{constructor(e,t){this[A]=!1,this[N]=t?{kind:"external",function:t}:{kind:"internal",promises:[]}}respondWith(e){this[I]||(this[I]=Promise.resolve(e))}passThroughOnException(){this[A]=!0}waitUntil(e){if("external"===this[N].kind)return(0,this[N].function)(e);this[N].promises.push(e)}}class $ extends L{constructor(e){var t;super(e.request,null==(t=e.context)?void 0:t.waitUntil),this.sourcePage=e.page}get request(){throw Object.defineProperty(new k({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new k({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}function M(e){return e.replace(/\/$/,"")||"/"}function D(e){let t=e.indexOf("#"),r=e.indexOf("?"),i=r>-1&&(t<0||r<t);return i||t>-1?{pathname:e.substring(0,i?r:t),query:i?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function U(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:s}=D(e);return""+t+r+i+s}function q(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:s}=D(e);return""+r+t+i+s}function B(e,t){if("string"!=typeof e)return!1;let{pathname:r}=D(e);return r===t||r.startsWith(t+"/")}let z=new WeakMap;function V(e,t){let r;if(!t)return{pathname:e};let i=z.get(t);i||(i=t.map(e=>e.toLowerCase()),z.set(t,i));let s=e.split("/",2);if(!s[1])return{pathname:e};let n=s[1].toLowerCase(),a=i.indexOf(n);return a<0?{pathname:e}:(r=t[a],{pathname:e=e.slice(r.length+1)||"/",detectedLocale:r})}let W=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function F(e,t){return new URL(String(e).replace(W,"localhost"),t&&String(t).replace(W,"localhost"))}let H=Symbol("NextURLInternal");class G{constructor(e,t,r){let i,s;"object"==typeof t&&"pathname"in t||"string"==typeof t?(i=t,s=r||{}):s=r||t||{},this[H]={url:F(e,i??s.base),options:s,basePath:""},this.analyze()}analyze(){var e,t,r,i,s;let n=function(e,t){var r,i;let{basePath:s,i18n:n,trailingSlash:a}=null!=(r=t.nextConfig)?r:{},o={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):a};s&&B(o.pathname,s)&&(o.pathname=function(e,t){if(!B(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(o.pathname,s),o.basePath=s);let l=o.pathname;if(o.pathname.startsWith("/_next/data/")&&o.pathname.endsWith(".json")){let e=o.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");o.buildId=e[0],l="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(o.pathname=l)}if(n){let e=t.i18nProvider?t.i18nProvider.analyze(o.pathname):V(o.pathname,n.locales);o.locale=e.detectedLocale,o.pathname=null!=(i=e.pathname)?i:o.pathname,!e.detectedLocale&&o.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(l):V(l,n.locales)).detectedLocale&&(o.locale=e.detectedLocale)}return o}(this[H].url.pathname,{nextConfig:this[H].options.nextConfig,parseData:!0,i18nProvider:this[H].options.i18nProvider}),a=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[H].url,this[H].options.headers);this[H].domainLocale=this[H].options.i18nProvider?this[H].options.i18nProvider.detectDomainLocale(a):function(e,t,r){if(e)for(let n of(r&&(r=r.toLowerCase()),e)){var i,s;if(t===(null==(i=n.domain)?void 0:i.split(":",1)[0].toLowerCase())||r===n.defaultLocale.toLowerCase()||(null==(s=n.locales)?void 0:s.some(e=>e.toLowerCase()===r)))return n}}(null==(t=this[H].options.nextConfig)||null==(e=t.i18n)?void 0:e.domains,a);let o=(null==(r=this[H].domainLocale)?void 0:r.defaultLocale)||(null==(s=this[H].options.nextConfig)||null==(i=s.i18n)?void 0:i.defaultLocale);this[H].url.pathname=n.pathname,this[H].defaultLocale=o,this[H].basePath=n.basePath??"",this[H].buildId=n.buildId,this[H].locale=n.locale??o,this[H].trailingSlash=n.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,r,i){if(!t||t===r)return e;let s=e.toLowerCase();return!i&&(B(s,"/api")||B(s,"/"+t.toLowerCase()))?e:U(e,"/"+t)}((e={basePath:this[H].basePath,buildId:this[H].buildId,defaultLocale:this[H].options.forceLocale?void 0:this[H].defaultLocale,locale:this[H].locale,pathname:this[H].url.pathname,trailingSlash:this[H].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=M(t)),e.buildId&&(t=q(U(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=U(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:q(t,"/"):M(t)}formatSearch(){return this[H].url.search}get buildId(){return this[H].buildId}set buildId(e){this[H].buildId=e}get locale(){return this[H].locale??""}set locale(e){var t,r;if(!this[H].locale||!(null==(r=this[H].options.nextConfig)||null==(t=r.i18n)?void 0:t.locales.includes(e)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${e}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[H].locale=e}get defaultLocale(){return this[H].defaultLocale}get domainLocale(){return this[H].domainLocale}get searchParams(){return this[H].url.searchParams}get host(){return this[H].url.host}set host(e){this[H].url.host=e}get hostname(){return this[H].url.hostname}set hostname(e){this[H].url.hostname=e}get port(){return this[H].url.port}set port(e){this[H].url.port=e}get protocol(){return this[H].url.protocol}set protocol(e){this[H].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[H].url=F(e),this.analyze()}get origin(){return this[H].url.origin}get pathname(){return this[H].url.pathname}set pathname(e){this[H].url.pathname=e}get hash(){return this[H].url.hash}set hash(e){this[H].url.hash=e}get search(){return this[H].url.search}set search(e){this[H].url.search=e}get password(){return this[H].url.password}set password(e){this[H].url.password=e}get username(){return this[H].url.username}set username(e){this[H].url.username=e}get basePath(){return this[H].basePath}set basePath(e){this[H].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new G(String(this),this[H].options)}}var K=r(724);let J=Symbol("internal request");class X extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);j(r),e instanceof Request?super(e,t):super(r,t);let i=new G(r,{headers:R(this.headers),nextConfig:t.nextConfig});this[J]={cookies:new K.RequestCookies(this.headers),nextUrl:i,url:i.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[J].cookies}get nextUrl(){return this[J].nextUrl}get page(){throw new O}get ua(){throw new T}get url(){return this[J].url}}class Y{static get(e,t,r){let i=Reflect.get(e,t,r);return"function"==typeof i?i.bind(e):i}static set(e,t,r,i){return Reflect.set(e,t,r,i)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}let Q=Symbol("internal response"),Z=new Set([301,302,303,307,308]);function ee(e,t){var r;if(null==e||null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let r=[];for(let[i,s]of e.request.headers)t.set("x-middleware-request-"+i,s),r.push(i);t.set("x-middleware-override-headers",r.join(","))}}class et extends Response{constructor(e,t={}){super(e,t);let r=this.headers,i=new Proxy(new K.ResponseCookies(r),{get(e,i,s){switch(i){case"delete":case"set":return(...s)=>{let n=Reflect.apply(e[i],e,s),a=new Headers(r);return n instanceof K.ResponseCookies&&r.set("x-middleware-set-cookie",n.getAll().map(e=>(0,K.stringifyCookie)(e)).join(",")),ee(t,a),n};default:return Y.get(e,i,s)}}});this[Q]={cookies:i,url:t.url?new G(t.url,{headers:R(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[Q].cookies}static json(e,t){let r=Response.json(e,t);return new et(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!Z.has(r))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let i="object"==typeof t?t:{},s=new Headers(null==i?void 0:i.headers);return s.set("Location",j(e)),new et(null,{...i,headers:s,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",j(e)),ee(t,r),new et(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),ee(e,t),new et(null,{...e,headers:t})}}function er(e,t){let r="string"==typeof t?new URL(t):t,i=new URL(e,t),s=i.origin===r.origin;return{url:s?i.toString().slice(r.origin.length):i.toString(),isRelative:s}}let ei="Next-Router-Prefetch",es=["RSC","Next-Router-State-Tree",ei,"Next-HMR-Refresh","Next-Router-Segment-Prefetch"],en="_rsc";class ea extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new ea}}class eo extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,i){if("symbol"==typeof r)return Y.get(t,r,i);let s=r.toLowerCase(),n=Object.keys(e).find(e=>e.toLowerCase()===s);if(void 0!==n)return Y.get(t,n,i)},set(t,r,i,s){if("symbol"==typeof r)return Y.set(t,r,i,s);let n=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===n);return Y.set(t,a??r,i,s)},has(t,r){if("symbol"==typeof r)return Y.has(t,r);let i=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0!==s&&Y.has(t,s)},deleteProperty(t,r){if("symbol"==typeof r)return Y.deleteProperty(t,r);let i=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0===s||Y.deleteProperty(t,s)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return ea.callable;default:return Y.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new eo(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,i]of this.entries())e.call(t,i,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}let el=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class eu{disable(){throw el}getStore(){}run(){throw el}exit(){throw el}enterWith(){throw el}static bind(e){return e}}let ec="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function eh(){return ec?new ec:new eu}let ed=eh(),ep=eh();class ef extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new ef}}class eg{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return ef.callable;default:return Y.get(e,t,r)}}})}}let em=Symbol.for("next.mutated.cookies");class eb{static wrap(e,t){let r=new K.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let i=[],s=new Set,n=()=>{let e=ed.getStore();if(e&&(e.pathWasRevalidated=!0),i=r.getAll().filter(e=>s.has(e.name)),t){let e=[];for(let t of i){let r=new K.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},a=new Proxy(r,{get(e,t,r){switch(t){case em:return i;case"delete":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),a}finally{n()}};case"set":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),a}finally{n()}};default:return Y.get(e,t,r)}}});return a}}function ev(e){if("action"!==function(e){let t=ep.getStore();switch(!t&&function(e){throw Object.defineProperty(Error(`\`${e}\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`),"__NEXT_ERROR_CODE",{value:"E251",enumerable:!1,configurable:!0})}(e),t.type){case"request":default:return t;case"prerender":case"prerender-ppr":case"prerender-legacy":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside a prerender. This is a bug in Next.js.`),"__NEXT_ERROR_CODE",{value:"E401",enumerable:!1,configurable:!0});case"cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside "use cache". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E37",enumerable:!1,configurable:!0});case"unstable-cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E69",enumerable:!1,configurable:!0})}}(e).phase)throw new ef}var ew=function(e){return e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404",e}(ew||{}),ey=function(e){return e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents",e}(ey||{}),e_=function(e){return e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer",e}(e_||{}),eS=function(e){return e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch",e}(eS||{}),eE=function(e){return e.startServer="startServer.startServer",e}(eE||{}),ek=function(e){return e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult",e}(ek||{}),eO=function(e){return e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch",e}(eO||{}),eT=function(e){return e.executeRoute="Router.executeRoute",e}(eT||{}),ex=function(e){return e.runHandler="Node.runHandler",e}(ex||{}),eP=function(e){return e.runHandler="AppRouteRouteHandlers.runHandler",e}(eP||{}),eC=function(e){return e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport",e}(eC||{}),eR=function(e){return e.execute="Middleware.execute",e}(eR||{});let ej=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],eI=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"];function eA(e){return null!==e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}let{context:eN,propagation:eL,trace:e$,SpanStatusCode:eM,SpanKind:eD,ROOT_CONTEXT:eU}=i=r(956);class eq extends Error{constructor(e,t){super(),this.bubble=e,this.result=t}}let eB=(e,t)=>{(function(e){return"object"==typeof e&&null!==e&&e instanceof eq})(t)&&t.bubble?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:eM.ERROR,message:null==t?void 0:t.message})),e.end()},ez=new Map,eV=i.createContextKey("next.rootSpanId"),eW=0,eF=()=>eW++,eH={set(e,t,r){e.push({key:t,value:r})}};class eG{getTracerInstance(){return e$.getTracer("next.js","0.0.1")}getContext(){return eN}getTracePropagationData(){let e=eN.active(),t=[];return eL.inject(e,t,eH),t}getActiveScopeSpan(){return e$.getSpan(null==eN?void 0:eN.active())}withPropagatedContext(e,t,r){let i=eN.active();if(e$.getSpanContext(i))return t();let s=eL.extract(i,e,r);return eN.with(s,t)}trace(...e){var t;let[r,i,s]=e,{fn:n,options:a}="function"==typeof i?{fn:i,options:{}}:{fn:s,options:{...i}},o=a.spanName??r;if(!ej.includes(r)&&"1"!==process.env.NEXT_OTEL_VERBOSE||a.hideSpan)return n();let l=this.getSpanContext((null==a?void 0:a.parentSpan)??this.getActiveScopeSpan()),u=!1;l?(null==(t=e$.getSpanContext(l))?void 0:t.isRemote)&&(u=!0):(l=(null==eN?void 0:eN.active())??eU,u=!0);let c=eF();return a.attributes={"next.span_name":o,"next.span_type":r,...a.attributes},eN.with(l.setValue(eV,c),()=>this.getTracerInstance().startActiveSpan(o,a,e=>{let t="performance"in globalThis&&"measure"in performance?globalThis.performance.now():void 0,i=()=>{ez.delete(c),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&eI.includes(r||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(r.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};u&&ez.set(c,new Map(Object.entries(a.attributes??{})));try{if(n.length>1)return n(e,t=>eB(e,t));let t=n(e);if(eA(t))return t.then(t=>(e.end(),t)).catch(t=>{throw eB(e,t),t}).finally(i);return e.end(),i(),t}catch(t){throw eB(e,t),i(),t}}))}wrap(...e){let t=this,[r,i,s]=3===e.length?e:[e[0],{},e[1]];return ej.includes(r)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=i;"function"==typeof e&&"function"==typeof s&&(e=e.apply(this,arguments));let n=arguments.length-1,a=arguments[n];if("function"!=typeof a)return t.trace(r,e,()=>s.apply(this,arguments));{let i=t.getContext().bind(eN.active(),a);return t.trace(r,e,(e,t)=>(arguments[n]=function(e){return null==t||t(e),i.apply(this,arguments)},s.apply(this,arguments)))}}:s}startSpan(...e){let[t,r]=e,i=this.getSpanContext((null==r?void 0:r.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,r,i)}getSpanContext(e){return e?e$.setSpan(eN.active(),e):void 0}getRootSpanAttributes(){let e=eN.active().getValue(eV);return ez.get(e)}setRootSpanAttribute(e,t){let r=eN.active().getValue(eV),i=ez.get(r);i&&i.set(e,t)}}let eK=(()=>{let e=new eG;return()=>e})(),eJ="__prerender_bypass";Symbol("__next_preview_data"),Symbol(eJ);class eX{constructor(e,t,r,i){var s;let n=e&&function(e,t){let r=eo.from(e.headers);return{isOnDemandRevalidate:r.get("x-prerender-revalidate")===t.previewModeId,revalidateOnlyGenerated:r.has("x-prerender-revalidate-if-generated")}}(t,e).isOnDemandRevalidate,a=null==(s=r.get(eJ))?void 0:s.value;this._isEnabled=!!(!n&&a&&e&&a===e.previewModeId),this._previewModeId=null==e?void 0:e.previewModeId,this._mutableCookies=i}get isEnabled(){return this._isEnabled}enable(){if(!this._previewModeId)throw Object.defineProperty(Error("Invariant: previewProps missing previewModeId this should never happen"),"__NEXT_ERROR_CODE",{value:"E93",enumerable:!1,configurable:!0});this._mutableCookies.set({name:eJ,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"}),this._isEnabled=!0}disable(){this._mutableCookies.set({name:eJ,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)}),this._isEnabled=!1}}function eY(e,t){if("x-middleware-set-cookie"in e.headers&&"string"==typeof e.headers["x-middleware-set-cookie"]){let r=e.headers["x-middleware-set-cookie"],i=new Headers;for(let e of C(r))i.append("set-cookie",e);for(let e of new K.ResponseCookies(i).getAll())t.set(e)}}var eQ=r(802),eZ=r.n(eQ);class e0 extends Error{constructor(e,t){super("Invariant: "+(e.endsWith(".")?e:e+".")+" This is a bug in Next.js.",t),this.name="InvariantError"}}class e1{constructor(e,t){this.cache=new Map,this.sizes=new Map,this.totalSize=0,this.maxSize=e,this.calculateSize=t||(()=>1)}set(e,t){if(!e||!t)return;let r=this.calculateSize(t);if(r>this.maxSize)return void console.warn("Single item size exceeds maxSize");this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0),this.cache.set(e,t),this.sizes.set(e,r),this.totalSize+=r,this.touch(e)}has(e){return!!e&&(this.touch(e),!!this.cache.get(e))}get(e){if(!e)return;let t=this.cache.get(e);if(void 0!==t)return this.touch(e),t}touch(e){let t=this.cache.get(e);void 0!==t&&(this.cache.delete(e),this.cache.set(e,t),this.evictIfNecessary())}evictIfNecessary(){for(;this.totalSize>this.maxSize&&this.cache.size>0;)this.evictLeastRecentlyUsed()}evictLeastRecentlyUsed(){let e=this.cache.keys().next().value;if(void 0!==e){let t=this.sizes.get(e)||0;this.totalSize-=t,this.cache.delete(e),this.sizes.delete(e)}}reset(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}keys(){return[...this.cache.keys()]}remove(e){this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0,this.cache.delete(e),this.sizes.delete(e))}clear(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}get size(){return this.cache.size}get currentSize(){return this.totalSize}}r(356).Buffer,new e1(0x3200000,e=>e.size),process.env.NEXT_PRIVATE_DEBUG_CACHE&&console.debug.bind(console,"DefaultCacheHandler:"),process.env.NEXT_PRIVATE_DEBUG_CACHE,Symbol.for("@next/cache-handlers");let e2=Symbol.for("@next/cache-handlers-map"),e3=Symbol.for("@next/cache-handlers-set"),e6=globalThis;function e4(){if(e6[e2])return e6[e2].entries()}async function e5(e,t){if(!e)return t();let r=e9(e);try{return await t()}finally{let t=function(e,t){let r=new Set(e.pendingRevalidatedTags),i=new Set(e.pendingRevalidateWrites);return{pendingRevalidatedTags:t.pendingRevalidatedTags.filter(e=>!r.has(e)),pendingRevalidates:Object.fromEntries(Object.entries(t.pendingRevalidates).filter(([t])=>!(t in e.pendingRevalidates))),pendingRevalidateWrites:t.pendingRevalidateWrites.filter(e=>!i.has(e))}}(r,e9(e));await e7(e,t)}}function e9(e){return{pendingRevalidatedTags:e.pendingRevalidatedTags?[...e.pendingRevalidatedTags]:[],pendingRevalidates:{...e.pendingRevalidates},pendingRevalidateWrites:e.pendingRevalidateWrites?[...e.pendingRevalidateWrites]:[]}}async function e8(e,t){if(0===e.length)return;let r=[];t&&r.push(t.revalidateTag(e));let i=function(){if(e6[e3])return e6[e3].values()}();if(i)for(let t of i)r.push(t.expireTags(...e));await Promise.all(r)}async function e7(e,t){let r=(null==t?void 0:t.pendingRevalidatedTags)??e.pendingRevalidatedTags??[],i=(null==t?void 0:t.pendingRevalidates)??e.pendingRevalidates??{},s=(null==t?void 0:t.pendingRevalidateWrites)??e.pendingRevalidateWrites??[];return Promise.all([e8(r,e.incrementalCache),...Object.values(i),...s])}let te=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class tt{disable(){throw te}getStore(){}run(){throw te}exit(){throw te}enterWith(){throw te}static bind(e){return e}}let tr="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage,ti=tr?new tr:new tt;class ts{constructor({waitUntil:e,onClose:t,onTaskError:r}){this.workUnitStores=new Set,this.waitUntil=e,this.onClose=t,this.onTaskError=r,this.callbackQueue=new(eZ()),this.callbackQueue.pause()}after(e){if(eA(e))this.waitUntil||tn(),this.waitUntil(e.catch(e=>this.reportTaskError("promise",e)));else if("function"==typeof e)this.addCallback(e);else throw Object.defineProperty(Error("`after()`: Argument must be a promise or a function"),"__NEXT_ERROR_CODE",{value:"E50",enumerable:!1,configurable:!0})}addCallback(e){var t;this.waitUntil||tn();let r=ep.getStore();r&&this.workUnitStores.add(r);let i=ti.getStore(),s=i?i.rootTaskSpawnPhase:null==r?void 0:r.phase;this.runCallbacksOnClosePromise||(this.runCallbacksOnClosePromise=this.runCallbacksOnClose(),this.waitUntil(this.runCallbacksOnClosePromise));let n=(t=async()=>{try{await ti.run({rootTaskSpawnPhase:s},()=>e())}catch(e){this.reportTaskError("function",e)}},tr?tr.bind(t):tt.bind(t));this.callbackQueue.add(n)}async runCallbacksOnClose(){return await new Promise(e=>this.onClose(e)),this.runCallbacks()}async runCallbacks(){if(0===this.callbackQueue.size)return;for(let e of this.workUnitStores)e.phase="after";let e=ed.getStore();if(!e)throw Object.defineProperty(new e0("Missing workStore in AfterContext.runCallbacks"),"__NEXT_ERROR_CODE",{value:"E547",enumerable:!1,configurable:!0});return e5(e,()=>(this.callbackQueue.start(),this.callbackQueue.onIdle()))}reportTaskError(e,t){if(console.error("promise"===e?"A promise passed to `after()` rejected:":"An error occurred in a function passed to `after()`:",t),this.onTaskError)try{null==this.onTaskError||this.onTaskError.call(this,t)}catch(e){console.error(Object.defineProperty(new e0("`onTaskError` threw while handling an error thrown from an `after` task",{cause:e}),"__NEXT_ERROR_CODE",{value:"E569",enumerable:!1,configurable:!0}))}}}function tn(){throw Object.defineProperty(Error("`after()` will not work correctly, because `waitUntil` is not available in the current environment."),"__NEXT_ERROR_CODE",{value:"E91",enumerable:!1,configurable:!0})}function ta(e){let t,r={then:(i,s)=>(t||(t=e()),t.then(e=>{r.value=e}).catch(()=>{}),t.then(i,s))};return r}class to{onClose(e){if(this.isClosed)throw Object.defineProperty(Error("Cannot subscribe to a closed CloseController"),"__NEXT_ERROR_CODE",{value:"E365",enumerable:!1,configurable:!0});this.target.addEventListener("close",e),this.listeners++}dispatchClose(){if(this.isClosed)throw Object.defineProperty(Error("Cannot close a CloseController multiple times"),"__NEXT_ERROR_CODE",{value:"E229",enumerable:!1,configurable:!0});this.listeners>0&&this.target.dispatchEvent(new Event("close")),this.isClosed=!0}constructor(){this.target=new EventTarget,this.listeners=0,this.isClosed=!1}}function tl(){return{previewModeId:process.env.__NEXT_PREVIEW_MODE_ID,previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}}let tu=Symbol.for("@next/request-context"),tc=e=>{let t=["/layout"];if(e.startsWith("/")){let r=e.split("/");for(let e=1;e<r.length+1;e++){let i=r.slice(0,e).join("/");i&&(i.endsWith("/page")||i.endsWith("/route")||(i=`${i}${!i.endsWith("/")?"/":""}layout`),t.push(i))}}return t};async function th(e,t,r){let i=[],s=r&&r.size>0;for(let t of tc(e))t=`${x}${t}`,i.push(t);if(t.pathname&&!s){let e=`${x}${t.pathname}`;i.push(e)}return{tags:i,expirationsByCacheKind:function(e){let t=new Map,r=e4();if(r)for(let[i,s]of r)"getExpiration"in s&&t.set(i,ta(async()=>s.getExpiration(...e)));return t}(i)}}class td extends X{constructor(e){super(e.input,e.init),this.sourcePage=e.page}get request(){throw Object.defineProperty(new k({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new k({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}waitUntil(){throw Object.defineProperty(new k({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}let tp={keys:e=>Array.from(e.keys()),get:(e,t)=>e.get(t)??void 0},tf=(e,t)=>eK().withPropagatedContext(e.headers,t,tp),tg=!1;async function tm(e){var t;let i,s;if(!tg&&(tg=!0,"true"===process.env.NEXT_PRIVATE_TEST_PROXY)){let{interceptTestApis:e,wrapRequestHandler:t}=r(905);e(),tf=t(tf)}await S();let n=void 0!==globalThis.__BUILD_MANIFEST;e.request.url=e.request.url.replace(/\.rsc($|\?)/,"$1");let a=new G(e.request.url,{headers:e.request.headers,nextConfig:e.request.nextConfig});for(let e of[...a.searchParams.keys()]){let t=a.searchParams.getAll(e),r=function(e){for(let t of["nxtP","nxtI"])if(e!==t&&e.startsWith(t))return e.substring(t.length);return null}(e);if(r){for(let e of(a.searchParams.delete(r),t))a.searchParams.append(r,e);a.searchParams.delete(e)}}let o=a.buildId;a.buildId="";let l=function(e){let t=new Headers;for(let[r,i]of Object.entries(e))for(let e of Array.isArray(i)?i:[i])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}(e.request.headers),u=l.has("x-nextjs-data"),c="1"===l.get("RSC");u&&"/index"===a.pathname&&(a.pathname="/");let h=new Map;if(!n)for(let e of es){let t=e.toLowerCase(),r=l.get(t);null!==r&&(h.set(t,r),l.delete(t))}let d=new td({page:e.page,input:(function(e){let t="string"==typeof e,r=t?new URL(e):e;return r.searchParams.delete(en),t?r.toString():r})(a).toString(),init:{body:e.request.body,headers:l,method:e.request.method,nextConfig:e.request.nextConfig,signal:e.request.signal}});u&&Object.defineProperty(d,"__isData",{enumerable:!1,value:!0}),!globalThis.__incrementalCache&&e.IncrementalCache&&(globalThis.__incrementalCache=new e.IncrementalCache({appDir:!0,fetchCache:!0,minimalMode:!0,fetchCacheKeyPrefix:"",dev:!1,requestHeaders:e.request.headers,requestProtocol:"https",getPrerenderManifest:()=>({version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:tl()})}));let p=e.request.waitUntil??(null==(t=function(){let e=globalThis[tu];return null==e?void 0:e.get()}())?void 0:t.waitUntil),f=new $({request:d,page:e.page,context:p?{waitUntil:p}:void 0});if((i=await tf(d,()=>{if("/middleware"===e.page||"/src/middleware"===e.page){let t=f.waitUntil.bind(f),r=new to;return eK().trace(eR.execute,{spanName:`middleware ${d.method} ${d.nextUrl.pathname}`,attributes:{"http.target":d.nextUrl.pathname,"http.method":d.method}},async()=>{try{var i,n,a,l,u,c;let h=tl(),p=await th("/",d.nextUrl,null),g=(u=d.nextUrl,c=e=>{s=e},function(e,t,r,i,s,n,a,o,l,u,c){function h(e){r&&r.setHeader("Set-Cookie",e)}let d={};return{type:"request",phase:e,implicitTags:n,url:{pathname:i.pathname,search:i.search??""},rootParams:s,get headers(){return d.headers||(d.headers=function(e){let t=eo.from(e);for(let e of es)t.delete(e.toLowerCase());return eo.seal(t)}(t.headers)),d.headers},get cookies(){if(!d.cookies){let e=new K.RequestCookies(eo.from(t.headers));eY(t,e),d.cookies=eg.seal(e)}return d.cookies},set cookies(value){d.cookies=value},get mutableCookies(){if(!d.mutableCookies){let e=function(e,t){let r=new K.RequestCookies(eo.from(e));return eb.wrap(r,t)}(t.headers,a||(r?h:void 0));eY(t,e),d.mutableCookies=e}return d.mutableCookies},get userspaceMutableCookies(){return d.userspaceMutableCookies||(d.userspaceMutableCookies=function(e){let t=new Proxy(e,{get(e,r,i){switch(r){case"delete":return function(...r){return ev("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return ev("cookies().set"),e.set(...r),t};default:return Y.get(e,r,i)}}});return t}(this.mutableCookies)),d.userspaceMutableCookies},get draftMode(){return d.draftMode||(d.draftMode=new eX(l,t,this.cookies,this.mutableCookies)),d.draftMode},renderResumeDataCache:o??null,isHmrRefresh:u,serverComponentsHmrCache:c||globalThis.__serverComponentsHmrCache}}("action",d,void 0,u,{},p,c,void 0,h,!1,void 0)),m=function({page:e,fallbackRouteParams:t,renderOpts:r,requestEndedState:i,isPrefetchRequest:s,buildId:n,previouslyRevalidatedTags:a}){var o;let l={isStaticGeneration:!r.shouldWaitOnAllReady&&!r.supportsDynamicResponse&&!r.isDraftMode&&!r.isPossibleServerAction,page:e,fallbackRouteParams:t,route:(o=e.split("/").reduce((e,t,r,i)=>t?"("===t[0]&&t.endsWith(")")||"@"===t[0]||("page"===t||"route"===t)&&r===i.length-1?e:e+"/"+t:e,"")).startsWith("/")?o:"/"+o,incrementalCache:r.incrementalCache||globalThis.__incrementalCache,cacheLifeProfiles:r.cacheLifeProfiles,isRevalidate:r.isRevalidate,isPrerendering:r.nextExport,fetchCache:r.fetchCache,isOnDemandRevalidate:r.isOnDemandRevalidate,isDraftMode:r.isDraftMode,requestEndedState:i,isPrefetchRequest:s,buildId:n,reactLoadableManifest:(null==r?void 0:r.reactLoadableManifest)||{},assetPrefix:(null==r?void 0:r.assetPrefix)||"",afterContext:function(e){let{waitUntil:t,onClose:r,onAfterTaskError:i}=e;return new ts({waitUntil:t,onClose:r,onTaskError:i})}(r),dynamicIOEnabled:r.experimental.dynamicIO,dev:r.dev??!1,previouslyRevalidatedTags:a,refreshTagsByCacheKind:function(){let e=new Map,t=e4();if(t)for(let[r,i]of t)"refreshTags"in i&&e.set(r,ta(async()=>i.refreshTags()));return e}()};return r.store=l,l}({page:"/",fallbackRouteParams:null,renderOpts:{cacheLifeProfiles:null==(n=e.request.nextConfig)||null==(i=n.experimental)?void 0:i.cacheLife,experimental:{isRoutePPREnabled:!1,dynamicIO:!1,authInterrupts:!!(null==(l=e.request.nextConfig)||null==(a=l.experimental)?void 0:a.authInterrupts)},supportsDynamicResponse:!0,waitUntil:t,onClose:r.onClose.bind(r),onAfterTaskError:void 0},requestEndedState:{ended:!1},isPrefetchRequest:d.headers.has(ei),buildId:o??"",previouslyRevalidatedTags:[]});return await ed.run(m,()=>ep.run(g,e.handler,d,f))}finally{setTimeout(()=>{r.dispatchClose()},0)}})}return e.handler(d,f)}))&&!(i instanceof Response))throw Object.defineProperty(TypeError("Expected an instance of Response to be returned"),"__NEXT_ERROR_CODE",{value:"E567",enumerable:!1,configurable:!0});i&&s&&i.headers.set("set-cookie",s);let g=null==i?void 0:i.headers.get("x-middleware-rewrite");if(i&&g&&(c||!n)){let t=new G(g,{forceLocale:!0,headers:e.request.headers,nextConfig:e.request.nextConfig});n||t.host!==d.nextUrl.host||(t.buildId=o||t.buildId,i.headers.set("x-middleware-rewrite",String(t)));let{url:r,isRelative:s}=er(t.toString(),a.toString());!n&&u&&i.headers.set("x-nextjs-rewrite",r),c&&s&&(a.pathname!==t.pathname&&i.headers.set("x-nextjs-rewritten-path",t.pathname),a.search!==t.search&&i.headers.set("x-nextjs-rewritten-query",t.search.slice(1)))}let m=null==i?void 0:i.headers.get("Location");if(i&&m&&!n){let t=new G(m,{forceLocale:!1,headers:e.request.headers,nextConfig:e.request.nextConfig});i=new Response(i.body,i),t.host===a.host&&(t.buildId=o||t.buildId,i.headers.set("Location",t.toString())),u&&(i.headers.delete("Location"),i.headers.set("x-nextjs-redirect",er(t.toString(),a.toString()).url))}let b=i||et.next(),v=b.headers.get("x-middleware-override-headers"),w=[];if(v){for(let[e,t]of h)b.headers.set(`x-middleware-request-${e}`,t),w.push(e);w.length>0&&b.headers.set("x-middleware-override-headers",v+","+w.join(","))}return{response:b,waitUntil:("internal"===f[N].kind?Promise.all(f[N].promises).then(()=>{}):void 0)??Promise.resolve(),fetchMetrics:d.fetchMetrics}}var tb=r(554);function tv(){return"undefined"!=typeof window&&void 0!==window.document}let tw={path:"/",sameSite:"lax",httpOnly:!1,maxAge:3456e4},ty=/^(.*)[.](0|[1-9][0-9]*)$/;function t_(e,t){if(e===t)return!0;let r=e.match(ty);return!!r&&r[1]===t}function tS(e,t,r){let i=r??3180,s=encodeURIComponent(t);if(s.length<=i)return[{name:e,value:t}];let n=[];for(;s.length>0;){let e=s.slice(0,i),t=e.lastIndexOf("%");t>i-3&&(e=e.slice(0,t));let r="";for(;e.length>0;)try{r=decodeURIComponent(e);break}catch(t){if(t instanceof URIError&&"%"===e.at(-3)&&e.length>3)e=e.slice(0,e.length-3);else throw t}n.push(r),s=s.slice(e.length)}return n.map((t,r)=>({name:`${e}.${r}`,value:t}))}async function tE(e,t){let r=await t(e);if(r)return r;let i=[];for(let r=0;;r++){let s=`${e}.${r}`,n=await t(s);if(!n)break;i.push(n)}return i.length>0?i.join(""):null}let tk="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),tO=" 	\n\r=".split(""),tT=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<tO.length;t+=1)e[tO[t].charCodeAt(0)]=-2;for(let t=0;t<tk.length;t+=1)e[tk[t].charCodeAt(0)]=t;return e})();function tx(e){let t=[],r=0,i=0;if(function(e,t){for(let r=0;r<e.length;r+=1){let i=e.charCodeAt(r);if(i>55295&&i<=56319){let t=(i-55296)*1024&65535;i=(e.charCodeAt(r+1)-56320&65535|t)+65536,r+=1}!function(e,t){if(e<=127)return t(e);if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}(i,t)}}(e,e=>{for(r=r<<8|e,i+=8;i>=6;){let e=r>>i-6&63;t.push(tk[e]),i-=6}}),i>0)for(r<<=6-i,i=6;i>=6;){let e=r>>i-6&63;t.push(tk[e]),i-=6}return t.join("")}function tP(e){let t=[],r=e=>{t.push(String.fromCodePoint(e))},i={utf8seq:0,codepoint:0},s=0,n=0;for(let t=0;t<e.length;t+=1){let a=tT[e.charCodeAt(t)];if(a>-1)for(s=s<<6|a,n+=6;n>=8;)(function(e,t,r){if(0===t.utf8seq){if(e<=127)return r(e);for(let r=1;r<6;r+=1)if((e>>7-r&1)==0){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}})(s>>n-8&255,i,r),n-=8;else if(-2===a)continue;else throw Error(`Invalid Base64-URL character "${e.at(t)}" at position ${t}`)}return t.join("")}let tC="base64-";async function tR({getAll:e,setAll:t,setItems:r,removedItems:i},s){let n=s.cookieEncoding,a=s.cookieOptions??null,o=await e([...r?Object.keys(r):[],...i?Object.keys(i):[]]),l=o?.map(({name:e})=>e)||[],u=Object.keys(i).flatMap(e=>l.filter(t=>t_(t,e))),c=Object.keys(r).flatMap(e=>{let t=new Set(l.filter(t=>t_(t,e))),i=r[e];"base64url"===n&&(i=tC+tx(i));let s=tS(e,i);return s.forEach(e=>{t.delete(e.name)}),u.push(...t),s}),h={...tw,...a,maxAge:0},d={...tw,...a,maxAge:tw.maxAge};delete h.name,delete d.name,await t([...u.map(e=>({name:e,value:"",options:h})),...c.map(({name:e,value:t})=>({name:e,value:t,options:d}))])}let tj=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,3)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)};class tI extends Error{constructor(e,t="FunctionsError",r){super(e),this.name=t,this.context=r}}class tA extends tI{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class tN extends tI{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class tL extends tI{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}!function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"}(n||(n={}));class t${constructor(e,{headers:t={},customFetch:r,region:i=n.Any}={}){this.url=e,this.headers=t,this.region=i,this.fetch=tj(r)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var r,i,s,n,a;return i=this,s=void 0,n=void 0,a=function*(){try{let i,s,{headers:n,method:a,body:o}=t,l={},{region:u}=t;u||(u=this.region),u&&"any"!==u&&(l["x-region"]=u),o&&(n&&!Object.prototype.hasOwnProperty.call(n,"Content-Type")||!n)&&("undefined"!=typeof Blob&&o instanceof Blob||o instanceof ArrayBuffer?(l["Content-Type"]="application/octet-stream",i=o):"string"==typeof o?(l["Content-Type"]="text/plain",i=o):"undefined"!=typeof FormData&&o instanceof FormData?i=o:(l["Content-Type"]="application/json",i=JSON.stringify(o)));let c=yield this.fetch(`${this.url}/${e}`,{method:a||"POST",headers:Object.assign(Object.assign(Object.assign({},l),this.headers),n),body:i}).catch(e=>{throw new tA(e)}),h=c.headers.get("x-relay-error");if(h&&"true"===h)throw new tN(c);if(!c.ok)throw new tL(c);let d=(null!=(r=c.headers.get("Content-Type"))?r:"text/plain").split(";")[0].trim();return{data:"application/json"===d?yield c.json():"application/octet-stream"===d?yield c.blob():"text/event-stream"===d?c:"multipart/form-data"===d?yield c.formData():yield c.text(),error:null}}catch(e){return{data:null,error:e}}},new(n||(n=Promise))(function(e,t){function r(e){try{l(a.next(e))}catch(e){t(e)}}function o(e){try{l(a.throw(e))}catch(e){t(e)}}function l(t){var i;t.done?e(t.value):((i=t.value)instanceof n?i:new n(function(e){e(i)})).then(r,o)}l((a=a.apply(i,s||[])).next())})}}let{PostgrestClient:tM,PostgrestQueryBuilder:tD,PostgrestFilterBuilder:tU,PostgrestTransformBuilder:tq,PostgrestBuilder:tB,PostgrestError:tz}=r(355),tV="undefined"==typeof window?r(999):window.WebSocket,tW={"X-Client-Info":"realtime-js/2.11.10"};!function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"}(a||(a={})),function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"}(o||(o={})),function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"}(l||(l={})),(u||(u={})).websocket="websocket",function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"}(c||(c={}));class tF{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):"string"==typeof e?t(JSON.parse(e)):t({})}_binaryDecode(e){let t=new DataView(e),r=new TextDecoder;return this._decodeBroadcast(e,t,r)}_decodeBroadcast(e,t,r){let i=t.getUint8(1),s=t.getUint8(2),n=this.HEADER_LENGTH+2,a=r.decode(e.slice(n,n+i));n+=i;let o=r.decode(e.slice(n,n+s));return n+=s,{ref:null,topic:a,event:o,payload:JSON.parse(r.decode(e.slice(n,e.byteLength)))}}}class tH{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}!function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"}(h||(h={}));let tG=(e,t,r={})=>{var i;let s=null!=(i=r.skipTypes)?i:[];return Object.keys(t).reduce((r,i)=>(r[i]=tK(i,e,t,s),r),{})},tK=(e,t,r,i)=>{let s=t.find(t=>t.name===e),n=null==s?void 0:s.type,a=r[e];return n&&!i.includes(n)?tJ(n,a):tX(a)},tJ=(e,t)=>{if("_"===e.charAt(0))return t0(t,e.slice(1,e.length));switch(e){case h.bool:return tY(t);case h.float4:case h.float8:case h.int2:case h.int4:case h.int8:case h.numeric:case h.oid:return tQ(t);case h.json:case h.jsonb:return tZ(t);case h.timestamp:return t1(t);case h.abstime:case h.date:case h.daterange:case h.int4range:case h.int8range:case h.money:case h.reltime:case h.text:case h.time:case h.timestamptz:case h.timetz:case h.tsrange:case h.tstzrange:default:return tX(t)}},tX=e=>e,tY=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},tQ=e=>{if("string"==typeof e){let t=parseFloat(e);if(!Number.isNaN(t))return t}return e},tZ=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){console.log(`JSON parse error: ${e}`)}return e},t0=(e,t)=>{if("string"!=typeof e)return e;let r=e.length-1,i=e[r];if("{"===e[0]&&"}"===i){let i,s=e.slice(1,r);try{i=JSON.parse("["+s+"]")}catch(e){i=s?s.split(","):[]}return i.map(e=>tJ(t,e))}return e},t1=e=>"string"==typeof e?e.replace(" ","T"):e,t2=e=>{let t=e;return(t=(t=t.replace(/^ws/i,"http")).replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,"")).replace(/\/+$/,"")};class t3{constructor(e,t,r={},i=1e4){this.channel=e,this.event=t,this.payload=r,this.timeout=i,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var r;return this._hasReceived(e)&&t(null==(r=this.receivedResp)?void 0:r.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){this.timeoutTimer||(this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref),this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout))}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(t=>t.status===e).forEach(e=>e.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}!function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"}(d||(d={}));class t6{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};let r=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},e=>{let{onJoin:t,onLeave:r,onSync:i}=this.caller;this.joinRef=this.channel._joinRef(),this.state=t6.syncState(this.state,e,t,r),this.pendingDiffs.forEach(e=>{this.state=t6.syncDiff(this.state,e,t,r)}),this.pendingDiffs=[],i()}),this.channel._on(r.diff,{},e=>{let{onJoin:t,onLeave:r,onSync:i}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=t6.syncDiff(this.state,e,t,r),i())}),this.onJoin((e,t,r)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:r})}),this.onLeave((e,t,r)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:r})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,r,i){let s=this.cloneDeep(e),n=this.transformState(t),a={},o={};return this.map(s,(e,t)=>{n[e]||(o[e]=t)}),this.map(n,(e,t)=>{let r=s[e];if(r){let i=t.map(e=>e.presence_ref),s=r.map(e=>e.presence_ref),n=t.filter(e=>0>s.indexOf(e.presence_ref)),l=r.filter(e=>0>i.indexOf(e.presence_ref));n.length>0&&(a[e]=n),l.length>0&&(o[e]=l)}else a[e]=t}),this.syncDiff(s,{joins:a,leaves:o},r,i)}static syncDiff(e,t,r,i){let{joins:s,leaves:n}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return r||(r=()=>{}),i||(i=()=>{}),this.map(s,(t,i)=>{var s;let n=null!=(s=e[t])?s:[];if(e[t]=this.cloneDeep(i),n.length>0){let r=e[t].map(e=>e.presence_ref),i=n.filter(e=>0>r.indexOf(e.presence_ref));e[t].unshift(...i)}r(t,n,i)}),this.map(n,(t,r)=>{let s=e[t];if(!s)return;let n=r.map(e=>e.presence_ref);s=s.filter(e=>0>n.indexOf(e.presence_ref)),e[t]=s,i(t,s,r),0===s.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(r=>t(r,e[r]))}static transformState(e){return Object.getOwnPropertyNames(e=this.cloneDeep(e)).reduce((t,r)=>{let i=e[r];return"metas"in i?t[r]=i.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):t[r]=i,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}!function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"}(p||(p={})),function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"}(f||(f={})),function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"}(g||(g={}));class t4{constructor(e,t={config:{}},r){this.topic=e,this.params=t,this.socket=r,this.bindings={},this.state=o.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new t3(this,l.join,this.params,this.timeout),this.rejoinTimer=new tH(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=o.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=o.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=o.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=o.errored,this.rejoinTimer.scheduleTimeout())}),this._on(l.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new t6(this),this.broadcastEndpointURL=t2(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var r,i;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{let{config:{broadcast:s,presence:n,private:a}}=this.params;this._onError(t=>null==e?void 0:e(g.CHANNEL_ERROR,t)),this._onClose(()=>null==e?void 0:e(g.CLOSED));let l={},u={broadcast:s,presence:n,postgres_changes:null!=(i=null==(r=this.bindings.postgres_changes)?void 0:r.map(e=>e.filter))?i:[],private:a};this.socket.accessTokenValue&&(l.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:u},l)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async({postgres_changes:t})=>{var r;if(this.socket.setAuth(),void 0===t){null==e||e(g.SUBSCRIBED);return}{let i=this.bindings.postgres_changes,s=null!=(r=null==i?void 0:i.length)?r:0,n=[];for(let r=0;r<s;r++){let s=i[r],{filter:{event:a,schema:l,table:u,filter:c}}=s,h=t&&t[r];if(h&&h.event===a&&h.schema===l&&h.table===u&&h.filter===c)n.push(Object.assign(Object.assign({},s),{id:h.id}));else{this.unsubscribe(),this.state=o.errored,null==e||e(g.CHANNEL_ERROR,Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=n,e&&e(g.SUBSCRIBED);return}}).receive("error",t=>{this.state=o.errored,null==e||e(g.CHANNEL_ERROR,Error(JSON.stringify(Object.values(t).join(", ")||"error")))}).receive("timeout",()=>{null==e||e(g.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,r){return this._on(e,t,r)}async send(e,t={}){var r,i;if(this._canPush()||"broadcast"!==e.type)return new Promise(r=>{var i,s,n;let a=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null==(n=null==(s=null==(i=this.params)?void 0:i.config)?void 0:s.broadcast)?void 0:n.ack)||r("ok"),a.receive("ok",()=>r("ok")),a.receive("error",()=>r("error")),a.receive("timeout",()=>r("timed out"))});{let{event:s,payload:n}=e,a={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:s,payload:n,private:this.private}]})};try{let e=await this._fetchWithTimeout(this.broadcastEndpointURL,a,null!=(r=t.timeout)?r:this.timeout);return await (null==(i=e.body)?void 0:i.cancel()),e.ok?"ok":"error"}catch(e){if("AbortError"===e.name)return"timed out";return"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=o.leaving;let t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(l.close,"leave",this._joinRef())};return this.joinPush.destroy(),new Promise(r=>{let i=new t3(this,l.leave,{},e);i.receive("ok",()=>{t(),r("ok")}).receive("timeout",()=>{t(),r("timed out")}).receive("error",()=>{r("error")}),i.send(),this._canPush()||i.trigger("ok",{})})}teardown(){this.pushBuffer.forEach(e=>e.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(e,t,r){let i=new AbortController,s=setTimeout(()=>i.abort(),r),n=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:i.signal}));return clearTimeout(s),n}_push(e,t,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let i=new t3(this,e,t,r);return this._canPush()?i.send():(i.startTimeout(),this.pushBuffer.push(i)),i}_onMessage(e,t,r){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,r){var i,s;let n=e.toLocaleLowerCase(),{close:a,error:o,leave:u,join:c}=l;if(r&&[a,o,u,c].indexOf(n)>=0&&r!==this._joinRef())return;let h=this._onMessage(n,t,r);if(t&&!h)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(n)?null==(i=this.bindings.postgres_changes)||i.filter(e=>{var t,r,i;return(null==(t=e.filter)?void 0:t.event)==="*"||(null==(i=null==(r=e.filter)?void 0:r.event)?void 0:i.toLocaleLowerCase())===n}).map(e=>e.callback(h,r)):null==(s=this.bindings[n])||s.filter(e=>{var r,i,s,a,o,l;if(!["broadcast","presence","postgres_changes"].includes(n))return e.type.toLocaleLowerCase()===n;if("id"in e){let n=e.id,a=null==(r=e.filter)?void 0:r.event;return n&&(null==(i=t.ids)?void 0:i.includes(n))&&("*"===a||(null==a?void 0:a.toLocaleLowerCase())===(null==(s=t.data)?void 0:s.type.toLocaleLowerCase()))}{let r=null==(o=null==(a=null==e?void 0:e.filter)?void 0:a.event)?void 0:o.toLocaleLowerCase();return"*"===r||r===(null==(l=null==t?void 0:t.event)?void 0:l.toLocaleLowerCase())}}).map(e=>{if("object"==typeof h&&"ids"in h){let e=h.data,{schema:t,table:r,commit_timestamp:i,type:s,errors:n}=e;h=Object.assign(Object.assign({},{schema:t,table:r,commit_timestamp:i,eventType:s,new:{},old:{},errors:n}),this._getPayloadRecords(e))}e.callback(h,r)})}_isClosed(){return this.state===o.closed}_isJoined(){return this.state===o.joined}_isJoining(){return this.state===o.joining}_isLeaving(){return this.state===o.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,r){let i=e.toLocaleLowerCase(),s={type:i,filter:t,callback:r};return this.bindings[i]?this.bindings[i].push(s):this.bindings[i]=[s],this}_off(e,t){let r=e.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(e=>{var i;return!((null==(i=e.type)?void 0:i.toLocaleLowerCase())===r&&t4.isEqual(e.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(let r in e)if(e[r]!==t[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(l.close,{},e)}_onError(e){this._on(l.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=o.joining,this.joinPush.resend(e))}_getPayloadRecords(e){let t={new:{},old:{}};return("INSERT"===e.type||"UPDATE"===e.type)&&(t.new=tG(e.columns,e.record)),("UPDATE"===e.type||"DELETE"===e.type)&&(t.old=tG(e.columns,e.old_record)),t}}let t5=()=>{},t9=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class t8{constructor(e,t){var i;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=tW,this.params={},this.timeout=1e4,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=t5,this.ref=0,this.logger=t5,this.conn=null,this.sendBuffer=[],this.serializer=new tF,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,3)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},this.endPoint=`${e}/${u.websocket}`,this.httpEndpoint=t2(e),(null==t?void 0:t.transport)?this.transport=t.transport:this.transport=null,(null==t?void 0:t.params)&&(this.params=t.params),(null==t?void 0:t.headers)&&(this.headers=Object.assign(Object.assign({},this.headers),t.headers)),(null==t?void 0:t.timeout)&&(this.timeout=t.timeout),(null==t?void 0:t.logger)&&(this.logger=t.logger),((null==t?void 0:t.logLevel)||(null==t?void 0:t.log_level))&&(this.logLevel=t.logLevel||t.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),(null==t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);let s=null==(i=null==t?void 0:t.params)?void 0:i.apikey;if(s&&(this.accessTokenValue=s,this.apiKey=s),this.reconnectAfterMs=(null==t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==t?void 0:t.encode)?t.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null==t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new tH(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(null==t?void 0:t.fetch),null==t?void 0:t.worker){if("undefined"!=typeof window&&!window.Worker)throw Error("Web Worker is not supported");this.worker=(null==t?void 0:t.worker)||!1,this.workerUrl=null==t?void 0:t.workerUrl}this.accessToken=(null==t?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=tV),this.transport){"undefined"!=typeof window&&this.transport===window.WebSocket?this.conn=new this.transport(this.endpointURL()):this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection();return}this.conn=new t7(this.endpointURL(),void 0,{close:()=>{this.conn=null}})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(e=>e.teardown()))}getChannels(){return this.channels}async removeChannel(e){let t=await e.unsubscribe();return this.channels=this.channels.filter(t=>t._joinRef!==e._joinRef),0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){let e=await Promise.all(this.channels.map(e=>e.unsubscribe()));return this.channels=[],this.disconnect(),e}log(e,t,r){this.logger(e,t,r)}connectionState(){switch(this.conn&&this.conn.readyState){case a.connecting:return c.Connecting;case a.open:return c.Open;case a.closing:return c.Closing;default:return c.Closed}}isConnected(){return this.connectionState()===c.Open}channel(e,t={config:{}}){let r=`realtime:${e}`,i=this.getChannels().find(e=>e.topic===r);if(i)return i;{let r=new t4(`realtime:${e}`,t,this);return this.channels.push(r),r}}push(e){let{topic:t,event:r,payload:i,ref:s}=e,n=()=>{this.encode(e,e=>{var t;null==(t=this.conn)||t.send(e)})};this.log("push",`${t} ${r} (${s})`,i),this.isConnected()?n():this.sendBuffer.push(n)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=t&&(this.accessTokenValue=t,this.channels.forEach(e=>{t&&e.updateJoinPayload({access_token:t,version:this.headers&&this.headers["X-Client-Info"]}),e.joinedOnce&&e._isJoined()&&e._push(l.access_token,{access_token:t})}))}async sendHeartbeat(){var e;if(!this.isConnected())return void this.heartbeatCallback("disconnected");if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),null==(e=this.conn)||e.close(1e3,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t.topic!==e.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,e=>{let{topic:t,event:r,payload:i,ref:s}=e;"phoenix"===t&&"phx_reply"===r&&this.heartbeatCallback("ok"==e.payload.status?"ok":"error"),s&&s===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${i.status||""} ${t} ${r} ${s&&"("+s+")"||""}`,i),Array.from(this.channels).filter(e=>e._isMember(t)).forEach(e=>e._trigger(r,i,s)),this.stateChangeCallbacks.message.forEach(t=>t(e))})}_onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");let e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}else this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs);this.stateChangeCallbacks.open.forEach(e=>e())}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",e.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(l.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;let r=e.match(/\?/)?"&":"?",i=new URLSearchParams(t);return`${e}${r}${i}`}_workerObjectUrl(e){let t;if(e)t=e;else{let e=new Blob([t9],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class t7{constructor(e,t,r){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=a.connecting,this.send=()=>{},this.url=null,this.url=e,this.close=r.close}}class re extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function rt(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class rr extends re{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class ri extends re{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}let rs=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,3)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},rn=()=>(function(e,t,r,i){return new(r||(r=Promise))(function(s,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){return"undefined"==typeof Response?(yield Promise.resolve().then(r.bind(r,3))).Response:Response}),ra=e=>{if(Array.isArray(e))return e.map(e=>ra(e));if("function"==typeof e||e!==Object(e))return e;let t={};return Object.entries(e).forEach(([e,r])=>{t[e.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""))]=ra(r)}),t};var ro=function(e,t,r,i){return new(r||(r=Promise))(function(s,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})};let rl=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),ru=(e,t,r)=>ro(void 0,void 0,void 0,function*(){e instanceof(yield rn())&&!(null==r?void 0:r.noResolveJson)?e.json().then(r=>{t(new rr(rl(r),e.status||500))}).catch(e=>{t(new ri(rl(e),e))}):t(new ri(rl(e),e))}),rc=(e,t,r,i)=>{let s={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?s:(s.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),i&&(s.body=JSON.stringify(i)),Object.assign(Object.assign({},s),r))};function rh(e,t,r,i,s,n){return ro(this,void 0,void 0,function*(){return new Promise((a,o)=>{e(r,rc(t,i,s,n)).then(e=>{if(!e.ok)throw e;return(null==i?void 0:i.noResolveJson)?e:e.json()}).then(e=>a(e)).catch(e=>ru(e,o,i))})})}function rd(e,t,r,i){return ro(this,void 0,void 0,function*(){return rh(e,"GET",t,r,i)})}function rp(e,t,r,i,s){return ro(this,void 0,void 0,function*(){return rh(e,"POST",t,i,s,r)})}function rf(e,t,r,i,s){return ro(this,void 0,void 0,function*(){return rh(e,"DELETE",t,i,s,r)})}var rg=r(356).Buffer,rm=function(e,t,r,i){return new(r||(r=Promise))(function(s,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})};let rb={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},rv={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class rw{constructor(e,t={},r,i){this.url=e,this.headers=t,this.bucketId=r,this.fetch=rs(i)}uploadOrUpdate(e,t,r,i){return rm(this,void 0,void 0,function*(){try{let s,n=Object.assign(Object.assign({},rv),i),a=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(n.upsert)}),o=n.metadata;"undefined"!=typeof Blob&&r instanceof Blob?((s=new FormData).append("cacheControl",n.cacheControl),o&&s.append("metadata",this.encodeMetadata(o)),s.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?((s=r).append("cacheControl",n.cacheControl),o&&s.append("metadata",this.encodeMetadata(o))):(s=r,a["cache-control"]=`max-age=${n.cacheControl}`,a["content-type"]=n.contentType,o&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(o)))),(null==i?void 0:i.headers)&&(a=Object.assign(Object.assign({},a),i.headers));let l=this._removeEmptyFolders(t),u=this._getFinalPath(l),c=yield this.fetch(`${this.url}/object/${u}`,Object.assign({method:e,body:s,headers:a},(null==n?void 0:n.duplex)?{duplex:n.duplex}:{})),h=yield c.json();if(c.ok)return{data:{path:l,id:h.Id,fullPath:h.Key},error:null};return{data:null,error:h}}catch(e){if(rt(e))return{data:null,error:e};throw e}})}upload(e,t,r){return rm(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,r)})}uploadToSignedUrl(e,t,r,i){return rm(this,void 0,void 0,function*(){let s=this._removeEmptyFolders(e),n=this._getFinalPath(s),a=new URL(this.url+`/object/upload/sign/${n}`);a.searchParams.set("token",t);try{let e,t=Object.assign({upsert:rv.upsert},i),n=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&r instanceof Blob?((e=new FormData).append("cacheControl",t.cacheControl),e.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(e=r).append("cacheControl",t.cacheControl):(e=r,n["cache-control"]=`max-age=${t.cacheControl}`,n["content-type"]=t.contentType);let o=yield this.fetch(a.toString(),{method:"PUT",body:e,headers:n}),l=yield o.json();if(o.ok)return{data:{path:s,fullPath:l.Key},error:null};return{data:null,error:l}}catch(e){if(rt(e))return{data:null,error:e};throw e}})}createSignedUploadUrl(e,t){return rm(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e),i=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(i["x-upsert"]="true");let s=yield rp(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:i}),n=new URL(this.url+s.url),a=n.searchParams.get("token");if(!a)throw new re("No token returned by API");return{data:{signedUrl:n.toString(),path:e,token:a},error:null}}catch(e){if(rt(e))return{data:null,error:e};throw e}})}update(e,t,r){return rm(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,r)})}move(e,t,r){return rm(this,void 0,void 0,function*(){try{return{data:yield rp(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(e){if(rt(e))return{data:null,error:e};throw e}})}copy(e,t,r){return rm(this,void 0,void 0,function*(){try{return{data:{path:(yield rp(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(e){if(rt(e))return{data:null,error:e};throw e}})}createSignedUrl(e,t,r){return rm(this,void 0,void 0,function*(){try{let i=this._getFinalPath(e),s=yield rp(this.fetch,`${this.url}/object/sign/${i}`,Object.assign({expiresIn:t},(null==r?void 0:r.transform)?{transform:r.transform}:{}),{headers:this.headers}),n=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:s={signedUrl:encodeURI(`${this.url}${s.signedURL}${n}`)},error:null}}catch(e){if(rt(e))return{data:null,error:e};throw e}})}createSignedUrls(e,t,r){return rm(this,void 0,void 0,function*(){try{let i=yield rp(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),s=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:i.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${s}`):null})),error:null}}catch(e){if(rt(e))return{data:null,error:e};throw e}})}download(e,t){return rm(this,void 0,void 0,function*(){let r=void 0!==(null==t?void 0:t.transform),i=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),s=i?`?${i}`:"";try{let t=this._getFinalPath(e),i=yield rd(this.fetch,`${this.url}/${r?"render/image/authenticated":"object"}/${t}${s}`,{headers:this.headers,noResolveJson:!0});return{data:yield i.blob(),error:null}}catch(e){if(rt(e))return{data:null,error:e};throw e}})}info(e){return rm(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{let e=yield rd(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:ra(e),error:null}}catch(e){if(rt(e))return{data:null,error:e};throw e}})}exists(e){return rm(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{return yield function(e,t,r,i){return ro(this,void 0,void 0,function*(){return rh(e,"HEAD",t,Object.assign(Object.assign({},r),{noResolveJson:!0}),void 0)})}(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(e){if(rt(e)&&e instanceof ri){let t=e.originalError;if([400,404].includes(null==t?void 0:t.status))return{data:!1,error:e}}throw e}})}getPublicUrl(e,t){let r=this._getFinalPath(e),i=[],s=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==s&&i.push(s);let n=void 0!==(null==t?void 0:t.transform),a=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==a&&i.push(a);let o=i.join("&");return""!==o&&(o=`?${o}`),{data:{publicUrl:encodeURI(`${this.url}/${n?"render/image":"object"}/public/${r}${o}`)}}}remove(e){return rm(this,void 0,void 0,function*(){try{return{data:yield rf(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(e){if(rt(e))return{data:null,error:e};throw e}})}list(e,t,r){return rm(this,void 0,void 0,function*(){try{let i=Object.assign(Object.assign(Object.assign({},rb),t),{prefix:e||""});return{data:yield rp(this.fetch,`${this.url}/object/list/${this.bucketId}`,i,{headers:this.headers},r),error:null}}catch(e){if(rt(e))return{data:null,error:e};throw e}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return void 0!==rg?rg.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){let t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}let ry={"X-Client-Info":"storage-js/2.7.1"};var r_=function(e,t,r,i){return new(r||(r=Promise))(function(s,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})};class rS{constructor(e,t={},r){this.url=e,this.headers=Object.assign(Object.assign({},ry),t),this.fetch=rs(r)}listBuckets(){return r_(this,void 0,void 0,function*(){try{return{data:yield rd(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(rt(e))return{data:null,error:e};throw e}})}getBucket(e){return r_(this,void 0,void 0,function*(){try{return{data:yield rd(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(e){if(rt(e))return{data:null,error:e};throw e}})}createBucket(e,t={public:!1}){return r_(this,void 0,void 0,function*(){try{return{data:yield rp(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(rt(e))return{data:null,error:e};throw e}})}updateBucket(e,t){return r_(this,void 0,void 0,function*(){try{return{data:yield function(e,t,r,i,s){return ro(this,void 0,void 0,function*(){return rh(e,"PUT",t,i,void 0,r)})}(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(rt(e))return{data:null,error:e};throw e}})}emptyBucket(e){return r_(this,void 0,void 0,function*(){try{return{data:yield rp(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(e){if(rt(e))return{data:null,error:e};throw e}})}deleteBucket(e){return r_(this,void 0,void 0,function*(){try{return{data:yield rf(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(e){if(rt(e))return{data:null,error:e};throw e}})}}class rE extends rS{constructor(e,t={},r){super(e,t,r)}from(e){return new rw(this.url,this.headers,e,this.fetch)}}let rk="";rk="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";let rO={headers:{"X-Client-Info":`supabase-js-${rk}/2.50.0`}},rT={schema:"public"},rx={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},rP={};var rC=r(3);let rR=e=>{let t;return t=e||("undefined"==typeof fetch?rC.default:fetch),(...e)=>t(...e)},rj=()=>"undefined"==typeof Headers?rC.Headers:Headers,rI=(e,t,r)=>{let i=rR(r),s=rj();return(r,n)=>(function(e,t,r,i){return new(r||(r=Promise))(function(s,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){var a;let o=null!=(a=yield t())?a:e,l=new s(null==n?void 0:n.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization",`Bearer ${o}`),i(r,Object.assign(Object.assign({},n),{headers:l}))})},rA="2.70.0",rN={"X-Client-Info":`gotrue-js/${rA}`},rL="X-Supabase-Api-Version",r$={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},rM=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class rD extends Error{constructor(e,t,r){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=r}}function rU(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class rq extends rD{constructor(e,t,r){super(e,t,r),this.name="AuthApiError",this.status=t,this.code=r}}class rB extends rD{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class rz extends rD{constructor(e,t,r,i){super(e,r,i),this.name=t,this.status=r}}class rV extends rz{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class rW extends rz{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class rF extends rz{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class rH extends rz{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class rG extends rz{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class rK extends rz{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function rJ(e){return rU(e)&&"AuthRetryableFetchError"===e.name}class rX extends rz{constructor(e,t,r){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=r}}class rY extends rz{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}let rQ="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),rZ=" 	\n\r=".split(""),r0=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<rZ.length;t+=1)e[rZ[t].charCodeAt(0)]=-2;for(let t=0;t<rQ.length;t+=1)e[rQ[t].charCodeAt(0)]=t;return e})();function r1(e,t,r){if(null!==e)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;)r(rQ[t.queue>>t.queuedBits-6&63]),t.queuedBits-=6;else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;)r(rQ[t.queue>>t.queuedBits-6&63]),t.queuedBits-=6}function r2(e,t,r){let i=r0[e];if(i>-1)for(t.queue=t.queue<<6|i,t.queuedBits+=6;t.queuedBits>=8;)r(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else if(-2===i)return;else throw Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}function r3(e){let t=[],r=e=>{t.push(String.fromCodePoint(e))},i={utf8seq:0,codepoint:0},s={queue:0,queuedBits:0},n=e=>{!function(e,t,r){if(0===t.utf8seq){if(e<=127)return r(e);for(let r=1;r<6;r+=1)if((e>>7-r&1)==0){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}}(e,i,r)};for(let t=0;t<e.length;t+=1)r2(e.charCodeAt(t),s,n);return t.join("")}let r6=()=>"undefined"!=typeof window&&"undefined"!=typeof document,r4={tested:!1,writable:!1},r5=()=>{if(!r6())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(e){return!1}if(r4.tested)return r4.writable;let e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),r4.tested=!0,r4.writable=!0}catch(e){r4.tested=!0,r4.writable=!1}return r4.writable},r9=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,3)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},r8=e=>"object"==typeof e&&null!==e&&"status"in e&&"ok"in e&&"json"in e&&"function"==typeof e.json,r7=async(e,t,r)=>{await e.setItem(t,JSON.stringify(r))},ie=async(e,t)=>{let r=await e.getItem(t);if(!r)return null;try{return JSON.parse(r)}catch(e){return r}},it=async(e,t)=>{await e.removeItem(t)};class ir{constructor(){this.promise=new ir.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}function ii(e){let t=e.split(".");if(3!==t.length)throw new rY("Invalid JWT structure");for(let e=0;e<t.length;e++)if(!rM.test(t[e]))throw new rY("JWT not in base64url format");return{header:JSON.parse(r3(t[0])),payload:JSON.parse(r3(t[1])),signature:function(e){let t=[],r={queue:0,queuedBits:0},i=e=>{t.push(e)};for(let t=0;t<e.length;t+=1)r2(e.charCodeAt(t),r,i);return new Uint8Array(t)}(t[2]),raw:{header:t[0],payload:t[1]}}}async function is(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function ia(e){return("0"+e.toString(16)).substr(-2)}async function io(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>String.fromCharCode(e)).join("")}async function il(e){return"undefined"==typeof crypto||void 0===crypto.subtle||"undefined"==typeof TextEncoder?(console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e):btoa(await io(e)).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function iu(e,t,r=!1){let i=function(){let e=new Uint32Array(56);if("undefined"==typeof crypto){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length,r="";for(let i=0;i<56;i++)r+=e.charAt(Math.floor(Math.random()*t));return r}return crypto.getRandomValues(e),Array.from(e,ia).join("")}(),s=i;r&&(s+="/PASSWORD_RECOVERY"),await r7(e,`${t}-code-verifier`,s);let n=await il(i),a=i===n?"plain":"s256";return[n,a]}ir.promiseConstructor=Promise;let ic=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i,ih=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function id(e){if(!ih.test(e))throw Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var ip=function(e,t){var r={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(r[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var s=0,i=Object.getOwnPropertySymbols(e);s<i.length;s++)0>t.indexOf(i[s])&&Object.prototype.propertyIsEnumerable.call(e,i[s])&&(r[i[s]]=e[i[s]]);return r};let ig=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),im=[502,503,504];async function ib(e){var t;let r,i;if(!r8(e))throw new rK(ig(e),0);if(im.includes(e.status))throw new rK(ig(e),e.status);try{r=await e.json()}catch(e){throw new rB(ig(e),e)}let s=function(e){let t=e.headers.get(rL);if(!t||!t.match(ic))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(e){return null}}(e);if(s&&s.getTime()>=r$["2024-01-01"].timestamp&&"object"==typeof r&&r&&"string"==typeof r.code?i=r.code:"object"==typeof r&&r&&"string"==typeof r.error_code&&(i=r.error_code),i){if("weak_password"===i)throw new rX(ig(r),e.status,(null==(t=r.weak_password)?void 0:t.reasons)||[]);else if("session_not_found"===i)throw new rV}else if("object"==typeof r&&r&&"object"==typeof r.weak_password&&r.weak_password&&Array.isArray(r.weak_password.reasons)&&r.weak_password.reasons.length&&r.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0))throw new rX(ig(r),e.status,r.weak_password.reasons);throw new rq(ig(r),e.status||500,i)}let iv=(e,t,r,i)=>{let s={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?s:(s.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),s.body=JSON.stringify(i),Object.assign(Object.assign({},s),r))};async function iw(e,t,r,i){var s;let n=Object.assign({},null==i?void 0:i.headers);n[rL]||(n[rL]=r$["2024-01-01"].name),(null==i?void 0:i.jwt)&&(n.Authorization=`Bearer ${i.jwt}`);let a=null!=(s=null==i?void 0:i.query)?s:{};(null==i?void 0:i.redirectTo)&&(a.redirect_to=i.redirectTo);let o=Object.keys(a).length?"?"+new URLSearchParams(a).toString():"",l=await iy(e,t,r+o,{headers:n,noResolveJson:null==i?void 0:i.noResolveJson},{},null==i?void 0:i.body);return(null==i?void 0:i.xform)?null==i?void 0:i.xform(l):{data:Object.assign({},l),error:null}}async function iy(e,t,r,i,s,n){let a,o=iv(t,i,s,n);try{a=await e(r,Object.assign({},o))}catch(e){throw console.error(e),new rK(ig(e),0)}if(a.ok||await ib(a),null==i?void 0:i.noResolveJson)return a;try{return await a.json()}catch(e){await ib(e)}}function i_(e){var t,r,i;let s=null;(i=e).access_token&&i.refresh_token&&i.expires_in&&(s=Object.assign({},e),e.expires_at||(s.expires_at=(r=e.expires_in,Math.round(Date.now()/1e3)+r)));return{data:{session:s,user:null!=(t=e.user)?t:e},error:null}}function iS(e){let t=i_(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0)&&(t.data.weak_password=e.weak_password),t}function iE(e){var t;return{data:{user:null!=(t=e.user)?t:e},error:null}}function ik(e){return{data:e,error:null}}function iO(e){let{action_link:t,email_otp:r,hashed_token:i,redirect_to:s,verification_type:n}=e;return{data:{properties:{action_link:t,email_otp:r,hashed_token:i,redirect_to:s,verification_type:n},user:Object.assign({},ip(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]))},error:null}}function iT(e){return e}let ix=["global","local","others"];var iP=function(e,t){var r={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(r[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var s=0,i=Object.getOwnPropertySymbols(e);s<i.length;s++)0>t.indexOf(i[s])&&Object.prototype.propertyIsEnumerable.call(e,i[s])&&(r[i[s]]=e[i[s]]);return r};class iC{constructor({url:e="",headers:t={},fetch:r}){this.url=e,this.headers=t,this.fetch=r9(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t=ix[0]){if(0>ix.indexOf(t))throw Error(`@supabase/auth-js: Parameter scope must be one of ${ix.join(", ")}`);try{return await iw(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(e){if(rU(e))return{data:null,error:e};throw e}}async inviteUserByEmail(e,t={}){try{return await iw(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:iE})}catch(e){if(rU(e))return{data:{user:null},error:e};throw e}}async generateLink(e){try{let{options:t}=e,r=iP(e,["options"]),i=Object.assign(Object.assign({},r),t);return"newEmail"in r&&(i.new_email=null==r?void 0:r.newEmail,delete i.newEmail),await iw(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:i,headers:this.headers,xform:iO,redirectTo:null==t?void 0:t.redirectTo})}catch(e){if(rU(e))return{data:{properties:null,user:null},error:e};throw e}}async createUser(e){try{return await iw(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:iE})}catch(e){if(rU(e))return{data:{user:null},error:e};throw e}}async listUsers(e){var t,r,i,s,n,a,o;try{let l={nextPage:null,lastPage:0,total:0},u=await iw(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!=(r=null==(t=null==e?void 0:e.page)?void 0:t.toString())?r:"",per_page:null!=(s=null==(i=null==e?void 0:e.perPage)?void 0:i.toString())?s:""},xform:iT});if(u.error)throw u.error;let c=await u.json(),h=null!=(n=u.headers.get("x-total-count"))?n:0,d=null!=(o=null==(a=u.headers.get("link"))?void 0:a.split(","))?o:[];return d.length>0&&(d.forEach(e=>{let t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),r=JSON.parse(e.split(";")[1].split("=")[1]);l[`${r}Page`]=t}),l.total=parseInt(h)),{data:Object.assign(Object.assign({},c),l),error:null}}catch(e){if(rU(e))return{data:{users:[]},error:e};throw e}}async getUserById(e){id(e);try{return await iw(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:iE})}catch(e){if(rU(e))return{data:{user:null},error:e};throw e}}async updateUserById(e,t){id(e);try{return await iw(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:iE})}catch(e){if(rU(e))return{data:{user:null},error:e};throw e}}async deleteUser(e,t=!1){id(e);try{return await iw(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:iE})}catch(e){if(rU(e))return{data:{user:null},error:e};throw e}}async _listFactors(e){id(e.userId);try{let{data:t,error:r}=await iw(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:r}}catch(e){if(rU(e))return{data:null,error:e};throw e}}async _deleteFactor(e){id(e.userId),id(e.id);try{return{data:await iw(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(e){if(rU(e))return{data:null,error:e};throw e}}}let iR={getItem:e=>r5()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{r5()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{r5()&&globalThis.localStorage.removeItem(e)}};function ij(e={}){return{getItem:t=>e[t]||null,setItem:(t,r)=>{e[t]=r},removeItem:t=>{delete e[t]}}}let iI={debug:!!(globalThis&&r5()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"))};class iA extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class iN extends iA{}async function iL(e,t,r){iI.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);let i=new globalThis.AbortController;return t>0&&setTimeout(()=>{i.abort(),iI.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,0===t?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:i.signal},async i=>{if(i){iI.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,i.name);try{return await r()}finally{iI.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,i.name)}}if(0===t)throw iI.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new iN(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(iI.debug)try{let e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(e){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",e)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await r()}))}if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}let i$={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:rN,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function iM(e,t,r){return await r()}class iD{constructor(e){var t,r;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=iD.nextInstanceID,iD.nextInstanceID+=1,this.instanceID>0&&r6()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");let i=Object.assign(Object.assign({},i$),e);if(this.logDebugMessages=!!i.debug,"function"==typeof i.debug&&(this.logger=i.debug),this.persistSession=i.persistSession,this.storageKey=i.storageKey,this.autoRefreshToken=i.autoRefreshToken,this.admin=new iC({url:i.url,headers:i.headers,fetch:i.fetch}),this.url=i.url,this.headers=i.headers,this.fetch=r9(i.fetch),this.lock=i.lock||iM,this.detectSessionInUrl=i.detectSessionInUrl,this.flowType=i.flowType,this.hasCustomAuthorizationHeader=i.hasCustomAuthorizationHeader,i.lock?this.lock=i.lock:r6()&&(null==(t=null==globalThis?void 0:globalThis.navigator)?void 0:t.locks)?this.lock=iL:this.lock=iM,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?i.storage?this.storage=i.storage:r5()?this.storage=iR:(this.memoryStorage={},this.storage=ij(this.memoryStorage)):(this.memoryStorage={},this.storage=ij(this.memoryStorage)),r6()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(e){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",e)}null==(r=this.broadcastChannel)||r.addEventListener("message",async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)})}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${rA}) ${new Date().toISOString()}`,...e),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var e;try{let t=function(e){let t={},r=new URL(e);if(r.hash&&"#"===r.hash[0])try{new URLSearchParams(r.hash.substring(1)).forEach((e,r)=>{t[r]=e})}catch(e){}return r.searchParams.forEach((e,r)=>{t[r]=e}),t}(window.location.href),r="none";if(this._isImplicitGrantCallback(t)?r="implicit":await this._isPKCECallback(t)&&(r="pkce"),r6()&&this.detectSessionInUrl&&"none"!==r){let{data:i,error:s}=await this._getSessionFromURL(t,r);if(s){if(this._debug("#_initialize()","error detecting session from URL",s),rU(s)&&"AuthImplicitGrantRedirectError"===s.name){let t=null==(e=s.details)?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:s}}return await this._removeSession(),{error:s}}let{session:n,redirectType:a}=i;return this._debug("#_initialize()","detected session in URL",n,"redirect type",a),await this._saveSession(n),setTimeout(async()=>{"recovery"===a?await this._notifyAllSubscribers("PASSWORD_RECOVERY",n):await this._notifyAllSubscribers("SIGNED_IN",n)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(e){if(rU(e))return{error:e};return{error:new rB("Unexpected error during initialization",e)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,r,i;try{let{data:s,error:n}=await iw(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!=(r=null==(t=null==e?void 0:e.options)?void 0:t.data)?r:{},gotrue_meta_security:{captcha_token:null==(i=null==e?void 0:e.options)?void 0:i.captchaToken}},xform:i_});if(n||!s)return{data:{user:null,session:null},error:n};let a=s.session,o=s.user;return s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:o,session:a},error:null}}catch(e){if(rU(e))return{data:{user:null,session:null},error:e};throw e}}async signUp(e){var t,r,i;try{let s;if("email"in e){let{email:r,password:i,options:n}=e,a=null,o=null;"pkce"===this.flowType&&([a,o]=await iu(this.storage,this.storageKey)),s=await iw(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==n?void 0:n.emailRedirectTo,body:{email:r,password:i,data:null!=(t=null==n?void 0:n.data)?t:{},gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken},code_challenge:a,code_challenge_method:o},xform:i_})}else if("phone"in e){let{phone:t,password:n,options:a}=e;s=await iw(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:t,password:n,data:null!=(r=null==a?void 0:a.data)?r:{},channel:null!=(i=null==a?void 0:a.channel)?i:"sms",gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken}},xform:i_})}else throw new rF("You must provide either an email or phone number and a password");let{data:n,error:a}=s;if(a||!n)return{data:{user:null,session:null},error:a};let o=n.session,l=n.user;return n.session&&(await this._saveSession(n.session),await this._notifyAllSubscribers("SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(e){if(rU(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithPassword(e){try{let t;if("email"in e){let{email:r,password:i,options:s}=e;t=await iw(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:r,password:i,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken}},xform:iS})}else if("phone"in e){let{phone:r,password:i,options:s}=e;t=await iw(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:r,password:i,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken}},xform:iS})}else throw new rF("You must provide either an email or phone number and a password");let{data:r,error:i}=t;if(i)return{data:{user:null,session:null},error:i};if(!r||!r.session||!r.user)return{data:{user:null,session:null},error:new rW};return r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:i}}catch(e){if(rU(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOAuth(e){var t,r,i,s;return await this._handleProviderSignIn(e.provider,{redirectTo:null==(t=e.options)?void 0:t.redirectTo,scopes:null==(r=e.options)?void 0:r.scopes,queryParams:null==(i=e.options)?void 0:i.queryParams,skipBrowserRedirect:null==(s=e.options)?void 0:s.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async signInWithWeb3(e){let{chain:t}=e;if("solana"===t)return await this.signInWithSolana(e);throw Error(`@supabase/auth-js: Unsupported chain "${t}"`)}async signInWithSolana(e){var t,r,i,s,n,a,o,l,u,c,h,d;let p,f;if("message"in e)p=e.message,f=e.signature;else{let h,{chain:d,wallet:g,statement:m,options:b}=e;if(r6())if("object"==typeof g)h=g;else{let e=window;if("solana"in e&&"object"==typeof e.solana&&("signIn"in e.solana&&"function"==typeof e.solana.signIn||"signMessage"in e.solana&&"function"==typeof e.solana.signMessage))h=e.solana;else throw Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}else{if("object"!=typeof g||!(null==b?void 0:b.url))throw Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");h=g}let v=new URL(null!=(t=null==b?void 0:b.url)?t:window.location.href);if("signIn"in h&&h.signIn){let e,t=await h.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},null==b?void 0:b.signInWithSolana),{version:"1",domain:v.host,uri:v.href}),m?{statement:m}:null));if(Array.isArray(t)&&t[0]&&"object"==typeof t[0])e=t[0];else if(t&&"object"==typeof t&&"signedMessage"in t&&"signature"in t)e=t;else throw Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in e&&"signature"in e&&("string"==typeof e.signedMessage||e.signedMessage instanceof Uint8Array)&&e.signature instanceof Uint8Array)p="string"==typeof e.signedMessage?e.signedMessage:new TextDecoder().decode(e.signedMessage),f=e.signature;else throw Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in h)||"function"!=typeof h.signMessage||!("publicKey"in h)||"object"!=typeof h||!h.publicKey||!("toBase58"in h.publicKey)||"function"!=typeof h.publicKey.toBase58)throw Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");p=[`${v.host} wants you to sign in with your Solana account:`,h.publicKey.toBase58(),...m?["",m,""]:[""],"Version: 1",`URI: ${v.href}`,`Issued At: ${null!=(i=null==(r=null==b?void 0:b.signInWithSolana)?void 0:r.issuedAt)?i:new Date().toISOString()}`,...(null==(s=null==b?void 0:b.signInWithSolana)?void 0:s.notBefore)?[`Not Before: ${b.signInWithSolana.notBefore}`]:[],...(null==(n=null==b?void 0:b.signInWithSolana)?void 0:n.expirationTime)?[`Expiration Time: ${b.signInWithSolana.expirationTime}`]:[],...(null==(a=null==b?void 0:b.signInWithSolana)?void 0:a.chainId)?[`Chain ID: ${b.signInWithSolana.chainId}`]:[],...(null==(o=null==b?void 0:b.signInWithSolana)?void 0:o.nonce)?[`Nonce: ${b.signInWithSolana.nonce}`]:[],...(null==(l=null==b?void 0:b.signInWithSolana)?void 0:l.requestId)?[`Request ID: ${b.signInWithSolana.requestId}`]:[],...(null==(c=null==(u=null==b?void 0:b.signInWithSolana)?void 0:u.resources)?void 0:c.length)?["Resources",...b.signInWithSolana.resources.map(e=>`- ${e}`)]:[]].join("\n");let e=await h.signMessage(new TextEncoder().encode(p),"utf8");if(!e||!(e instanceof Uint8Array))throw Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");f=e}}try{let{data:t,error:r}=await iw(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:p,signature:function(e){let t=[],r={queue:0,queuedBits:0},i=e=>{t.push(e)};return e.forEach(e=>r1(e,r,i)),r1(null,r,i),t.join("")}(f)},(null==(h=e.options)?void 0:h.captchaToken)?{gotrue_meta_security:{captcha_token:null==(d=e.options)?void 0:d.captchaToken}}:null),xform:i_});if(r)throw r;if(!t||!t.session||!t.user)return{data:{user:null,session:null},error:new rW};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign({},t),error:r}}catch(e){if(rU(e))return{data:{user:null,session:null},error:e};throw e}}async _exchangeCodeForSession(e){let t=await ie(this.storage,`${this.storageKey}-code-verifier`),[r,i]=(null!=t?t:"").split("/");try{let{data:t,error:s}=await iw(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:r},xform:i_});if(await it(this.storage,`${this.storageKey}-code-verifier`),s)throw s;if(!t||!t.session||!t.user)return{data:{user:null,session:null,redirectType:null},error:new rW};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!=i?i:null}),error:s}}catch(e){if(rU(e))return{data:{user:null,session:null,redirectType:null},error:e};throw e}}async signInWithIdToken(e){try{let{options:t,provider:r,token:i,access_token:s,nonce:n}=e,{data:a,error:o}=await iw(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:i,access_token:s,nonce:n,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:i_});if(o)return{data:{user:null,session:null},error:o};if(!a||!a.session||!a.user)return{data:{user:null,session:null},error:new rW};return a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",a.session)),{data:a,error:o}}catch(e){if(rU(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOtp(e){var t,r,i,s,n;try{if("email"in e){let{email:i,options:s}=e,n=null,a=null;"pkce"===this.flowType&&([n,a]=await iu(this.storage,this.storageKey));let{error:o}=await iw(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:i,data:null!=(t=null==s?void 0:s.data)?t:{},create_user:null==(r=null==s?void 0:s.shouldCreateUser)||r,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken},code_challenge:n,code_challenge_method:a},redirectTo:null==s?void 0:s.emailRedirectTo});return{data:{user:null,session:null},error:o}}if("phone"in e){let{phone:t,options:r}=e,{data:a,error:o}=await iw(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:t,data:null!=(i=null==r?void 0:r.data)?i:{},create_user:null==(s=null==r?void 0:r.shouldCreateUser)||s,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken},channel:null!=(n=null==r?void 0:r.channel)?n:"sms"}});return{data:{user:null,session:null,messageId:null==a?void 0:a.message_id},error:o}}throw new rF("You must provide either an email or phone number.")}catch(e){if(rU(e))return{data:{user:null,session:null},error:e};throw e}}async verifyOtp(e){var t,r;try{let i,s;"options"in e&&(i=null==(t=e.options)?void 0:t.redirectTo,s=null==(r=e.options)?void 0:r.captchaToken);let{data:n,error:a}=await iw(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:s}}),redirectTo:i,xform:i_});if(a)throw a;if(!n)throw Error("An error occurred on token verification.");let o=n.session,l=n.user;return(null==o?void 0:o.access_token)&&(await this._saveSession(o),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(e){if(rU(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithSSO(e){var t,r,i;try{let s=null,n=null;return"pkce"===this.flowType&&([s,n]=await iu(this.storage,this.storageKey)),await iw(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!=(r=null==(t=e.options)?void 0:t.redirectTo)?r:void 0}),(null==(i=null==e?void 0:e.options)?void 0:i.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:s,code_challenge_method:n}),headers:this.headers,xform:ik})}catch(e){if(rU(e))return{data:null,error:e};throw e}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{let{data:{session:t},error:r}=e;if(r)throw r;if(!t)throw new rV;let{error:i}=await iw(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:i}})}catch(e){if(rU(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{let t=`${this.url}/resend`;if("email"in e){let{email:r,type:i,options:s}=e,{error:n}=await iw(this.fetch,"POST",t,{headers:this.headers,body:{email:r,type:i,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken}},redirectTo:null==s?void 0:s.emailRedirectTo});return{data:{user:null,session:null},error:n}}if("phone"in e){let{phone:r,type:i,options:s}=e,{data:n,error:a}=await iw(this.fetch,"POST",t,{headers:this.headers,body:{phone:r,type:i,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken}}});return{data:{user:null,session:null,messageId:null==n?void 0:n.message_id},error:a}}throw new rF("You must provide either an email or phone number and a type")}catch(e){if(rU(e))return{data:{user:null,session:null},error:e};throw e}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async e=>e))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){let e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),r=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await r}catch(e){}})()),r}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;let e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(e){}})()),await e;this.pendingInLock.length;){let e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{let t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",Error().stack);try{let e=null,t=await ie(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};let r=!!e.expires_at&&1e3*e.expires_at-Date.now()<9e4;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",e.expires_at),!r){if(this.storage.isServer){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,r,i)=>(t||"user"!==r||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,r,i))})}return{data:{session:e},error:null}}let{session:i,error:s}=await this._callRefreshToken(e.refresh_token);if(s)return{data:{session:null},error:s};return{data:{session:i},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){return e?await this._getUser(e):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(e){try{if(e)return await iw(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:iE});return await this._useSession(async e=>{var t,r,i;let{data:s,error:n}=e;if(n)throw n;return(null==(t=s.session)?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await iw(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!=(i=null==(r=s.session)?void 0:r.access_token)?i:void 0,xform:iE}):{data:{user:null},error:new rV}})}catch(e){if(rU(e))return rU(e)&&"AuthSessionMissingError"===e.name&&(await this._removeSession(),await it(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:e};throw e}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e,t={}){try{return await this._useSession(async r=>{let{data:i,error:s}=r;if(s)throw s;if(!i.session)throw new rV;let n=i.session,a=null,o=null;"pkce"===this.flowType&&null!=e.email&&([a,o]=await iu(this.storage,this.storageKey));let{data:l,error:u}=await iw(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:a,code_challenge_method:o}),jwt:n.access_token,xform:iE});if(u)throw u;return n.user=l.user,await this._saveSession(n),await this._notifyAllSubscribers("USER_UPDATED",n),{data:{user:n.user},error:null}})}catch(e){if(rU(e))return{data:{user:null},error:e};throw e}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new rV;let t=Date.now()/1e3,r=t,i=!0,s=null,{payload:n}=ii(e.access_token);if(n.exp&&(i=(r=n.exp)<=t),i){let{session:t,error:r}=await this._callRefreshToken(e.refresh_token);if(r)return{data:{user:null,session:null},error:r};if(!t)return{data:{user:null,session:null},error:null};s=t}else{let{data:i,error:n}=await this._getUser(e.access_token);if(n)throw n;s={access_token:e.access_token,refresh_token:e.refresh_token,user:i.user,token_type:"bearer",expires_in:r-t,expires_at:r},await this._saveSession(s),await this._notifyAllSubscribers("SIGNED_IN",s)}return{data:{user:s.user,session:s},error:null}}catch(e){if(rU(e))return{data:{session:null,user:null},error:e};throw e}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var r;if(!e){let{data:i,error:s}=t;if(s)throw s;e=null!=(r=i.session)?r:void 0}if(!(null==e?void 0:e.refresh_token))throw new rV;let{session:i,error:s}=await this._callRefreshToken(e.refresh_token);return s?{data:{user:null,session:null},error:s}:i?{data:{user:i.user,session:i},error:null}:{data:{user:null,session:null},error:null}})}catch(e){if(rU(e))return{data:{user:null,session:null},error:e};throw e}}async _getSessionFromURL(e,t){try{if(!r6())throw new rH("No browser detected.");if(e.error||e.error_description||e.error_code)throw new rH(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new rG("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new rH("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new rG("No code detected.");let{data:t,error:r}=await this._exchangeCodeForSession(e.code);if(r)throw r;let i=new URL(window.location.href);return i.searchParams.delete("code"),window.history.replaceState(window.history.state,"",i.toString()),{data:{session:t.session,redirectType:null},error:null}}let{provider_token:r,provider_refresh_token:i,access_token:s,refresh_token:n,expires_in:a,expires_at:o,token_type:l}=e;if(!s||!a||!n||!l)throw new rH("No session defined in URL");let u=Math.round(Date.now()/1e3),c=parseInt(a),h=u+c;o&&(h=parseInt(o));let d=h-u;1e3*d<=3e4&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${d}s, should have been closer to ${c}s`);let p=h-c;u-p>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",p,h,u):u-p<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",p,h,u);let{data:f,error:g}=await this._getUser(s);if(g)throw g;let m={provider_token:r,provider_refresh_token:i,access_token:s,expires_in:c,expires_at:h,refresh_token:n,token_type:l,user:f.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:m,redirectType:e.type},error:null}}catch(e){if(rU(e))return{data:{session:null,redirectType:null},error:e};throw e}}_isImplicitGrantCallback(e){return!!(e.access_token||e.error_description)}async _isPKCECallback(e){let t=await ie(this.storage,`${this.storageKey}-code-verifier`);return!!(e.code&&t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut({scope:e}={scope:"global"}){return await this._useSession(async t=>{var r;let{data:i,error:s}=t;if(s)return{error:s};let n=null==(r=i.session)?void 0:r.access_token;if(n){let{error:t}=await this.admin.signOut(n,e);if(t&&!(rU(t)&&"AuthApiError"===t.name&&(404===t.status||401===t.status||403===t.status)))return{error:t}}return"others"!==e&&(await this._removeSession(),await it(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){let t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}),r={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,r),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})})(),{data:{subscription:r}}}async _emitInitialSession(e){return await this._useSession(async t=>{var r,i;try{let{data:{session:i},error:s}=t;if(s)throw s;await (null==(r=this.stateChangeEmitters.get(e))?void 0:r.callback("INITIAL_SESSION",i)),this._debug("INITIAL_SESSION","callback id",e,"session",i)}catch(t){await (null==(i=this.stateChangeEmitters.get(e))?void 0:i.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",t),console.error(t)}})}async resetPasswordForEmail(e,t={}){let r=null,i=null;"pkce"===this.flowType&&([r,i]=await iu(this.storage,this.storageKey,!0));try{return await iw(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:r,code_challenge_method:i,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(e){if(rU(e))return{data:null,error:e};throw e}}async getUserIdentities(){var e;try{let{data:t,error:r}=await this.getUser();if(r)throw r;return{data:{identities:null!=(e=t.user.identities)?e:[]},error:null}}catch(e){if(rU(e))return{data:null,error:e};throw e}}async linkIdentity(e){var t;try{let{data:r,error:i}=await this._useSession(async t=>{var r,i,s,n,a;let{data:o,error:l}=t;if(l)throw l;let u=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null==(r=e.options)?void 0:r.redirectTo,scopes:null==(i=e.options)?void 0:i.scopes,queryParams:null==(s=e.options)?void 0:s.queryParams,skipBrowserRedirect:!0});return await iw(this.fetch,"GET",u,{headers:this.headers,jwt:null!=(a=null==(n=o.session)?void 0:n.access_token)?a:void 0})});if(i)throw i;return!r6()||(null==(t=e.options)?void 0:t.skipBrowserRedirect)||window.location.assign(null==r?void 0:r.url),{data:{provider:e.provider,url:null==r?void 0:r.url},error:null}}catch(t){if(rU(t))return{data:{provider:e.provider,url:null},error:t};throw t}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var r,i;let{data:s,error:n}=t;if(n)throw n;return await iw(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!=(i=null==(r=s.session)?void 0:r.access_token)?i:void 0})})}catch(e){if(rU(e))return{data:null,error:e};throw e}}async _refreshAccessToken(e){let t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{var r,i;let s=Date.now();return await (r=async r=>(r>0&&await is(200*Math.pow(2,r-1)),this._debug(t,"refreshing attempt",r),await iw(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:i_})),i=(e,t)=>{let r=200*Math.pow(2,e);return t&&rJ(t)&&Date.now()+r-s<3e4},new Promise((e,t)=>{(async()=>{for(let s=0;s<1/0;s++)try{let t=await r(s);if(!i(s,null,t))return void e(t)}catch(e){if(!i(s,e))return void t(e)}})()}))}catch(e){if(this._debug(t,"error",e),rU(e))return{data:{session:null,user:null},error:e};throw e}finally{this._debug(t,"end")}}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){let r=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",r),r6()&&!t.skipBrowserRedirect&&window.location.assign(r),{data:{provider:e,url:r},error:null}}async _recoverAndRefresh(){var e;let t="#_recoverAndRefresh()";this._debug(t,"begin");try{let r=await ie(this.storage,this.storageKey);if(this._debug(t,"session from storage",r),!this._isValidSession(r)){this._debug(t,"session is not valid"),null!==r&&await this._removeSession();return}let i=(null!=(e=r.expires_at)?e:1/0)*1e3-Date.now()<9e4;if(this._debug(t,`session has${i?"":" not"} expired with margin of 90000s`),i){if(this.autoRefreshToken&&r.refresh_token){let{error:e}=await this._callRefreshToken(r.refresh_token);e&&(console.error(e),rJ(e)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",r)}catch(e){this._debug(t,"error",e),console.error(e);return}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,r;if(!e)throw new rV;if(this.refreshingDeferred)return this.refreshingDeferred.promise;let i=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(i,"begin");try{this.refreshingDeferred=new ir;let{data:t,error:r}=await this._refreshAccessToken(e);if(r)throw r;if(!t.session)throw new rV;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);let i={session:t.session,error:null};return this.refreshingDeferred.resolve(i),i}catch(e){if(this._debug(i,"error",e),rU(e)){let r={session:null,error:e};return rJ(e)||await this._removeSession(),null==(t=this.refreshingDeferred)||t.resolve(r),r}throw null==(r=this.refreshingDeferred)||r.reject(e),e}finally{this.refreshingDeferred=null,this._debug(i,"end")}}async _notifyAllSubscribers(e,t,r=!0){let i=`#_notifyAllSubscribers(${e})`;this._debug(i,"begin",t,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:e,session:t});let i=[],s=Array.from(this.stateChangeEmitters.values()).map(async r=>{try{await r.callback(e,t)}catch(e){i.push(e)}});if(await Promise.all(s),i.length>0){for(let e=0;e<i.length;e+=1)console.error(i[e]);throw i[0]}}finally{this._debug(i,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await r7(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await it(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");let e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&r6()&&(null==window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(e){console.error("removing visibilitychange callback failed",e)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");let e=setInterval(()=>this._autoRefreshTokenTick(),3e4);this.autoRefreshTicker=e,e&&"object"==typeof e&&"function"==typeof e.unref?e.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");let e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{let e=Date.now();try{return await this._useSession(async t=>{let{data:{session:r}}=t;if(!r||!r.refresh_token||!r.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");let i=Math.floor((1e3*r.expires_at-e)/3e4);this._debug("#_autoRefreshTokenTick()",`access token expires in ${i} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),i<=3&&await this._callRefreshToken(r.refresh_token)})}catch(e){console.error("Auto refresh tick failed with error. This is likely a transient error.",e)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(e.isAcquireTimeout||e instanceof iA)this._debug("auto refresh token tick lock not available");else throw e}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!r6()||!(null==window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null==window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){let t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{if("visible"!==document.visibilityState)return void this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");await this._recoverAndRefresh()}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,r){let i=[`provider=${encodeURIComponent(t)}`];if((null==r?void 0:r.redirectTo)&&i.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),(null==r?void 0:r.scopes)&&i.push(`scopes=${encodeURIComponent(r.scopes)}`),"pkce"===this.flowType){let[e,t]=await iu(this.storage,this.storageKey),r=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(t)}`});i.push(r.toString())}if(null==r?void 0:r.queryParams){let e=new URLSearchParams(r.queryParams);i.push(e.toString())}return(null==r?void 0:r.skipBrowserRedirect)&&i.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${e}?${i.join("&")}`}async _unenroll(e){try{return await this._useSession(async t=>{var r;let{data:i,error:s}=t;return s?{data:null,error:s}:await iw(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null==(r=null==i?void 0:i.session)?void 0:r.access_token})})}catch(e){if(rU(e))return{data:null,error:e};throw e}}async _enroll(e){try{return await this._useSession(async t=>{var r,i;let{data:s,error:n}=t;if(n)return{data:null,error:n};let a=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:o,error:l}=await iw(this.fetch,"POST",`${this.url}/factors`,{body:a,headers:this.headers,jwt:null==(r=null==s?void 0:s.session)?void 0:r.access_token});return l?{data:null,error:l}:("totp"===e.factorType&&(null==(i=null==o?void 0:o.totp)?void 0:i.qr_code)&&(o.totp.qr_code=`data:image/svg+xml;utf-8,${o.totp.qr_code}`),{data:o,error:null})})}catch(e){if(rU(e))return{data:null,error:e};throw e}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;let{data:i,error:s}=t;if(s)return{data:null,error:s};let{data:n,error:a}=await iw(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null==(r=null==i?void 0:i.session)?void 0:r.access_token});return a?{data:null,error:a}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+n.expires_in},n)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",n),{data:n,error:a})})}catch(e){if(rU(e))return{data:null,error:e};throw e}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;let{data:i,error:s}=t;return s?{data:null,error:s}:await iw(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null==(r=null==i?void 0:i.session)?void 0:r.access_token})})}catch(e){if(rU(e))return{data:null,error:e};throw e}})}async _challengeAndVerify(e){let{data:t,error:r}=await this._challenge({factorId:e.factorId});return r?{data:null,error:r}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){let{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};let r=(null==e?void 0:e.factors)||[],i=r.filter(e=>"totp"===e.factor_type&&"verified"===e.status),s=r.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:r,totp:i,phone:s},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,r;let{data:{session:i},error:s}=e;if(s)return{data:null,error:s};if(!i)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};let{payload:n}=ii(i.access_token),a=null;n.aal&&(a=n.aal);let o=a;return(null!=(r=null==(t=i.user.factors)?void 0:t.filter(e=>"verified"===e.status))?r:[]).length>0&&(o="aal2"),{data:{currentLevel:a,nextLevel:o,currentAuthenticationMethods:n.amr||[]},error:null}}))}async fetchJwk(e,t={keys:[]}){let r=t.keys.find(t=>t.kid===e);if(r||(r=this.jwks.keys.find(t=>t.kid===e))&&this.jwks_cached_at+6e5>Date.now())return r;let{data:i,error:s}=await iw(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(s)throw s;if(!i.keys||0===i.keys.length)throw new rY("JWKS is empty");if(this.jwks=i,this.jwks_cached_at=Date.now(),!(r=i.keys.find(t=>t.kid===e)))throw new rY("No matching signing key found in JWKS");return r}async getClaims(e,t={keys:[]}){try{let i=e;if(!i){let{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};i=e.session.access_token}let{header:s,payload:n,signature:a,raw:{header:o,payload:l}}=ii(i);var r=n.exp;if(!r)throw Error("Missing exp claim");if(r<=Math.floor(Date.now()/1e3))throw Error("JWT has expired");if(!s.kid||"HS256"===s.alg||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){let{error:e}=await this.getUser(i);if(e)throw e;return{data:{claims:n,header:s,signature:a},error:null}}let u=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw Error("Invalid alg claim")}}(s.alg),c=await this.fetchJwk(s.kid,t),h=await crypto.subtle.importKey("jwk",c,u,!0,["verify"]);if(!await crypto.subtle.verify(u,h,a,function(e){let t=[];return function(e,t){for(let r=0;r<e.length;r+=1){let i=e.charCodeAt(r);if(i>55295&&i<=56319){let t=(i-55296)*1024&65535;i=(e.charCodeAt(r+1)-56320&65535|t)+65536,r+=1}!function(e,t){if(e<=127)return t(e);if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}(i,t)}}(e,e=>t.push(e)),new Uint8Array(t)}(`${o}.${l}`)))throw new rY("Invalid JWT signature");return{data:{claims:n,header:s,signature:a},error:null}}catch(e){if(rU(e))return{data:null,error:e};throw e}}}iD.nextInstanceID=0;let iU=iD;class iq extends iU{constructor(e){super(e)}}class iB{constructor(e,t,r){var i,s,n;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw Error("supabaseUrl is required.");if(!t)throw Error("supabaseKey is required.");let a=new URL(function(e){return e.endsWith("/")?e:e+"/"}(e));this.realtimeUrl=new URL("realtime/v1",a),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",a),this.storageUrl=new URL("storage/v1",a),this.functionsUrl=new URL("functions/v1",a);let o=`sb-${a.hostname.split(".")[0]}-auth-token`,l=function(e,t){var r,i;let{db:s,auth:n,realtime:a,global:o}=e,{db:l,auth:u,realtime:c,global:h}=t,d={db:Object.assign(Object.assign({},l),s),auth:Object.assign(Object.assign({},u),n),realtime:Object.assign(Object.assign({},c),a),global:Object.assign(Object.assign(Object.assign({},h),o),{headers:Object.assign(Object.assign({},null!=(r=null==h?void 0:h.headers)?r:{}),null!=(i=null==o?void 0:o.headers)?i:{})}),accessToken:()=>{var e,t,r,i;return e=this,t=void 0,i=function*(){return""},new(r=void 0,r=Promise)(function(s,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})}};return e.accessToken?d.accessToken=e.accessToken:delete d.accessToken,d}(null!=r?r:{},{db:rT,realtime:rP,auth:Object.assign(Object.assign({},rx),{storageKey:o}),global:rO});this.storageKey=null!=(i=l.auth.storageKey)?i:"",this.headers=null!=(s=l.global.headers)?s:{},l.accessToken?(this.accessToken=l.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!=(n=l.auth)?n:{},this.headers,l.global.fetch),this.fetch=rI(t,this._getAccessToken.bind(this),l.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},l.realtime)),this.rest=new tM(new URL("rest/v1",a).href,{headers:this.headers,schema:l.db.schema,fetch:this.fetch}),l.accessToken||this._listenForAuthEvents()}get functions(){return new t$(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new rE(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},r={}){return this.rest.rpc(e,t,r)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t,r,i,s,n;return r=this,i=void 0,s=void 0,n=function*(){if(this.accessToken)return yield this.accessToken();let{data:r}=yield this.auth.getSession();return null!=(t=null==(e=r.session)?void 0:e.access_token)?t:null},new(s||(s=Promise))(function(e,t){function a(e){try{l(n.next(e))}catch(e){t(e)}}function o(e){try{l(n.throw(e))}catch(e){t(e)}}function l(t){var r;t.done?e(t.value):((r=t.value)instanceof s?r:new s(function(e){e(r)})).then(a,o)}l((n=n.apply(r,i||[])).next())})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:i,storageKey:s,flowType:n,lock:a,debug:o},l,u){let c={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new iq({url:this.authUrl.href,headers:Object.assign(Object.assign({},c),l),storageKey:s,autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:i,flowType:n,lock:a,debug:o,fetch:u,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new t8(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)})}_handleTokenChanged(e,t,r){("TOKEN_REFRESHED"===e||"SIGNED_IN"===e)&&this.changedAccessToken!==r?this.changedAccessToken=r:"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0)}}let iz=(e,t,r)=>new iB(e,t,r);r(280),"undefined"==typeof URLPattern||URLPattern;var iV=r(196);new WeakMap;let iW="function"==typeof iV.unstable_postpone;function iF(e,t){return`Route ${e} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}if(!1===function(e){return e.includes("needs to bail out of prerendering at this point because it used")&&e.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}(iF("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});async function iH(e){let t=et.next({request:{headers:e.headers}}),r=function(e,t,r){if(!e||!t)throw Error(`Your project's URL and Key are required to create a Supabase client!

Check your Supabase project's API settings to find these values

https://supabase.com/dashboard/project/_/settings/api`);let{storage:i,getAll:s,setAll:n,setItems:a,removedItems:o}=function(e,t){let r,i,s=e.cookies??null,n=e.cookieEncoding,a={},o={};if(s)if("get"in s){let e=async e=>{let t=e.flatMap(e=>[e,...Array.from({length:5}).map((t,r)=>`${e}.${r}`)]),r=[];for(let e=0;e<t.length;e+=1){let i=await s.get(t[e]);(i||"string"==typeof i)&&r.push({name:t[e],value:i})}return r};if(r=async t=>await e(t),"set"in s&&"remove"in s)i=async e=>{for(let t=0;t<e.length;t+=1){let{name:r,value:i,options:n}=e[t];i?await s.set(r,i,n):await s.remove(r,n)}};else if(t)i=async()=>{console.warn("@supabase/ssr: createServerClient was configured without set and remove cookie methods, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness. Consider switching to the getAll and setAll cookie methods instead of get, set and remove which are deprecated and can be difficult to use correctly.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring a getAll and setAll cookie method (deprecated: alternatively both get, set and remove can be used)")}else if("getAll"in s)if(r=async()=>await s.getAll(),"setAll"in s)i=s.setAll;else if(t)i=async()=>{console.warn("@supabase/ssr: createServerClient was configured without the setAll cookie method, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring both getAll and setAll cookie methods (deprecated: alternatively both get, set and remove can be used)");else throw Error(`@supabase/ssr: ${t?"createServerClient":"createBrowserClient"} requires configuring getAll and setAll cookie methods (deprecated: alternatively use get, set and remove).${tv()?" As this is called in a browser runtime, consider removing the cookies option object to use the document.cookie API automatically.":""}`);else if(!t&&tv()){let e=()=>{let e=(0,tb.qg)(document.cookie);return Object.keys(e).map(t=>({name:t,value:e[t]??""}))};r=()=>e(),i=e=>{e.forEach(({name:e,value:t,options:r})=>{document.cookie=(0,tb.lK)(e,t,r)})}}else if(t)throw Error("@supabase/ssr: createServerClient must be initialized with cookie options that specify getAll and setAll functions (deprecated, not recommended: alternatively use get, set and remove)");else r=()=>[],i=()=>{throw Error("@supabase/ssr: createBrowserClient in non-browser runtimes (including Next.js pre-rendering mode) was not initialized cookie options that specify getAll and setAll functions (deprecated: alternatively use get, set and remove), but they were needed")};return t?{getAll:r,setAll:i,setItems:a,removedItems:o,storage:{isServer:!0,getItem:async e=>{if("string"==typeof a[e])return a[e];if(o[e])return null;let t=await r([e]),i=await tE(e,async e=>{let r=t?.find(({name:t})=>t===e)||null;return r?r.value:null});if(!i)return null;let s=i;return"string"==typeof i&&i.startsWith(tC)&&(s=tP(i.substring(tC.length))),s},setItem:async(t,s)=>{t.endsWith("-code-verifier")&&await tR({getAll:r,setAll:i,setItems:{[t]:s},removedItems:{}},{cookieOptions:e?.cookieOptions??null,cookieEncoding:n}),a[t]=s,delete o[t]},removeItem:async e=>{delete a[e],o[e]=!0}}}:{getAll:r,setAll:i,setItems:a,removedItems:o,storage:{isServer:!1,getItem:async e=>{let t=await r([e]),i=await tE(e,async e=>{let r=t?.find(({name:t})=>t===e)||null;return r?r.value:null});if(!i)return null;let s=i;return i.startsWith(tC)&&(s=tP(i.substring(tC.length))),s},setItem:async(t,s)=>{let a=await r([t]),o=new Set((a?.map(({name:e})=>e)||[]).filter(e=>t_(e,t))),l=s;"base64url"===n&&(l=tC+tx(s));let u=tS(t,l);u.forEach(({name:e})=>{o.delete(e)});let c={...tw,...e?.cookieOptions,maxAge:0},h={...tw,...e?.cookieOptions,maxAge:tw.maxAge};delete c.name,delete h.name;let d=[...[...o].map(e=>({name:e,value:"",options:c})),...u.map(({name:e,value:t})=>({name:e,value:t,options:h}))];d.length>0&&await i(d)},removeItem:async t=>{let s=await r([t]),n=(s?.map(({name:e})=>e)||[]).filter(e=>t_(e,t)),a={...tw,...e?.cookieOptions,maxAge:0};delete a.name,n.length>0&&await i(n.map(e=>({name:e,value:"",options:a})))}}}}({...r,cookieEncoding:r?.cookieEncoding??"base64url"},!0),l=iz(e,t,{...r,global:{...r?.global,headers:{...r?.global?.headers,"X-Client-Info":"supabase-ssr/0.6.1 createServerClient"}},auth:{...r?.cookieOptions?.name?{storageKey:r.cookieOptions.name}:null,...r?.auth,flowType:"pkce",autoRefreshToken:!1,detectSessionInUrl:!1,persistSession:!0,storage:i}});return l.auth.onAuthStateChange(async e=>{(Object.keys(a).length>0||Object.keys(o).length>0)&&("SIGNED_IN"===e||"TOKEN_REFRESHED"===e||"USER_UPDATED"===e||"PASSWORD_RECOVERY"===e||"SIGNED_OUT"===e||"MFA_CHALLENGE_VERIFIED"===e)&&await tR({getAll:s,setAll:n,setItems:a,removedItems:o},{cookieOptions:r?.cookieOptions??null,cookieEncoding:r?.cookieEncoding??"base64url"})}),l}("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(r,i,s){e.cookies.set({name:r,value:i,...s}),(t=et.next({request:{headers:e.headers}})).cookies.set({name:r,value:i,...s})},remove(r,i){e.cookies.set({name:r,value:"",...i}),(t=et.next({request:{headers:e.headers}})).cookies.set({name:r,value:"",...i})}}}),i=e.nextUrl.pathname,s=["/","/pricing","/auth/signin","/auth/signup","/auth/callback","/auth/verify-email","/checkout","/api/stripe/webhooks"].some(e=>i===e||i.startsWith(e)),n=["/api/stripe/webhooks","/api/system-status","/api/debug"].some(e=>i.startsWith(e));if(s||n)return t;let{data:{session:a}}=await r.auth.getSession();if(!a){let t=new URL("/auth/signin",e.url);return t.searchParams.set("redirectTo",i),et.redirect(t)}if(i.startsWith("/dashboard")||i.startsWith("/playground")||i.startsWith("/logs")||i.startsWith("/my-models")||i.startsWith("/api-keys")||i.startsWith("/configurations"))try{let{data:t}=await r.from("user_profiles").select("subscription_status, subscription_tier").eq("id",a.user.id).single();if(!t||"active"!==t.subscription_status){let t=new URL("/pricing",e.url);return t.searchParams.set("checkout","true"),t.searchParams.set("message","subscription_required"),et.redirect(t)}}catch(r){let t=new URL("/pricing",e.url);return t.searchParams.set("checkout","true"),t.searchParams.set("message","subscription_check_failed"),et.redirect(t)}return t}RegExp(`\\n\\s+at __next_metadata_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_viewport_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_outlet_boundary__[\\n\\s]`),new WeakMap;let iG={matcher:["/((?!_next/static|_next/image|favicon.ico|public/).*)"]},iK=(Object.values({NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401}),{...m}),iJ=iK.middleware||iK.default,iX="/src/middleware";if("function"!=typeof iJ)throw Object.defineProperty(Error(`The Middleware "${iX}" must export a \`middleware\` or a \`default\` function`),"__NEXT_ERROR_CODE",{value:"E120",enumerable:!1,configurable:!0});function iY(e){return tm({...e,page:iX,handler:async(...e)=>{try{return await iJ(...e)}catch(s){let t=e[0],r=new URL(t.url),i=r.pathname+r.search;throw await y(s,{path:i,method:t.method,headers:Object.fromEntries(t.headers.entries())},{routerKind:"Pages Router",routePath:"/middleware",routeType:"middleware",revalidateReason:void 0}),s}}})}},861:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let s=i(r(279));class n extends s.default{select(e){let t=!1,r=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:r,foreignTable:i,referencedTable:s=i}={}){let n=s?`${s}.order`:"order",a=this.url.searchParams.get(n);return this.url.searchParams.set(n,`${a?`${a},`:""}${e}.${t?"asc":"desc"}${void 0===r?"":r?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:r=t}={}){let i=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(i,`${e}`),this}range(e,t,{foreignTable:r,referencedTable:i=r}={}){let s=void 0===i?"offset":`${i}.offset`,n=void 0===i?"limit":`${i}.limit`;return this.url.searchParams.set(s,`${e}`),this.url.searchParams.set(n,`${t-e+1}`),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:r=!1,buffers:i=!1,wal:s=!1,format:n="text"}={}){var a;let o=[e?"analyze":null,t?"verbose":null,r?"settings":null,i?"buffers":null,s?"wal":null].filter(Boolean).join("|"),l=null!=(a=this.headers.Accept)?a:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${n}; for="${l}"; options=${o};`,this}rollback(){var e;return(null!=(e=this.headers.Prefer)?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}t.default=n},890:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var s={},n=t.split(i),a=(r||{}).decode||e,o=0;o<n.length;o++){var l=n[o],u=l.indexOf("=");if(!(u<0)){var c=l.substr(0,u).trim(),h=l.substr(++u,l.length).trim();'"'==h[0]&&(h=h.slice(1,-1)),void 0==s[c]&&(s[c]=function(e,t){try{return t(e)}catch(t){return e}}(h,a))}}return s},t.serialize=function(e,t,i){var n=i||{},a=n.encode||r;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!s.test(e))throw TypeError("argument name is invalid");var o=a(t);if(o&&!s.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=n.maxAge){var u=n.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(n.domain){if(!s.test(n.domain))throw TypeError("option domain is invalid");l+="; Domain="+n.domain}if(n.path){if(!s.test(n.path))throw TypeError("option path is invalid");l+="; Path="+n.path}if(n.expires){if("function"!=typeof n.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+n.expires.toUTCString()}if(n.httpOnly&&(l+="; HttpOnly"),n.secure&&(l+="; Secure"),n.sameSite)switch("string"==typeof n.sameSite?n.sameSite.toLowerCase():n.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,i=/; */,s=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},905:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{interceptTestApis:function(){return n},wrapRequestHandler:function(){return a}});let i=r(201),s=r(552);function n(){return(0,s.interceptFetch)(r.g.fetch)}function a(e){return(t,r)=>(0,i.withRequest)(t,s.reader,()=>e(t,r))}},956:(e,t,r)=>{(()=>{"use strict";var t={491:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ContextAPI=void 0;let i=r(223),s=r(172),n=r(930),a="context",o=new i.NoopContextManager;class l{constructor(){}static getInstance(){return this._instance||(this._instance=new l),this._instance}setGlobalContextManager(e){return(0,s.registerGlobal)(a,e,n.DiagAPI.instance())}active(){return this._getContextManager().active()}with(e,t,r,...i){return this._getContextManager().with(e,t,r,...i)}bind(e,t){return this._getContextManager().bind(e,t)}_getContextManager(){return(0,s.getGlobal)(a)||o}disable(){this._getContextManager().disable(),(0,s.unregisterGlobal)(a,n.DiagAPI.instance())}}t.ContextAPI=l},930:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagAPI=void 0;let i=r(56),s=r(912),n=r(957),a=r(172);class o{constructor(){function e(e){return function(...t){let r=(0,a.getGlobal)("diag");if(r)return r[e](...t)}}let t=this;t.setLogger=(e,r={logLevel:n.DiagLogLevel.INFO})=>{var i,o,l;if(e===t){let e=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!=(i=e.stack)?i:e.message),!1}"number"==typeof r&&(r={logLevel:r});let u=(0,a.getGlobal)("diag"),c=(0,s.createLogLevelDiagLogger)(null!=(o=r.logLevel)?o:n.DiagLogLevel.INFO,e);if(u&&!r.suppressOverrideMessage){let e=null!=(l=Error().stack)?l:"<failed to generate stacktrace>";u.warn(`Current logger will be overwritten from ${e}`),c.warn(`Current logger will overwrite one already registered from ${e}`)}return(0,a.registerGlobal)("diag",c,t,!0)},t.disable=()=>{(0,a.unregisterGlobal)("diag",t)},t.createComponentLogger=e=>new i.DiagComponentLogger(e),t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}static instance(){return this._instance||(this._instance=new o),this._instance}}t.DiagAPI=o},653:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MetricsAPI=void 0;let i=r(660),s=r(172),n=r(930),a="metrics";class o{constructor(){}static getInstance(){return this._instance||(this._instance=new o),this._instance}setGlobalMeterProvider(e){return(0,s.registerGlobal)(a,e,n.DiagAPI.instance())}getMeterProvider(){return(0,s.getGlobal)(a)||i.NOOP_METER_PROVIDER}getMeter(e,t,r){return this.getMeterProvider().getMeter(e,t,r)}disable(){(0,s.unregisterGlobal)(a,n.DiagAPI.instance())}}t.MetricsAPI=o},181:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PropagationAPI=void 0;let i=r(172),s=r(874),n=r(194),a=r(277),o=r(369),l=r(930),u="propagation",c=new s.NoopTextMapPropagator;class h{constructor(){this.createBaggage=o.createBaggage,this.getBaggage=a.getBaggage,this.getActiveBaggage=a.getActiveBaggage,this.setBaggage=a.setBaggage,this.deleteBaggage=a.deleteBaggage}static getInstance(){return this._instance||(this._instance=new h),this._instance}setGlobalPropagator(e){return(0,i.registerGlobal)(u,e,l.DiagAPI.instance())}inject(e,t,r=n.defaultTextMapSetter){return this._getGlobalPropagator().inject(e,t,r)}extract(e,t,r=n.defaultTextMapGetter){return this._getGlobalPropagator().extract(e,t,r)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,i.unregisterGlobal)(u,l.DiagAPI.instance())}_getGlobalPropagator(){return(0,i.getGlobal)(u)||c}}t.PropagationAPI=h},997:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceAPI=void 0;let i=r(172),s=r(846),n=r(139),a=r(607),o=r(930),l="trace";class u{constructor(){this._proxyTracerProvider=new s.ProxyTracerProvider,this.wrapSpanContext=n.wrapSpanContext,this.isSpanContextValid=n.isSpanContextValid,this.deleteSpan=a.deleteSpan,this.getSpan=a.getSpan,this.getActiveSpan=a.getActiveSpan,this.getSpanContext=a.getSpanContext,this.setSpan=a.setSpan,this.setSpanContext=a.setSpanContext}static getInstance(){return this._instance||(this._instance=new u),this._instance}setGlobalTracerProvider(e){let t=(0,i.registerGlobal)(l,this._proxyTracerProvider,o.DiagAPI.instance());return t&&this._proxyTracerProvider.setDelegate(e),t}getTracerProvider(){return(0,i.getGlobal)(l)||this._proxyTracerProvider}getTracer(e,t){return this.getTracerProvider().getTracer(e,t)}disable(){(0,i.unregisterGlobal)(l,o.DiagAPI.instance()),this._proxyTracerProvider=new s.ProxyTracerProvider}}t.TraceAPI=u},277:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deleteBaggage=t.setBaggage=t.getActiveBaggage=t.getBaggage=void 0;let i=r(491),s=(0,r(780).createContextKey)("OpenTelemetry Baggage Key");function n(e){return e.getValue(s)||void 0}t.getBaggage=n,t.getActiveBaggage=function(){return n(i.ContextAPI.getInstance().active())},t.setBaggage=function(e,t){return e.setValue(s,t)},t.deleteBaggage=function(e){return e.deleteValue(s)}},993:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaggageImpl=void 0;class r{constructor(e){this._entries=e?new Map(e):new Map}getEntry(e){let t=this._entries.get(e);if(t)return Object.assign({},t)}getAllEntries(){return Array.from(this._entries.entries()).map(([e,t])=>[e,t])}setEntry(e,t){let i=new r(this._entries);return i._entries.set(e,t),i}removeEntry(e){let t=new r(this._entries);return t._entries.delete(e),t}removeEntries(...e){let t=new r(this._entries);for(let r of e)t._entries.delete(r);return t}clear(){return new r}}t.BaggageImpl=r},830:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataSymbol=void 0,t.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataFromString=t.createBaggage=void 0;let i=r(930),s=r(993),n=r(830),a=i.DiagAPI.instance();t.createBaggage=function(e={}){return new s.BaggageImpl(new Map(Object.entries(e)))},t.baggageEntryMetadataFromString=function(e){return"string"!=typeof e&&(a.error(`Cannot create baggage metadata from unknown type: ${typeof e}`),e=""),{__TYPE__:n.baggageEntryMetadataSymbol,toString:()=>e}}},67:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.context=void 0,t.context=r(491).ContextAPI.getInstance()},223:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopContextManager=void 0;let i=r(780);class s{active(){return i.ROOT_CONTEXT}with(e,t,r,...i){return t.call(r,...i)}bind(e,t){return t}enable(){return this}disable(){return this}}t.NoopContextManager=s},780:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ROOT_CONTEXT=t.createContextKey=void 0,t.createContextKey=function(e){return Symbol.for(e)};class r{constructor(e){let t=this;t._currentContext=e?new Map(e):new Map,t.getValue=e=>t._currentContext.get(e),t.setValue=(e,i)=>{let s=new r(t._currentContext);return s._currentContext.set(e,i),s},t.deleteValue=e=>{let i=new r(t._currentContext);return i._currentContext.delete(e),i}}}t.ROOT_CONTEXT=new r},506:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diag=void 0,t.diag=r(930).DiagAPI.instance()},56:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagComponentLogger=void 0;let i=r(172);class s{constructor(e){this._namespace=e.namespace||"DiagComponentLogger"}debug(...e){return n("debug",this._namespace,e)}error(...e){return n("error",this._namespace,e)}info(...e){return n("info",this._namespace,e)}warn(...e){return n("warn",this._namespace,e)}verbose(...e){return n("verbose",this._namespace,e)}}function n(e,t,r){let s=(0,i.getGlobal)("diag");if(s)return r.unshift(t),s[e](...r)}t.DiagComponentLogger=s},972:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagConsoleLogger=void 0;let r=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class i{constructor(){for(let e=0;e<r.length;e++)this[r[e].n]=function(e){return function(...t){if(console){let r=console[e];if("function"!=typeof r&&(r=console.log),"function"==typeof r)return r.apply(console,t)}}}(r[e].c)}}t.DiagConsoleLogger=i},912:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createLogLevelDiagLogger=void 0;let i=r(957);t.createLogLevelDiagLogger=function(e,t){function r(r,i){let s=t[r];return"function"==typeof s&&e>=i?s.bind(t):function(){}}return e<i.DiagLogLevel.NONE?e=i.DiagLogLevel.NONE:e>i.DiagLogLevel.ALL&&(e=i.DiagLogLevel.ALL),t=t||{},{error:r("error",i.DiagLogLevel.ERROR),warn:r("warn",i.DiagLogLevel.WARN),info:r("info",i.DiagLogLevel.INFO),debug:r("debug",i.DiagLogLevel.DEBUG),verbose:r("verbose",i.DiagLogLevel.VERBOSE)}}},957:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagLogLevel=void 0,function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(t.DiagLogLevel||(t.DiagLogLevel={}))},172:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.unregisterGlobal=t.getGlobal=t.registerGlobal=void 0;let i=r(200),s=r(521),n=r(130),a=s.VERSION.split(".")[0],o=Symbol.for(`opentelemetry.js.api.${a}`),l=i._globalThis;t.registerGlobal=function(e,t,r,i=!1){var n;let a=l[o]=null!=(n=l[o])?n:{version:s.VERSION};if(!i&&a[e]){let t=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);return r.error(t.stack||t.message),!1}if(a.version!==s.VERSION){let t=Error(`@opentelemetry/api: Registration of version v${a.version} for ${e} does not match previously registered API v${s.VERSION}`);return r.error(t.stack||t.message),!1}return a[e]=t,r.debug(`@opentelemetry/api: Registered a global for ${e} v${s.VERSION}.`),!0},t.getGlobal=function(e){var t,r;let i=null==(t=l[o])?void 0:t.version;if(i&&(0,n.isCompatible)(i))return null==(r=l[o])?void 0:r[e]},t.unregisterGlobal=function(e,t){t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${s.VERSION}.`);let r=l[o];r&&delete r[e]}},130:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isCompatible=t._makeCompatibilityCheck=void 0;let i=r(521),s=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function n(e){let t=new Set([e]),r=new Set,i=e.match(s);if(!i)return()=>!1;let n={major:+i[1],minor:+i[2],patch:+i[3],prerelease:i[4]};if(null!=n.prerelease)return function(t){return t===e};function a(e){return r.add(e),!1}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;let i=e.match(s);if(!i)return a(e);let o={major:+i[1],minor:+i[2],patch:+i[3],prerelease:i[4]};if(null!=o.prerelease||n.major!==o.major)return a(e);if(0===n.major)return n.minor===o.minor&&n.patch<=o.patch?(t.add(e),!0):a(e);return n.minor<=o.minor?(t.add(e),!0):a(e)}}t._makeCompatibilityCheck=n,t.isCompatible=n(i.VERSION)},886:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.metrics=void 0,t.metrics=r(653).MetricsAPI.getInstance()},901:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValueType=void 0,function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(t.ValueType||(t.ValueType={}))},102:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createNoopMeter=t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=t.NOOP_OBSERVABLE_GAUGE_METRIC=t.NOOP_OBSERVABLE_COUNTER_METRIC=t.NOOP_UP_DOWN_COUNTER_METRIC=t.NOOP_HISTOGRAM_METRIC=t.NOOP_COUNTER_METRIC=t.NOOP_METER=t.NoopObservableUpDownCounterMetric=t.NoopObservableGaugeMetric=t.NoopObservableCounterMetric=t.NoopObservableMetric=t.NoopHistogramMetric=t.NoopUpDownCounterMetric=t.NoopCounterMetric=t.NoopMetric=t.NoopMeter=void 0;class r{constructor(){}createHistogram(e,r){return t.NOOP_HISTOGRAM_METRIC}createCounter(e,r){return t.NOOP_COUNTER_METRIC}createUpDownCounter(e,r){return t.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(e,r){return t.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(e,r){return t.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(e,r){return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(e,t){}removeBatchObservableCallback(e){}}t.NoopMeter=r;class i{}t.NoopMetric=i;class s extends i{add(e,t){}}t.NoopCounterMetric=s;class n extends i{add(e,t){}}t.NoopUpDownCounterMetric=n;class a extends i{record(e,t){}}t.NoopHistogramMetric=a;class o{addCallback(e){}removeCallback(e){}}t.NoopObservableMetric=o;class l extends o{}t.NoopObservableCounterMetric=l;class u extends o{}t.NoopObservableGaugeMetric=u;class c extends o{}t.NoopObservableUpDownCounterMetric=c,t.NOOP_METER=new r,t.NOOP_COUNTER_METRIC=new s,t.NOOP_HISTOGRAM_METRIC=new a,t.NOOP_UP_DOWN_COUNTER_METRIC=new n,t.NOOP_OBSERVABLE_COUNTER_METRIC=new l,t.NOOP_OBSERVABLE_GAUGE_METRIC=new u,t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new c,t.createNoopMeter=function(){return t.NOOP_METER}},660:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NOOP_METER_PROVIDER=t.NoopMeterProvider=void 0;let i=r(102);class s{getMeter(e,t,r){return i.NOOP_METER}}t.NoopMeterProvider=s,t.NOOP_METER_PROVIDER=new s},200:function(e,t,r){var i=this&&this.__createBinding||(Object.create?function(e,t,r,i){void 0===i&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){void 0===i&&(i=r),e[i]=t[r]}),s=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||i(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),s(r(46),t)},651:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t._globalThis=void 0,t._globalThis="object"==typeof globalThis?globalThis:r.g},46:function(e,t,r){var i=this&&this.__createBinding||(Object.create?function(e,t,r,i){void 0===i&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){void 0===i&&(i=r),e[i]=t[r]}),s=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||i(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),s(r(651),t)},939:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.propagation=void 0,t.propagation=r(181).PropagationAPI.getInstance()},874:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTextMapPropagator=void 0;class r{inject(e,t){}extract(e,t){return e}fields(){return[]}}t.NoopTextMapPropagator=r},194:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTextMapSetter=t.defaultTextMapGetter=void 0,t.defaultTextMapGetter={get(e,t){if(null!=e)return e[t]},keys:e=>null==e?[]:Object.keys(e)},t.defaultTextMapSetter={set(e,t,r){null!=e&&(e[t]=r)}}},845:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.trace=void 0,t.trace=r(997).TraceAPI.getInstance()},403:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NonRecordingSpan=void 0;let i=r(476);class s{constructor(e=i.INVALID_SPAN_CONTEXT){this._spanContext=e}spanContext(){return this._spanContext}setAttribute(e,t){return this}setAttributes(e){return this}addEvent(e,t){return this}setStatus(e){return this}updateName(e){return this}end(e){}isRecording(){return!1}recordException(e,t){}}t.NonRecordingSpan=s},614:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracer=void 0;let i=r(491),s=r(607),n=r(403),a=r(139),o=i.ContextAPI.getInstance();class l{startSpan(e,t,r=o.active()){var i;if(null==t?void 0:t.root)return new n.NonRecordingSpan;let l=r&&(0,s.getSpanContext)(r);return"object"==typeof(i=l)&&"string"==typeof i.spanId&&"string"==typeof i.traceId&&"number"==typeof i.traceFlags&&(0,a.isSpanContextValid)(l)?new n.NonRecordingSpan(l):new n.NonRecordingSpan}startActiveSpan(e,t,r,i){let n,a,l;if(arguments.length<2)return;2==arguments.length?l=t:3==arguments.length?(n=t,l=r):(n=t,a=r,l=i);let u=null!=a?a:o.active(),c=this.startSpan(e,n,u),h=(0,s.setSpan)(u,c);return o.with(h,l,void 0,c)}}t.NoopTracer=l},124:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracerProvider=void 0;let i=r(614);class s{getTracer(e,t,r){return new i.NoopTracer}}t.NoopTracerProvider=s},125:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracer=void 0;let i=new(r(614)).NoopTracer;class s{constructor(e,t,r,i){this._provider=e,this.name=t,this.version=r,this.options=i}startSpan(e,t,r){return this._getTracer().startSpan(e,t,r)}startActiveSpan(e,t,r,i){let s=this._getTracer();return Reflect.apply(s.startActiveSpan,s,arguments)}_getTracer(){if(this._delegate)return this._delegate;let e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):i}}t.ProxyTracer=s},846:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracerProvider=void 0;let i=r(125),s=new(r(124)).NoopTracerProvider;class n{getTracer(e,t,r){var s;return null!=(s=this.getDelegateTracer(e,t,r))?s:new i.ProxyTracer(this,e,t,r)}getDelegate(){var e;return null!=(e=this._delegate)?e:s}setDelegate(e){this._delegate=e}getDelegateTracer(e,t,r){var i;return null==(i=this._delegate)?void 0:i.getTracer(e,t,r)}}t.ProxyTracerProvider=n},996:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SamplingDecision=void 0,function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(t.SamplingDecision||(t.SamplingDecision={}))},607:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSpanContext=t.setSpanContext=t.deleteSpan=t.setSpan=t.getActiveSpan=t.getSpan=void 0;let i=r(780),s=r(403),n=r(491),a=(0,i.createContextKey)("OpenTelemetry Context Key SPAN");function o(e){return e.getValue(a)||void 0}function l(e,t){return e.setValue(a,t)}t.getSpan=o,t.getActiveSpan=function(){return o(n.ContextAPI.getInstance().active())},t.setSpan=l,t.deleteSpan=function(e){return e.deleteValue(a)},t.setSpanContext=function(e,t){return l(e,new s.NonRecordingSpan(t))},t.getSpanContext=function(e){var t;return null==(t=o(e))?void 0:t.spanContext()}},325:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceStateImpl=void 0;let i=r(564);class s{constructor(e){this._internalState=new Map,e&&this._parse(e)}set(e,t){let r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r}unset(e){let t=this._clone();return t._internalState.delete(e),t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce((e,t)=>(e.push(t+"="+this.get(t)),e),[]).join(",")}_parse(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce((e,t)=>{let r=t.trim(),s=r.indexOf("=");if(-1!==s){let n=r.slice(0,s),a=r.slice(s+1,t.length);(0,i.validateKey)(n)&&(0,i.validateValue)(a)&&e.set(n,a)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let e=new s;return e._internalState=new Map(this._internalState),e}}t.TraceStateImpl=s},564:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validateValue=t.validateKey=void 0;let r="[_0-9a-z-*/]",i=`[a-z]${r}{0,255}`,s=`[a-z0-9]${r}{0,240}@[a-z]${r}{0,13}`,n=RegExp(`^(?:${i}|${s})$`),a=/^[ -~]{0,255}[!-~]$/,o=/,|=/;t.validateKey=function(e){return n.test(e)},t.validateValue=function(e){return a.test(e)&&!o.test(e)}},98:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createTraceState=void 0;let i=r(325);t.createTraceState=function(e){return new i.TraceStateImpl(e)}},476:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.INVALID_SPAN_CONTEXT=t.INVALID_TRACEID=t.INVALID_SPANID=void 0;let i=r(475);t.INVALID_SPANID="0000000000000000",t.INVALID_TRACEID="00000000000000000000000000000000",t.INVALID_SPAN_CONTEXT={traceId:t.INVALID_TRACEID,spanId:t.INVALID_SPANID,traceFlags:i.TraceFlags.NONE}},357:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanKind=void 0,function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(t.SpanKind||(t.SpanKind={}))},139:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.wrapSpanContext=t.isSpanContextValid=t.isValidSpanId=t.isValidTraceId=void 0;let i=r(476),s=r(403),n=/^([0-9a-f]{32})$/i,a=/^[0-9a-f]{16}$/i;function o(e){return n.test(e)&&e!==i.INVALID_TRACEID}function l(e){return a.test(e)&&e!==i.INVALID_SPANID}t.isValidTraceId=o,t.isValidSpanId=l,t.isSpanContextValid=function(e){return o(e.traceId)&&l(e.spanId)},t.wrapSpanContext=function(e){return new s.NonRecordingSpan(e)}},847:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanStatusCode=void 0,function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(t.SpanStatusCode||(t.SpanStatusCode={}))},475:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceFlags=void 0,function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(t.TraceFlags||(t.TraceFlags={}))},521:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=void 0,t.VERSION="1.6.0"}},i={};function s(e){var r=i[e];if(void 0!==r)return r.exports;var n=i[e]={exports:{}},a=!0;try{t[e].call(n.exports,n,n.exports,s),a=!1}finally{a&&delete i[e]}return n.exports}s.ab="//";var n={};(()=>{Object.defineProperty(n,"__esModule",{value:!0}),n.trace=n.propagation=n.metrics=n.diag=n.context=n.INVALID_SPAN_CONTEXT=n.INVALID_TRACEID=n.INVALID_SPANID=n.isValidSpanId=n.isValidTraceId=n.isSpanContextValid=n.createTraceState=n.TraceFlags=n.SpanStatusCode=n.SpanKind=n.SamplingDecision=n.ProxyTracerProvider=n.ProxyTracer=n.defaultTextMapSetter=n.defaultTextMapGetter=n.ValueType=n.createNoopMeter=n.DiagLogLevel=n.DiagConsoleLogger=n.ROOT_CONTEXT=n.createContextKey=n.baggageEntryMetadataFromString=void 0;var e=s(369);Object.defineProperty(n,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return e.baggageEntryMetadataFromString}});var t=s(780);Object.defineProperty(n,"createContextKey",{enumerable:!0,get:function(){return t.createContextKey}}),Object.defineProperty(n,"ROOT_CONTEXT",{enumerable:!0,get:function(){return t.ROOT_CONTEXT}});var r=s(972);Object.defineProperty(n,"DiagConsoleLogger",{enumerable:!0,get:function(){return r.DiagConsoleLogger}});var i=s(957);Object.defineProperty(n,"DiagLogLevel",{enumerable:!0,get:function(){return i.DiagLogLevel}});var a=s(102);Object.defineProperty(n,"createNoopMeter",{enumerable:!0,get:function(){return a.createNoopMeter}});var o=s(901);Object.defineProperty(n,"ValueType",{enumerable:!0,get:function(){return o.ValueType}});var l=s(194);Object.defineProperty(n,"defaultTextMapGetter",{enumerable:!0,get:function(){return l.defaultTextMapGetter}}),Object.defineProperty(n,"defaultTextMapSetter",{enumerable:!0,get:function(){return l.defaultTextMapSetter}});var u=s(125);Object.defineProperty(n,"ProxyTracer",{enumerable:!0,get:function(){return u.ProxyTracer}});var c=s(846);Object.defineProperty(n,"ProxyTracerProvider",{enumerable:!0,get:function(){return c.ProxyTracerProvider}});var h=s(996);Object.defineProperty(n,"SamplingDecision",{enumerable:!0,get:function(){return h.SamplingDecision}});var d=s(357);Object.defineProperty(n,"SpanKind",{enumerable:!0,get:function(){return d.SpanKind}});var p=s(847);Object.defineProperty(n,"SpanStatusCode",{enumerable:!0,get:function(){return p.SpanStatusCode}});var f=s(475);Object.defineProperty(n,"TraceFlags",{enumerable:!0,get:function(){return f.TraceFlags}});var g=s(98);Object.defineProperty(n,"createTraceState",{enumerable:!0,get:function(){return g.createTraceState}});var m=s(139);Object.defineProperty(n,"isSpanContextValid",{enumerable:!0,get:function(){return m.isSpanContextValid}}),Object.defineProperty(n,"isValidTraceId",{enumerable:!0,get:function(){return m.isValidTraceId}}),Object.defineProperty(n,"isValidSpanId",{enumerable:!0,get:function(){return m.isValidSpanId}});var b=s(476);Object.defineProperty(n,"INVALID_SPANID",{enumerable:!0,get:function(){return b.INVALID_SPANID}}),Object.defineProperty(n,"INVALID_TRACEID",{enumerable:!0,get:function(){return b.INVALID_TRACEID}}),Object.defineProperty(n,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return b.INVALID_SPAN_CONTEXT}});let v=s(67);Object.defineProperty(n,"context",{enumerable:!0,get:function(){return v.context}});let w=s(506);Object.defineProperty(n,"diag",{enumerable:!0,get:function(){return w.diag}});let y=s(886);Object.defineProperty(n,"metrics",{enumerable:!0,get:function(){return y.metrics}});let _=s(939);Object.defineProperty(n,"propagation",{enumerable:!0,get:function(){return _.propagation}});let S=s(845);Object.defineProperty(n,"trace",{enumerable:!0,get:function(){return S.trace}}),n.default={context:v.context,diag:w.diag,metrics:y.metrics,propagation:_.propagation,trace:S.trace}})(),e.exports=n})()},999:e=>{"use strict";e.exports=function(){throw Error("ws does not work in the browser. Browser clients must use the native WebSocket object")}}},e=>{var t=e(e.s=815);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_src/middleware"]=t}]);
//# sourceMappingURL=middleware.js.map
(()=>{var e={};e.id=7637,e.ids=[7637],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10258:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(37413);function i(){return(0,s.jsx)("div",{className:"min-h-screen bg-[#faf8f5] p-6",children:(0,s.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,s.jsxs)("div",{className:"animate-pulse space-y-8",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"h-10 bg-gray-200 rounded w-1/3"}),(0,s.jsx)("div",{className:"h-6 bg-gray-200 rounded w-2/3"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg p-8 space-y-6",children:[(0,s.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,s.jsx)("div",{className:"h-32 bg-gray-200 rounded"}),(0,s.jsx)("div",{className:"h-12 bg-gray-200 rounded w-1/3"})]})]})]})})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},38149:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=t(65239),i=t(48088),n=t(88170),a=t.n(n),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["training",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,79729)),"C:\\RoKey App\\rokey-app\\src\\app\\training\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(t.bind(t,10258)),"C:\\RoKey App\\rokey-app\\src\\app\\training\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\training\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/training/page",pathname:"/training",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},52587:(e,r,t)=>{Promise.resolve().then(t.bind(t,52627))},52627:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(60687),i=t(43210);function n(){let[e,r]=(0,i.useState)([]),[t,n]=(0,i.useState)(""),[a,o]=(0,i.useState)([]),[l,d]=(0,i.useState)(!1),[c,p]=(0,i.useState)(null),[u,m]=(0,i.useState)(null),[x,h]=(0,i.useState)(""),g=e=>{let r={system_instructions:"",examples:[],behavior_guidelines:"",general_instructions:""};for(let t of e.split("\n").filter(e=>e.trim())){let e=t.trim();if(e.startsWith("SYSTEM:"))r.system_instructions+=e.replace("SYSTEM:","").trim()+"\n";else if(e.startsWith("BEHAVIOR:"))r.behavior_guidelines+=e.replace("BEHAVIOR:","").trim()+"\n";else if(e.includes("→")||e.includes("->")){let t=e.includes("→")?"→":"->",s=e.split(t);if(s.length>=2){let e=s[0].trim(),i=s.slice(1).join(t).trim();r.examples.push({input:e,output:i})}}else e.length>0&&(r.general_instructions+=e+"\n")}return r},f=async()=>{if(!t||!x.trim())return void p("Please select an API configuration and provide training prompts.");if(!l){d(!0),p(null),m(null);try{let r=g(x),s=e.find(e=>e.id===t)?.name||"Unknown Config",i={custom_api_config_id:t,name:`${s} Training - ${new Date().toLocaleDateString()}`,description:`Training job for ${s} with ${r.examples.length} examples`,training_data:{processed_prompts:r,raw_prompts:x.trim(),last_prompt_update:new Date().toISOString()},parameters:{training_type:"prompt_engineering",created_via:"training_page",version:"1.0"}},n=await fetch("/api/training/jobs/upsert",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)});if(!n.ok){let e=await n.text();throw Error(`Failed to save training job: ${n.status} ${e}`)}let a=await n.json(),o="updated"===a.operation,l=`${o?"\uD83D\uDD04":"\uD83C\uDF89"} Prompt Engineering ${o?"updated":"completed"} successfully!

Your "${s}" configuration has been ${o?"updated":"enhanced"} with:
• ${r.examples.length} training examples
• Custom system instructions and behavior guidelines

✨ All future chats using this configuration will automatically:
• Follow your training examples
• Apply your behavior guidelines
• Maintain consistent personality and responses

🚀 Try it now in the Playground to see your ${o?"updated":"enhanced"} model in action!

💡 Your training prompts remain here so you can modify them anytime.`;m(l)}catch(e){p(`Failed to create prompt engineering: ${e.message}`)}finally{d(!1)}}};return(0,s.jsx)("div",{className:"min-h-screen bg-[#faf8f5] p-6",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Prompt Engineering"}),(0,s.jsx)("p",{className:"text-lg text-gray-600 max-w-3xl",children:"Create custom prompts to enhance your AI models with specific instructions, behavior guidelines, and examples."})]}),c&&(0,s.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 rounded-xl p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("svg",{className:"w-5 h-5 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,s.jsx)("p",{className:"text-red-800 text-sm font-medium",children:c})]})}),u&&(0,s.jsx)("div",{className:"mb-6 bg-green-50 border border-green-200 rounded-xl p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("svg",{className:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,s.jsx)("p",{className:"text-green-800 text-sm font-medium",children:u})]})}),(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg p-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Create Custom Prompts"}),(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"configSelect",className:"block text-sm font-medium text-gray-700 mb-2",children:"Select API Configuration"}),(0,s.jsxs)("select",{id:"configSelect",value:t,onChange:e=>n(e.target.value),className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm",children:[(0,s.jsx)("option",{value:"",children:"Choose which model to train..."}),e.map(e=>(0,s.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"trainingPrompts",className:"block text-sm font-medium text-gray-700 mb-2",children:"Custom Prompts & Instructions"}),(0,s.jsx)("textarea",{id:"trainingPrompts",value:x,onChange:e=>h(e.target.value),placeholder:`Enter your training prompts using these formats:

SYSTEM: You are a helpful customer service agent for our company
BEHAVIOR: Always be polite and offer solutions

User asks about returns → I'd be happy to help with your return! Let me check our policy for you.
Customer is frustrated → I understand your frustration. Let me see how I can resolve this for you.

General instructions can be written as regular text.`,rows:12,className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm resize-none font-mono"}),(0,s.jsxs)("div",{className:"mt-3 bg-blue-50 border border-blue-200 rounded-lg p-3",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-blue-900 mb-2",children:"Training Format Guide:"}),(0,s.jsxs)("ul",{className:"text-xs text-blue-800 space-y-1",children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"SYSTEM:"})," Core instructions for the AI's role and personality"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"BEHAVIOR:"})," Guidelines for how the AI should behave"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Examples:"}),' Use "User input → Expected response" format']}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"General:"})," Any other instructions written as normal text"]})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center pt-6 border-t border-gray-200",children:[(0,s.jsx)("div",{className:"flex space-x-3",children:(0,s.jsx)("button",{type:"button",className:"btn-secondary",onClick:()=>{confirm("Clear all training prompts?")&&h("")},children:"Clear Form"})}),(0,s.jsx)("button",{type:"button",onClick:f,disabled:!t||!x.trim()||l,className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:l?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Processing Prompts..."]}):"Save Prompts"})]})]})]})]})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79729:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\training\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},92339:(e,r,t)=>{Promise.resolve().then(t.bind(t,79729))},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,2252,1658,7437],()=>t(38149));module.exports=s})();
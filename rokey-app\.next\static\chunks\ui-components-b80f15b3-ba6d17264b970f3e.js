"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9968],{4654:(e,r,a)=>{a.d(r,{f:()=>i});var s=a(95155);a(12115);var t=a(11485);let i=e=>{let{orchestrationComplete:r,onMaximize:a,isCanvasOpen:i,isCanvasMinimized:l}=e;return(0,s.jsxs)("div",{className:"flex justify-start group mb-16 mt-8 ".concat(i&&!l?"-ml-96":""," ").concat(i&&!l?"ml-8":""),children:[(0,s.jsx)("div",{className:"w-7 h-7 rounded-full bg-orange-50 flex items-center justify-center mr-3 mt-1 flex-shrink-0 border border-orange-100",children:(0,s.jsx)("svg",{className:"w-3.5 h-3.5 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,s.jsx)("div",{className:"".concat(i&&!l?"max-w-[80%]":"max-w-[65%]"," relative"),children:(0,s.jsx)("div",{onClick:a,className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-2xl rounded-bl-lg shadow-lg cursor-pointer hover:shadow-xl transition-all duration-300 hover:scale-[1.02] min-w-[320px] ring-2 ring-blue-300/60 hover:ring-blue-300/80 shadow-blue-500/20",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(t.v,{className:"w-6 h-6"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("h3",{className:"font-semibold text-sm",children:"AI Team Collaboration"}),(0,s.jsx)("p",{className:"text-xs opacity-90",children:r?"Completed - Click to view results":"Multi-Role Orchestration in progress"})]}),(0,s.jsxs)("div",{className:"flex-shrink-0",children:[!r&&(0,s.jsxs)("div",{className:"flex space-x-1",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-white rounded-full animate-bounce",style:{animationDelay:"0ms"}}),(0,s.jsx)("div",{className:"w-2 h-2 bg-white rounded-full animate-bounce",style:{animationDelay:"150ms"}}),(0,s.jsx)("div",{className:"w-2 h-2 bg-white rounded-full animate-bounce",style:{animationDelay:"300ms"}})]}),r&&(0,s.jsx)(t.B,{className:"w-5 h-5"})]})]})})})]})}},43456:(e,r,a)=>{a.d(r,{A:()=>d});var s=a(95155),t=a(11518),i=a.n(t),l=a(12115),n=a(10747);let o={initializing:{icon:n.P,text:"Initializing",description:"Starting up systems",bgColor:"bg-gradient-to-r from-slate-50 to-gray-50",iconColor:"text-slate-600",borderColor:"border-slate-200/60",glowColor:"shadow-slate-200/50",gradientFrom:"from-slate-400",gradientTo:"to-gray-400",duration:200},analyzing:{icon:n.$p,text:"Analyzing",description:"Understanding your request",bgColor:"bg-gradient-to-r from-cyan-50 to-blue-50",iconColor:"text-cyan-600",borderColor:"border-cyan-200/60",glowColor:"shadow-cyan-200/50",gradientFrom:"from-cyan-400",gradientTo:"to-blue-400",duration:300},routing:{icon:n.EF,text:"Smart routing",description:"Finding optimal path",bgColor:"bg-gradient-to-r from-indigo-50 to-purple-50",iconColor:"text-indigo-600",borderColor:"border-indigo-200/60",glowColor:"shadow-indigo-200/50",gradientFrom:"from-indigo-400",gradientTo:"to-purple-400",duration:400},complexity_analysis:{icon:n.XL,text:"Analyzing complexity",description:"Evaluating request depth",bgColor:"bg-gradient-to-r from-amber-50 to-yellow-50",iconColor:"text-amber-600",borderColor:"border-amber-200/60",glowColor:"shadow-amber-200/50",gradientFrom:"from-amber-400",gradientTo:"to-yellow-400",duration:500},role_classification:{icon:n.Gg,text:"Assembling specialists",description:"Building expert team",bgColor:"bg-gradient-to-r from-violet-50 to-purple-50",iconColor:"text-violet-600",borderColor:"border-violet-200/60",glowColor:"shadow-violet-200/50",gradientFrom:"from-violet-400",gradientTo:"to-purple-400",duration:600},preparing:{icon:n.DQ,text:"Preparing",description:"Setting up processing",bgColor:"bg-gradient-to-r from-orange-50 to-amber-50",iconColor:"text-orange-600",borderColor:"border-orange-200/60",glowColor:"shadow-orange-200/50",gradientFrom:"from-orange-400",gradientTo:"to-amber-400",duration:300},connecting:{icon:n.nr,text:"Connecting",description:"Establishing AI link",bgColor:"bg-gradient-to-r from-rose-50 to-pink-50",iconColor:"text-rose-600",borderColor:"border-rose-200/60",glowColor:"shadow-rose-200/50",gradientFrom:"from-rose-400",gradientTo:"to-pink-400",duration:400},generating:{icon:n.Y3,text:"Thinking deeply",description:"AI processing in progress",bgColor:"bg-gradient-to-r from-emerald-50 to-teal-50",iconColor:"text-emerald-600",borderColor:"border-emerald-200/60",glowColor:"shadow-emerald-200/50",gradientFrom:"from-emerald-400",gradientTo:"to-teal-400",duration:800},typing:{icon:n.R2,text:"Streaming response",description:"Delivering your answer",bgColor:"bg-gradient-to-r from-green-50 to-emerald-50",iconColor:"text-green-600",borderColor:"border-green-200/60",glowColor:"shadow-green-200/50",gradientFrom:"from-green-400",gradientTo:"to-emerald-400"},finalizing:{icon:n.BZ,text:"Finalizing",description:"Adding finishing touches",bgColor:"bg-gradient-to-r from-teal-50 to-cyan-50",iconColor:"text-teal-600",borderColor:"border-teal-200/60",glowColor:"shadow-teal-200/50",gradientFrom:"from-teal-400",gradientTo:"to-cyan-400",duration:200},complete:{icon:n.Zu,text:"Complete",description:"Response delivered",bgColor:"bg-gradient-to-r from-green-50 to-lime-50",iconColor:"text-green-600",borderColor:"border-green-200/60",glowColor:"shadow-green-200/50",gradientFrom:"from-green-400",gradientTo:"to-lime-400",duration:100}};function d(e){let{currentStage:r,isStreaming:a=!1,className:t="",onStageChange:n,orchestrationStatus:d}=e,[c,m]=(0,l.useState)(r),[g,x]=(0,l.useState)(!1),h=o[c],u=h.icon;return(0,l.useEffect)(()=>{r!==c&&(x(!0),setTimeout(()=>{m(r),x(!1),null==n||n(r)},200))},[r,c,n]),(0,s.jsxs)("div",{className:"jsx-f56d70faa8a01b64 "+"flex justify-start ".concat(t),children:[(0,s.jsxs)("div",{className:"jsx-f56d70faa8a01b64 relative w-6 h-6 rounded-full flex items-center justify-center mr-2.5 mt-0.5 flex-shrink-0",children:[(0,s.jsx)("div",{style:{animation:g?"spin 0.6s linear infinite":"spin 1.2s linear infinite",borderImage:"conic-gradient(from 0deg, transparent 0%, ".concat(h.iconColor.replace("text-","")," 25%, transparent 50%, ").concat(h.iconColor.replace("text-","")," 75%, transparent 100%) 1"),filter:"drop-shadow(0 0 4px rgba(59, 130, 246, 0.5))"},className:"jsx-f56d70faa8a01b64 absolute -inset-1.5 w-9 h-9 rounded-full border-[3px] border-transparent animate-spin"}),(0,s.jsx)("div",{style:{animation:g?"spin 0.8s linear infinite reverse":"spin 1.6s linear infinite reverse",borderImage:"conic-gradient(from 180deg, transparent 0%, ".concat(h.iconColor.replace("text-","")," 30%, transparent 60%, ").concat(h.iconColor.replace("text-","")," 90%, transparent 100%) 1"),opacity:.8},className:"jsx-f56d70faa8a01b64 absolute -inset-1 w-8 h-8 rounded-full border-[2px] border-transparent animate-spin"}),(0,s.jsx)("div",{style:{borderColor:h.iconColor.replace("text-",""),opacity:.6,animation:"pulse 2s ease-in-out infinite"},className:"jsx-f56d70faa8a01b64 absolute -inset-0.5 w-7 h-7 rounded-full border border-transparent animate-pulse"}),(0,s.jsx)("div",{style:{boxShadow:"0 0 12px ".concat(h.iconColor.replace("text-",""),"40, 0 0 24px ").concat(h.iconColor.replace("text-",""),"20")},className:"jsx-f56d70faa8a01b64 "+"relative w-full h-full rounded-full flex items-center justify-center transition-all duration-500 ".concat(h.bgColor," border-2 ").concat(h.borderColor," shadow-lg backdrop-blur-sm"),children:(0,s.jsx)(u,{className:"jsx-f56d70faa8a01b64 "+"w-3.5 h-3.5 transition-all duration-500 ".concat(h.iconColor," ").concat(g?"scale-125 rotate-12":"scale-100"," drop-shadow-lg")})})]}),(0,s.jsxs)("div",{className:"jsx-f56d70faa8a01b64 "+"max-w-[65%] rounded-xl px-3 py-2 transition-all duration-500 ".concat(h.bgColor," ").concat(h.borderColor," border ").concat(h.glowColor," shadow-sm backdrop-blur-sm"),children:[(0,s.jsx)("div",{className:"jsx-f56d70faa8a01b64 flex items-center space-x-1.5",children:(0,s.jsxs)("div",{className:"jsx-f56d70faa8a01b64 transition-all duration-500",children:[(0,s.jsx)("span",{className:"jsx-f56d70faa8a01b64 "+"text-xs font-semibold transition-colors duration-500 ".concat(h.iconColor," tracking-wide"),children:d||h.text}),a&&"typing"===c&&!d&&(0,s.jsx)("span",{className:"jsx-f56d70faa8a01b64 "+"ml-1.5 text-[10px] opacity-80 ".concat(h.iconColor," font-medium"),children:"• Live"}),d&&(0,s.jsx)("span",{className:"jsx-f56d70faa8a01b64 "+"ml-1.5 text-[10px] opacity-80 ".concat(h.iconColor," font-medium"),children:"• Orchestrating"})]})}),("generating"===c||"typing"===c)&&(0,s.jsx)("div",{className:"jsx-f56d70faa8a01b64 mt-2",children:(0,s.jsx)("div",{className:"jsx-f56d70faa8a01b64 h-1 rounded-full overflow-hidden bg-white/30 backdrop-blur-sm border border-white/20",children:(0,s.jsx)("div",{style:{width:"typing"===c?"100%":"60%",animation:"typing"===c?"progressPulse 1.5s ease-in-out infinite, progressShimmer 2s linear infinite":"progressShimmer 2s linear infinite, progressGlow 3s ease-in-out infinite"},className:"jsx-f56d70faa8a01b64 "+"h-full rounded-full transition-all duration-1000 bg-gradient-to-r ".concat(h.gradientFrom," ").concat(h.gradientTo," relative overflow-hidden"),children:(0,s.jsx)("div",{style:{animation:"progressShine 2s linear infinite",transform:"skewX(-20deg)"},className:"jsx-f56d70faa8a01b64 absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent"})})})})]}),(0,s.jsx)(i(),{id:"f56d70faa8a01b64",children:"@-webkit-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-moz-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-o-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-webkit-keyframes progressShine{0%{-webkit-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-webkit-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-moz-keyframes progressShine{0%{-moz-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-moz-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-o-keyframes progressShine{0%{-o-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-o-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@keyframes progressShine{0%{-webkit-transform:translatex(-100%)skewx(-20deg);-moz-transform:translatex(-100%)skewx(-20deg);-o-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-webkit-transform:translatex(300%)skewx(-20deg);-moz-transform:translatex(300%)skewx(-20deg);-o-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-webkit-keyframes progressPulse{0%,100%{opacity:1;-webkit-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-webkit-transform:scaley(1.1);transform:scaley(1.1)}}@-moz-keyframes progressPulse{0%,100%{opacity:1;-moz-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-moz-transform:scaley(1.1);transform:scaley(1.1)}}@-o-keyframes progressPulse{0%,100%{opacity:1;-o-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-o-transform:scaley(1.1);transform:scaley(1.1)}}@keyframes progressPulse{0%,100%{opacity:1;-webkit-transform:scaley(1);-moz-transform:scaley(1);-o-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-webkit-transform:scaley(1.1);-moz-transform:scaley(1.1);-o-transform:scaley(1.1);transform:scaley(1.1)}}@-webkit-keyframes progressGlow{0%,100%{-webkit-filter:brightness(1)drop-shadow(0 0 2px currentColor);filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{-webkit-filter:brightness(1.2)drop-shadow(0 0 6px currentColor);filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@-moz-keyframes progressGlow{0%,100%{filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@-o-keyframes progressGlow{0%,100%{filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@keyframes progressGlow{0%,100%{-webkit-filter:brightness(1)drop-shadow(0 0 2px currentColor);filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{-webkit-filter:brightness(1.2)drop-shadow(0 0 6px currentColor);filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}"})]})}},52399:(e,r,a)=>{a.d(r,{c:()=>l});var s=a(95155),t=a(12115),i=a(60875);let l=e=>{var r,a;let{message:l}=e,n=e=>{switch(e){case"assignment":return(0,s.jsx)(i.fl,{className:"w-3 h-3 text-blue-500"});case"completion":return(0,s.jsx)(i.C1,{className:"w-3 h-3 text-green-500"});case"handoff":return(0,s.jsx)(i.fl,{className:"w-3 h-3 text-purple-500"});default:return null}},o="moderator"===l.sender,d=(e=>{if(!e)return"from-blue-500 to-blue-600";let r=["from-green-500 to-green-600","from-purple-500 to-purple-600","from-orange-500 to-orange-600","from-pink-500 to-pink-600","from-indigo-500 to-indigo-600","from-teal-500 to-teal-600","from-red-500 to-red-600","from-yellow-500 to-yellow-600"];return r[e.split("").reduce((e,r)=>e+r.charCodeAt(0),0)%r.length]})(l.roleId);return(0,s.jsx)("div",{className:"flex ".concat("justify-start"," mb-4"),children:(0,s.jsxs)("div",{className:"flex items-start space-x-3 max-w-[85%]",children:[(0,s.jsx)("div",{className:"flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r ".concat(d," flex items-center justify-center text-white shadow-sm"),children:(r=l.sender,l.roleId,"moderator"===r?(0,s.jsx)(i.BZ,{className:"w-4 h-4"}):(0,s.jsx)(i.YE,{className:"w-4 h-4"}))}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,s.jsx)("span",{className:"text-sm font-semibold ".concat(o?"text-blue-700":"text-gray-700"),children:l.senderName}),n(l.type)&&(0,s.jsx)("div",{className:"flex items-center",children:n(l.type)}),(0,s.jsx)("span",{className:"text-xs text-gray-500",children:l.timestamp.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]}),(0,s.jsx)("div",{className:"inline-block px-4 py-3 rounded-2xl shadow-sm ".concat(o?"bg-blue-50 border border-blue-100":"bg-gray-50 border border-gray-100"," ").concat("completion"===l.type?"border-green-200 bg-green-50":"assignment"===l.type?"border-blue-200 bg-blue-50":"handoff"===l.type?"border-purple-200 bg-purple-50":""),children:(0,s.jsx)("div",{className:"text-sm leading-relaxed ".concat(o?"text-blue-900":"text-gray-800"," ").concat("completion"===l.type?"text-green-900":"assignment"===l.type?"text-blue-900":"handoff"===l.type?"text-purple-900":""),children:l.content.split("\n").map((e,r)=>(0,s.jsxs)(t.Fragment,{children:[e,r<l.content.split("\n").length-1&&(0,s.jsx)("br",{})]},r))})}),"message"!==l.type&&(0,s.jsx)("div",{className:"mt-2",children:(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ".concat("assignment"===l.type?"bg-blue-100 text-blue-800":"completion"===l.type?"bg-green-100 text-green-800":"handoff"===l.type?"bg-purple-100 text-purple-800":"clarification"===l.type?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"),children:["assignment"===l.type&&"\uD83D\uDCCB Task Assignment","completion"===l.type&&"✅ Task Complete","handoff"===l.type&&"\uD83D\uDD04 Handoff","clarification"===l.type&&"❓ Clarification"]})})]})]})})}},52469:(e,r,a)=>{a.d(r,{default:()=>l});var s=a(12115),t=a(35695);let i=["/features","/pricing","/about","/auth/signin","/auth/signup"];function l(){let e=(0,t.useRouter)();return(0,s.useEffect)(()=>{let r=()=>{i.forEach(r=>{e.prefetch(r)})};"requestIdleCallback"in window?window.requestIdleCallback(r,{timeout:2e3}):setTimeout(r,100)},[e]),null}},79112:(e,r,a)=>{a.d(r,{A:()=>n});var s=a(95155),t=a(12115);let i=(0,t.lazy)(()=>Promise.all([a.e(5006),a.e(5928),a.e(4726),a.e(4280),a.e(2548),a.e(8960),a.e(8961),a.e(3084),a.e(3285),a.e(9968),a.e(6060),a.e(3613),a.e(5260),a.e(7525),a.e(3310),a.e(7096),a.e(7455),a.e(678),a.e(8730)]).then(a.bind(a,90882))),l=()=>(0,s.jsxs)("div",{className:"space-y-2 animate-pulse",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-5/6"})]});function n(e){let{content:r,className:a=""}=e;return(0,s.jsx)(t.Suspense,{fallback:(0,s.jsx)(l,{}),children:(0,s.jsx)(i,{content:r,className:a})})}},79958:(e,r,a)=>{a.d(r,{A:()=>t,_:()=>i});var s=a(95155);function t(){return(0,s.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"h-8 bg-gray-200 rounded w-48 mb-2 animate-pulse"}),(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-64 animate-pulse"})]})]})}),(0,s.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 p-6",children:[(0,s.jsx)("div",{className:"h-6 bg-gray-200 rounded w-32 mb-6 animate-pulse"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-2 animate-pulse"}),(0,s.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 mb-2 animate-pulse"}),(0,s.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-2 animate-pulse"}),(0,s.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-12 mb-2 animate-pulse"}),(0,s.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,s.jsxs)("div",{className:"md:col-span-2",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,s.jsx)("div",{className:"h-6 bg-gray-200 rounded animate-pulse"})]})]}),(0,s.jsx)("div",{className:"mt-6",children:(0,s.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})})]}),(0,s.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("div",{className:"h-6 bg-gray-200 rounded w-40 animate-pulse"}),(0,s.jsx)("div",{className:"h-8 bg-gray-200 rounded w-24 animate-pulse"})]}),(0,s.jsx)("div",{className:"space-y-4",children:[1,2,3].map(e=>(0,s.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-gray-200 rounded-lg animate-pulse"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"h-5 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-48 animate-pulse"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"}),(0,s.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16 animate-pulse"}),(0,s.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16 animate-pulse"})]})]}),(0,s.jsxs)("div",{className:"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"h-3 bg-gray-200 rounded w-16 mb-1 animate-pulse"}),(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 animate-pulse"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"h-3 bg-gray-200 rounded w-12 mb-1 animate-pulse"}),(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 animate-pulse"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"h-3 bg-gray-200 rounded w-20 mb-1 animate-pulse"}),(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 animate-pulse"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"h-3 bg-gray-200 rounded w-14 mb-1 animate-pulse"}),(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-18 animate-pulse"})]})]}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-28 mb-2 animate-pulse"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:[1,2,3].map(e=>(0,s.jsx)("div",{className:"h-6 bg-gray-200 rounded-full w-20 animate-pulse"},e))})]})]},e))})]}),(0,s.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("div",{className:"h-6 bg-gray-200 rounded w-36 animate-pulse"}),(0,s.jsx)("div",{className:"h-8 bg-gray-200 rounded w-28 animate-pulse"})]}),(0,s.jsx)("div",{className:"space-y-3",children:[1,2].map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 mb-1 animate-pulse"}),(0,s.jsx)("div",{className:"h-3 bg-gray-200 rounded w-48 animate-pulse"})]}),(0,s.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16 animate-pulse"})]},e))})]})]})}function i(){return(0,s.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"h-6 bg-gray-200 rounded w-40 mb-1 animate-pulse"}),(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-56 animate-pulse"})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[1,2,3,4].map(e=>(0,s.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-1 animate-pulse"}),(0,s.jsx)("div",{className:"h-3 bg-gray-200 rounded w-32 animate-pulse"})]})]})},e))})]})}a(12115)},83434:(e,r,a)=>{a.d(r,{default:()=>h});var s=a(95155),t=a(35695),i=a(22261),l=a(99323),n=a(12115),o=a(95494),d=a(95060),c=a(69903),m=a(42126);function g(e){let{children:r}=e,{isCollapsed:a,collapseSidebar:t}=(0,i.c)(),{isNavigating:n,targetRoute:g,isPageCached:x}=(0,l.bu)()||{isNavigating:!1,targetRoute:null,isPageCached:()=>!1};return(0,m.v)({maxConcurrent:2,idleTimeout:1500,backgroundDelay:3e3}),(0,s.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,s.jsx)("div",{className:"hidden lg:block fixed left-0 top-0 h-full z-40",children:(0,s.jsx)(d.A,{})}),(0,s.jsxs)("div",{className:"lg:hidden fixed inset-0 z-50 ".concat(a?"pointer-events-none":""),children:[(0,s.jsx)("div",{onClick:t,className:"absolute inset-0 bg-black transition-opacity duration-200 ease-out cursor-pointer ".concat(a?"opacity-0":"opacity-50")}),(0,s.jsx)("div",{className:"absolute left-0 top-0 h-full transform transition-transform duration-200 ease-out ".concat(a?"-translate-x-full":"translate-x-0"),children:(0,s.jsx)(d.A,{})})]}),(0,s.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden min-w-0 lg:ml-64",children:[(0,s.jsx)("div",{className:"fixed top-0 right-0 left-0 lg:left-64 z-30",children:(0,s.jsx)(o.A,{})}),(0,s.jsx)("main",{className:"flex-1 overflow-y-auto content-area mt-16",children:(0,s.jsx)("div",{className:"p-4 sm:p-6 lg:p-8 max-w-7xl mx-auto w-full",children:(0,s.jsx)("div",{className:"page-transition",children:n&&g?(0,s.jsx)(c.A,{targetRoute:g,children:r}):r})})})]})]})}function x(e){let{children:r}=e;return(0,s.jsx)(n.Suspense,{fallback:(0,s.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,s.jsx)("div",{className:"hidden lg:block fixed left-0 top-0 h-full w-64 bg-gray-900 animate-pulse"}),(0,s.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden min-w-0 lg:ml-64",children:[(0,s.jsx)("div",{className:"fixed top-0 right-0 left-0 lg:left-64 h-16 bg-white border-b border-gray-200 animate-pulse"}),(0,s.jsx)("main",{className:"flex-1 overflow-y-auto mt-16",children:(0,s.jsx)("div",{className:"p-4 sm:p-6 lg:p-8 max-w-7xl mx-auto w-full",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3 animate-pulse"}),(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 animate-pulse"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((e,r)=>(0,s.jsx)("div",{className:"h-32 bg-gray-200 rounded animate-pulse"},r))})]})})})]})]}),children:(0,s.jsx)(g,{children:r})})}function h(e){let{children:r}=e,a=(0,t.usePathname)();return"/"===a||a.startsWith("/pricing")||a.startsWith("/features")||a.startsWith("/about")||a.startsWith("/auth/")?(0,s.jsx)(s.Fragment,{children:r}):(0,s.jsx)(i.G,{children:(0,s.jsx)(l.i9,{children:(0,s.jsx)(x,{children:r})})})}},90882:(e,r,a)=>{a.r(r),a.d(r,{default:()=>d});var s=a(95155),t=a(28831),i=a(70765),l=a(18730),n=a(15478),o=a(95803);function d(e){let{content:r,className:a=""}=e;return(0,s.jsx)("div",{className:"markdown-content ".concat(a),children:(0,s.jsx)(t.Ay,{remarkPlugins:[i.A],components:{h1:e=>{let{children:r}=e;return(0,s.jsx)("h1",{className:"text-xl font-bold mb-3 mt-4 first:mt-0 text-gray-900",children:r})},h2:e=>{let{children:r}=e;return(0,s.jsx)("h2",{className:"text-lg font-bold mb-2 mt-3 first:mt-0 text-gray-900",children:r})},h3:e=>{let{children:r}=e;return(0,s.jsx)("h3",{className:"text-base font-bold mb-2 mt-3 first:mt-0 text-gray-900",children:r})},h4:e=>{let{children:r}=e;return(0,s.jsx)("h4",{className:"text-sm font-bold mb-1 mt-2 first:mt-0 text-gray-900",children:r})},p:e=>{let{children:r}=e;return(0,s.jsx)("p",{className:"mb-3 last:mb-0 leading-relaxed text-gray-900 break-words",children:r})},strong:e=>{let{children:r}=e;return(0,s.jsx)("strong",{className:"font-bold text-gray-900",children:r})},em:e=>{let{children:r}=e;return(0,s.jsx)("em",{className:"italic text-gray-900",children:r})},ul:e=>{let{children:r}=e;return(0,s.jsx)("ul",{className:"list-disc list-inside mb-3 space-y-1 text-gray-900",children:r})},ol:e=>{let{children:r}=e;return(0,s.jsx)("ol",{className:"list-decimal list-inside mb-3 space-y-1 text-gray-900",children:r})},li:e=>{let{children:r}=e;return(0,s.jsx)("li",{className:"leading-relaxed text-gray-900",children:r})},code:e=>{let{node:r,inline:a,className:t,children:i,...d}=e,c=/language-(\w+)/.exec(t||""),m=c?c[1]:"",g=String(i).replace(/\n$/,"");if(!a)if(m)return(0,s.jsxs)("div",{className:"my-3 rounded-lg overflow-hidden relative group",children:[(0,s.jsx)("div",{className:"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200",children:(0,s.jsx)(o.A,{text:g,variant:"code",size:"sm",title:"Copy code",className:"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50"})}),(0,s.jsx)(l.M,{style:n.bM,language:m,PreTag:"div",className:"text-sm",...d,children:g})]});else return(0,s.jsxs)("div",{className:"my-3 rounded-lg overflow-hidden relative group bg-gray-900 text-gray-100",children:[(0,s.jsx)("div",{className:"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200",children:(0,s.jsx)(o.A,{text:g,variant:"code",size:"sm",title:"Copy code",className:"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50"})}),(0,s.jsx)("pre",{className:"p-4 text-sm font-mono overflow-x-auto",children:(0,s.jsx)("code",{children:g})})]});return(0,s.jsx)("code",{className:"bg-gray-100 text-gray-900 px-1.5 py-0.5 rounded text-sm font-mono",...d,children:i})},blockquote:e=>{let{children:r}=e;return(0,s.jsx)("blockquote",{className:"border-l-4 border-orange-500 pl-4 my-3 italic text-gray-700",children:r})},a:e=>{let{children:r,href:a}=e;return(0,s.jsx)("a",{href:a,target:"_blank",rel:"noopener noreferrer",className:"text-orange-600 hover:text-orange-700 underline transition-colors duration-200",children:r})},table:e=>{let{children:r}=e;return(0,s.jsx)("div",{className:"overflow-x-auto my-3",children:(0,s.jsx)("table",{className:"min-w-full border border-gray-200 rounded-lg",children:r})})},thead:e=>{let{children:r}=e;return(0,s.jsx)("thead",{className:"bg-gray-50",children:r})},tbody:e=>{let{children:r}=e;return(0,s.jsx)("tbody",{className:"divide-y divide-gray-200 bg-white",children:r})},tr:e=>{let{children:r}=e;return(0,s.jsx)("tr",{className:"hover:bg-gray-50",children:r})},th:e=>{let{children:r}=e;return(0,s.jsx)("th",{className:"px-3 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border-b border-gray-200",children:r})},td:e=>{let{children:r}=e;return(0,s.jsx)("td",{className:"px-3 py-2 text-sm text-gray-900 border-b border-gray-200",children:r})},hr:()=>(0,s.jsx)("hr",{className:"my-4 border-gray-200"})},children:r})})}},95565:(e,r,a)=>{a.d(r,{AnalyticsSkeleton:()=>d,ConfigSelectorSkeleton:()=>l,MessageSkeleton:()=>i,MyModelsSkeleton:()=>n,RoutingSetupSkeleton:()=>o});var s=a(95155);a(11518),a(12115);let t=e=>{let{className:r="",variant:a="text",width:t="100%",height:i="1rem",lines:l=1}=e,n="animate-pulse bg-gray-200 rounded",o=()=>{switch(a){case"circular":return"rounded-full";case"rectangular":return"rounded-lg";default:return"rounded"}},d={width:"number"==typeof t?"".concat(t,"px"):t,height:"number"==typeof i?"".concat(i,"px"):i};return l>1?(0,s.jsx)("div",{className:"space-y-2 ".concat(r),children:Array.from({length:l}).map((e,r)=>(0,s.jsx)("div",{className:"".concat(n," ").concat(o()),style:{...d,width:r===l-1?"75%":d.width}},r))}):(0,s.jsx)("div",{className:"".concat(n," ").concat(o()," ").concat(r),style:d})},i=()=>(0,s.jsx)("div",{className:"space-y-6 py-8",children:Array.from({length:3}).map((e,r)=>(0,s.jsx)("div",{className:"flex ".concat(r%2==0?"justify-end":"justify-start"),children:(0,s.jsx)("div",{className:"max-w-3xl p-4 rounded-2xl ".concat(r%2==0?"bg-orange-50":"bg-white border border-gray-200"),children:(0,s.jsx)(t,{lines:3,height:"1rem"})})},r))}),l=()=>(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(t,{variant:"circular",width:32,height:32}),(0,s.jsx)(t,{width:"8rem",height:"1.5rem"})]}),n=()=>(0,s.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(t,{height:"2.5rem",width:"10rem",className:"mb-2"}),(0,s.jsx)(t,{height:"1.25rem",width:"18rem"})]}),(0,s.jsx)(t,{variant:"rectangular",height:"2.5rem",width:"10rem"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((e,r)=>(0,s.jsxs)("div",{className:"card p-6",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)(t,{height:"1.5rem",width:"8rem",className:"mb-2"}),(0,s.jsx)(t,{height:"1rem",width:"12rem"})]}),(0,s.jsx)(t,{variant:"circular",width:32,height:32})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)(t,{height:"0.875rem",width:"4rem"}),(0,s.jsx)(t,{height:"0.875rem",width:"2rem"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)(t,{height:"0.875rem",width:"5rem"}),(0,s.jsx)(t,{height:"0.875rem",width:"3rem"})]})]})]},r))})]}),o=()=>(0,s.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(t,{height:"3rem",width:"16rem",className:"mx-auto mb-4"}),(0,s.jsx)(t,{height:"1.25rem",width:"24rem",className:"mx-auto"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:Array.from({length:4}).map((e,r)=>(0,s.jsxs)("div",{className:"card p-6",children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)(t,{variant:"circular",width:48,height:48,className:"mr-4"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)(t,{height:"1.5rem",width:"10rem",className:"mb-2"}),(0,s.jsx)(t,{height:"1rem",width:"8rem"})]})]}),(0,s.jsx)(t,{lines:3,height:"0.875rem"})]},r))})]}),d=()=>(0,s.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(t,{height:"2.5rem",width:"9rem",className:"mb-2"}),(0,s.jsx)(t,{height:"1.25rem",width:"18rem"})]}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsx)(t,{variant:"rectangular",height:"2.5rem",width:"8rem"}),(0,s.jsx)(t,{variant:"rectangular",height:"2.5rem",width:"6rem"})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:Array.from({length:3}).map((e,r)=>(0,s.jsxs)("div",{className:"card p-6",children:[(0,s.jsx)(t,{height:"1.25rem",width:"6rem",className:"mb-4"}),(0,s.jsx)(t,{height:"2.5rem",width:"5rem",className:"mb-2"}),(0,s.jsx)(t,{height:"1rem",width:"8rem"})]},r))}),(0,s.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"card p-6",children:[(0,s.jsx)(t,{height:"1.5rem",width:"12rem",className:"mb-6"}),(0,s.jsx)(t,{variant:"rectangular",height:"24rem"})]}),(0,s.jsxs)("div",{className:"card p-6",children:[(0,s.jsx)(t,{height:"1.5rem",width:"10rem",className:"mb-6"}),(0,s.jsx)(t,{variant:"rectangular",height:"24rem"})]})]})]})},95803:(e,r,a)=>{a.d(r,{A:()=>l});var s=a(95155),t=a(12115),i=a(76032);function l(e){let{text:r,className:a="",size:l="sm",variant:n="default",title:o="Copy to clipboard"}=e,[d,c]=(0,t.useState)(!1),m=async()=>{try{await navigator.clipboard.writeText(r),c(!0),setTimeout(()=>c(!1),2e3)}catch(a){let e=document.createElement("textarea");e.value=r,document.body.appendChild(e),e.focus(),e.select();try{document.execCommand("copy"),c(!0),setTimeout(()=>c(!1),2e3)}catch(e){}document.body.removeChild(e)}},g={sm:"w-4 h-4",md:"w-5 h-5",lg:"w-6 h-6"};return(0,s.jsx)("button",{onClick:m,className:"\n        ".concat({sm:"p-1.5",md:"p-2",lg:"p-2.5"}[l],"\n        ").concat({default:"text-gray-500 hover:text-gray-700 hover:bg-gray-100/80",code:"text-gray-300 hover:text-white hover:bg-gray-600/80",message:"text-gray-500 hover:text-gray-700 hover:bg-white/20"}[n],"\n        rounded transition-all duration-200 cursor-pointer\n        ").concat(d?"text-green-600":"","\n        ").concat(a,"\n      "),title:d?"Copied!":o,children:d?(0,s.jsx)(i.S,{className:"".concat(g[l]," stroke-2")}):(0,s.jsx)(i.X,{className:"".concat(g[l]," stroke-2")})})}},99030:(e,r,a)=>{a.d(r,{default:()=>i});var s=a(12115),t=a(34962);function i(){let{pageTitle:e}=(0,t.rT)();return(0,s.useEffect)(()=>{"undefined"!=typeof document&&(document.title=e)},[e]),null}}}]);
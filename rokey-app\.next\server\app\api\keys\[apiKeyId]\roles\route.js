(()=>{var e={};e.id=1529,e.ids=[1529],e.modules={507:(e,t,r)=>{"use strict";r.d(t,{Dc:()=>i,p2:()=>s});let s=[{id:"general_chat",name:"General Chat",description:"Handles general conversation, Q&A, and tasks not covered by other specific roles. Often the default fallback."},{id:"logic_reasoning",name:"Logic & Reasoning",description:"For tasks requiring logical deduction, problem-solving, mathematical reasoning, and complex analytical thinking."},{id:"writing",name:"Writing & Content Creation",description:"For all writing tasks, including articles, blog posts, marketing copy, creative content, essays, and more."},{id:"coding_frontend",name:"Coding - Frontend",description:"For generating and assisting with HTML, CSS, JavaScript, and frontend frameworks (React, Vue, Angular, etc.)."},{id:"coding_backend",name:"Coding - Backend",description:"For generating and assisting with server-side logic, APIs, databases, and backend frameworks (Node.js, Python, Java, etc.)."},{id:"research_synthesis",name:"Research & Synthesis",description:"For information retrieval from various sources, data analysis, and synthesizing findings into reports or summaries."},{id:"summarization_briefing",name:"Summarization & Briefing",description:"For condensing long texts, documents, or conversations into concise summaries or executive briefings."},{id:"translation_localization",name:"Translation & Localization",description:"For translating text between languages and adapting content culturally for different locales."},{id:"data_extraction_structuring",name:"Data Extraction & Structuring",description:"For identifying and extracting specific pieces of information from unstructured/semi-structured text and organizing it."},{id:"brainstorming_ideation",name:"Brainstorming & Ideation",description:"For generating new ideas, exploring concepts, and creative problem-solving sessions."},{id:"education_tutoring",name:"Education & Tutoring",description:"For explaining concepts, answering educational questions, and providing tutoring assistance across various subjects."},{id:"image_generation",name:"Image Generation",description:"For creating images from textual descriptions. Assign to keys linked to image generation models."},{id:"audio_transcription",name:"Audio Transcription",description:"For converting speech from audio files into written text. Assign to keys linked to transcription models."}],i=e=>s.find(t=>t.id===e)},2507:(e,t,r)=>{"use strict";r.d(t,{x:()=>n});var s=r(34386),i=r(44999);async function n(){let e=await (0,i.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},49722:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>c,POST:()=>l});var i=r(96559),n=r(48088),o=r(37719),a=r(32190),d=r(2507),u=r(507);async function c(e,{params:t}){let r=await (0,d.x)(),{apiKeyId:s}=await t;if(!s)return a.NextResponse.json({error:"API Key ID is required"},{status:400});try{let{data:e,error:t}=await r.from("api_key_role_assignments").select("role_name, created_at").eq("api_key_id",s);if(t)return a.NextResponse.json({error:"Failed to fetch role assignments",details:t.message},{status:500});let i=e.map(e=>{let t=(0,u.Dc)(e.role_name);return{...e,role_details:t||{id:e.role_name,name:e.role_name,description:"Custom role (details managed globally)"}}});return a.NextResponse.json(i||[],{status:200})}catch(e){return a.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}async function l(e,{params:t}){let r=await (0,d.x)(),{apiKeyId:s}=await t,i="00000000-0000-0000-0000-000000000000";if(!s)return a.NextResponse.json({error:"API Key ID is required"},{status:400});try{let{role_name:t}=await e.json();if(!t||"string"!=typeof t)return a.NextResponse.json({error:"Role name (role_id) is required and must be a string"},{status:400});let{data:n,error:o}=await r.from("api_keys").select(`
        custom_api_config_id,
        custom_api_configs ( user_id )
      `).eq("id",s).single();if(o||!n)return a.NextResponse.json({error:"API Key not found or failed to fetch its details"},{status:404});let d=n.custom_api_configs?.user_id;if(d&&i!==d)return a.NextResponse.json({error:"Forbidden. You do not own the configuration this API key belongs to."},{status:403});let c=u.p2.some(e=>e.id===t),l=!1;if(!c){let{data:e,error:s}=await r.from("user_custom_roles").select("id").eq("user_id",i).eq("role_id",t).maybeSingle();if(s)return a.NextResponse.json({error:"Error validating role.",details:s.message},{status:500});e&&(l=!0)}if(!c&&!l)return a.NextResponse.json({error:`Invalid role_name: ${t}. Not a predefined role or a custom role you own.`},{status:400});let{custom_api_config_id:p}=n,{data:g,error:m}=await r.from("api_key_role_assignments").insert({api_key_id:s,custom_api_config_id:p,role_name:t}).select().single();if(m){if("23505"===m.code){if(m.message.includes("unique_api_key_role"))return a.NextResponse.json({error:"This API key already has this role assigned.",details:m.message},{status:409});if(m.message.includes("unique_role_per_custom_config"))return a.NextResponse.json({error:"This role is already assigned to another API key in this Custom Model (config). Check unique_role_per_custom_config constraint.",details:m.message},{status:409});return a.NextResponse.json({error:"Failed to assign role: This role may already be assigned in a way that violates a uniqueness constraint.",details:m.message,code:m.code},{status:409})}return a.NextResponse.json({error:"Failed to assign role to API key",details:m.message},{status:500})}return a.NextResponse.json(g,{status:201})}catch(e){if("SyntaxError"===e.name)return a.NextResponse.json({error:"Invalid request body: Malformed JSON."},{status:400});return a.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/keys/[apiKeyId]/roles/route",pathname:"/api/keys/[apiKeyId]/roles",filename:"route",bundlePath:"app/api/keys/[apiKeyId]/roles/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\keys\\[apiKeyId]\\roles\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:m,serverHooks:f}=p;function y(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:m})}},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,3410],()=>r(49722));module.exports=s})();
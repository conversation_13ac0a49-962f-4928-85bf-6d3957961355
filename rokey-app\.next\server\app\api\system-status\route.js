(()=>{var e={};e.id=2580,e.ids=[2580],e.modules={2507:(e,t,s)=>{"use strict";s.d(t,{Q:()=>o,x:()=>i});var r=s(34386),a=s(44999);async function i(){let e=await (0,a.UL)();return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,s,r){try{e.set({name:t,value:s,...r})}catch(e){}},remove(t,s){try{e.set({name:t,value:"",...s})}catch(e){}}}})}function o(e){return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,s){},remove(e,t){}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78284:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var r={};s.r(r),s.d(r,{GET:()=>c});var a=s(96559),i=s(48088),o=s(37719),n=s(32190),u=s(2507);async function c(e){try{let e=await (0,u.x)(),t=new Date;t.setDate(t.getDate()-1);let[s,r,a]=await Promise.allSettled([e.from("custom_api_configs").select("id").limit(1),e.from("api_keys").select("id").eq("status","active").limit(1),e.from("request_logs").select("id").gte("request_timestamp",t.toISOString()).limit(1)]),i=[],o="operational",c="";"rejected"===s.status?(o="down",c=s.reason?.message||"Connection failed"):s.value.error&&(o="down",c=s.value.error.message),i.push({name:"API Gateway",status:o,details:c,lastChecked:new Date().toISOString()});let p="operational",d="";"rejected"===r.status?(p="down",d=r.reason?.message||"Connection failed"):r.value.error?(p="degraded",d="Error checking active keys"):r.value.data&&0!==r.value.data.length||(p="degraded",d="No active API keys found"),i.push({name:"Routing Engine",status:p,details:d,lastChecked:new Date().toISOString()});let l="operational",m="";"rejected"===a.status?(l="down",m=a.reason?.message||"Connection failed"):a.value.error?(l="degraded",m="Error checking recent logs"):a.value.data&&0!==a.value.data.length||(l="degraded",m="No recent activity logged"),i.push({name:"Analytics",status:l,details:m,lastChecked:new Date().toISOString()});let h=i.some(e=>"down"===e.status),g=i.some(e=>"degraded"===e.status),I="operational";h?I="down":g&&(I="degraded");let y=n.NextResponse.json({overall_status:I,checks:i,last_updated:new Date().toISOString()});return y.headers.set("Cache-Control","public, max-age=30, stale-while-revalidate=60"),y.headers.set("X-Content-Type-Options","nosniff"),y}catch(e){return n.NextResponse.json({overall_status:"down",checks:[{name:"System Check",status:"down",details:"Failed to perform system health checks",lastChecked:new Date().toISOString()}],last_updated:new Date().toISOString(),error:e.message},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/system-status/route",pathname:"/api/system-status",filename:"route",bundlePath:"app/api/system-status/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\system-status\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:m}=p;function h(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,9398,3410],()=>s(78284));module.exports=r})();
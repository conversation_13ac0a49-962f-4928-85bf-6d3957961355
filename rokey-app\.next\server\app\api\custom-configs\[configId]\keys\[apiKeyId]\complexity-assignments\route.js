(()=>{var e={};e.id=4007,e.ids=[4007],e.modules={2411:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>I,routeModule:()=>y,serverHooks:()=>h,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>g});var r={};s.r(r),s.d(r,{GET:()=>m,PUT:()=>x});var i=s(96559),o=s(48088),a=s(37719),n=s(32190),u=s(2507),p=s(45697);let c=p.z.object({complexity_levels:p.z.array(p.z.number().int().min(1).max(5)).min(0).max(5)}),d="00000000-0000-0000-0000-000000000000";async function l(e,t,s){let{data:r,error:i}=await e.from("custom_api_configs").select("user_id").eq("id",t).single();return!i&&!!r&&r.user_id===s}async function m(e,{params:t}){let s=await (0,u.x)(),{configId:r,apiKeyId:i}=await t;if(!await l(s,r,d))return n.NextResponse.json({error:"Unauthorized or Configuration not found for this user."},{status:403});if(!r||!i)return n.NextResponse.json({error:"Config ID and API Key ID are required."},{status:400});try{let{data:e,error:t}=await s.from("config_api_key_complexity_assignments").select("complexity_level").eq("custom_api_config_id",r).eq("api_key_id",i);if(t)throw t;let o=e.map(e=>e.complexity_level);return n.NextResponse.json(o,{status:200})}catch(e){return n.NextResponse.json({error:"Failed to fetch complexity assignments.",details:e.message},{status:500})}}async function x(e,{params:t}){let s,r=await (0,u.x)(),{configId:i,apiKeyId:o}=await t;if(!await l(r,i,d))return n.NextResponse.json({error:"Unauthorized or Configuration not found for this user."},{status:403});if(!i||!o)return n.NextResponse.json({error:"Config ID and API Key ID are required."},{status:400});try{s=await e.json()}catch(e){return n.NextResponse.json({error:"Invalid JSON body."},{status:400})}let a=c.safeParse(s);if(!a.success)return n.NextResponse.json({error:"Invalid request body.",issues:a.error.flatten()},{status:400});let{complexity_levels:p}=a.data;try{let{error:e}=await r.rpc("update_api_key_complexity_assignments",{p_config_id:i,p_api_key_id:o,p_complexity_levels:p});if(e)throw e;return n.NextResponse.json({message:"Complexity assignments updated successfully.",assigned_levels:p},{status:200})}catch(t){let e="Failed to update complexity assignments.";return"23503"===t.code?e="Failed to update complexity assignments. Ensure the API key and Configuration ID are valid.":"23505"===t.code&&(e="Failed to update complexity assignments due to a conflict. This might indicate a concurrent update."),n.NextResponse.json({error:e,details:t.message},{status:500})}}let y=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments/route",pathname:"/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments",filename:"route",bundlePath:"app/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\custom-configs\\[configId]\\keys\\[apiKeyId]\\complexity-assignments\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:f,workUnitAsyncStorage:g,serverHooks:h}=y;function I(){return(0,a.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:g})}},2507:(e,t,s)=>{"use strict";s.d(t,{x:()=>o});var r=s(34386),i=s(44999);async function o(){let e=await (0,i.UL)();return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,s,r){try{e.set({name:t,value:s,...r})}catch(e){}},remove(t,s){try{e.set({name:t,value:"",...s})}catch(e){}}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,9398,3410,5697],()=>s(2411));module.exports=r})();
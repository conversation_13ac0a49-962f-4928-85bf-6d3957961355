(()=>{var e={};e.id=7177,e.ids=[7177],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{Q:()=>n,x:()=>i});var s=r(34386),o=r(44999);async function i(){let e=await (0,o.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}function n(e){return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,r){},remove(e,t){}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},17528:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>d,serverHooks:()=>h,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>c});var o=r(96559),i=r(48088),n=r(37719),a=r(32190),u=r(2507);async function c(e){let t=(0,u.Q)(e),{data:{session:r},error:s}=await t.auth.getSession();if(s||!r?.user)return a.NextResponse.json({error:"Unauthorized: You must be logged in to create configurations."},{status:401});let o=r.user;try{let{name:r}=await e.json();if(!r||"string"!=typeof r||0===r.trim().length)return a.NextResponse.json({error:"Configuration name is required and must be a non-empty string"},{status:400});if(r.length>255)return a.NextResponse.json({error:"Configuration name must be 255 characters or less"},{status:400});let{data:s,error:i}=await t.from("custom_api_configs").insert([{name:r,user_id:o.id}]).select().single();if(i)return a.NextResponse.json({error:"Failed to create custom API configuration",details:i.message},{status:500});return a.NextResponse.json(s,{status:201})}catch(e){if("SyntaxError"===e.name)return a.NextResponse.json({error:"Invalid request body: Malformed JSON."},{status:400});return a.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}async function p(e){let t=(0,u.Q)(e),{data:{session:r},error:s}=await t.auth.getSession();if(s||!r?.user)return a.NextResponse.json({error:"Unauthorized: You must be logged in to view configurations."},{status:401});let o=r.user;try{let{data:r,error:s}=await t.from("custom_api_configs").select("id, name, created_at, updated_at, routing_strategy").eq("user_id",o.id).order("created_at",{ascending:!1}).limit(100);if(s)return a.NextResponse.json({error:"Failed to fetch custom API configurations",details:s.message},{status:500});let i=a.NextResponse.json(r||[],{status:200}),n="true"===e.headers.get("X-Prefetch");return i.headers.set("Cache-Control",`private, max-age=${n?600:120}, stale-while-revalidate=300`),i.headers.set("X-Content-Type-Options","nosniff"),i.headers.set("Vary","X-Prefetch"),i}catch(e){return a.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/custom-configs/route",pathname:"/api/custom-configs",filename:"route",bundlePath:"app/api/custom-configs/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\custom-configs\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:h}=d;function g(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,3410],()=>r(17528));module.exports=s})();
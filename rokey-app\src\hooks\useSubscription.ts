import { useState, useEffect } from 'react';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';
import { SubscriptionStatus, UsageStatus, SubscriptionTier } from '@/lib/stripe';
import { User } from '@supabase/supabase-js';

export function useSubscription() {
  const [user, setUser] = useState<User | null>(null);
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
  const [usageStatus, setUsageStatus] = useState<UsageStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = createSupabaseBrowserClient();

  useEffect(() => {
    // Get initial session
    const getUser = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setUser(session?.user ?? null);

      if (session?.user) {
        fetchSubscriptionStatus(session.user);
        fetchUsageStatus(session.user);
      } else {
        setLoading(false);
      }
    };

    getUser();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setUser(session?.user ?? null);

        if (session?.user) {
          fetchSubscriptionStatus(session.user);
          fetchUsageStatus(session.user);
        } else {
          setSubscriptionStatus(null);
          setUsageStatus(null);
          setLoading(false);
        }
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const fetchSubscriptionStatus = async (currentUser: User) => {
    try {
      const response = await fetch(`/api/stripe/subscription-status?userId=${currentUser.id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch subscription status');
      }
      const data = await response.json();
      setSubscriptionStatus(data);
    } catch (err) {
      console.error('Error fetching subscription status:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    }
  };

  const fetchUsageStatus = async (currentUser: User) => {
    try {
      const response = await fetch('/api/stripe/subscription-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: currentUser.id }),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch usage status');
      }

      const data = await response.json();
      setUsageStatus(data);
    } catch (err) {
      console.error('Error fetching usage status:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const createCheckoutSession = async (tier: SubscriptionTier) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    const response = await fetch('/api/stripe/create-checkout-session', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        priceId: getPriceIdForTier(tier),
        userId: user.id,
        userEmail: user.email,
        tier,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create checkout session');
    }

    const { url } = await response.json();
    window.location.href = url;
  };

  const openCustomerPortal = async () => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    const response = await fetch('/api/stripe/customer-portal', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId: user.id,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to open customer portal');
    }

    const { url } = await response.json();
    window.location.href = url;
  };

  const refreshStatus = () => {
    if (user) {
      setLoading(true);
      fetchSubscriptionStatus(user);
      fetchUsageStatus(user);
    }
  };

  return {
    subscriptionStatus,
    usageStatus,
    loading,
    error,
    createCheckoutSession,
    openCustomerPortal,
    refreshStatus,
    isAuthenticated: !!user,
    user,
  };
}

function getPriceIdForTier(tier: SubscriptionTier): string {
  switch (tier) {
    case 'starter':
      return process.env.NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID!;
    case 'professional':
      return process.env.NEXT_PUBLIC_STRIPE_PROFESSIONAL_PRICE_ID!;
    case 'enterprise':
      return process.env.NEXT_PUBLIC_STRIPE_ENTERPRISE_PRICE_ID!;
    default:
      throw new Error(`Invalid tier: ${tier}`);
  }
}

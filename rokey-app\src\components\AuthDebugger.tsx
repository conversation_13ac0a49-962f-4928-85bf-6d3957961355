'use client';

import { useEffect, useState } from 'react';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';

interface AuthDebugInfo {
  hasSession: boolean;
  userId?: string;
  userEmail?: string;
  sessionError?: string;
  apiTestResult?: string;
  apiTestError?: string;
  timestamp: string;
}

export default function AuthDebugger() {
  const [debugInfo, setDebugInfo] = useState<AuthDebugInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const supabase = createSupabaseBrowserClient();

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut();
      window.location.href = '/auth/signin';
    } catch (err) {
      console.error('Sign out error:', err);
    }
  };

  const checkAuthStatus = async () => {
    try {
      setLoading(true);

      // Check session
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      
      let apiTestResult = '';
      let apiTestError = '';
      
      // Test API call if we have a session
      if (session) {
        try {
          const response = await fetch('/api/custom-configs');
          if (response.ok) {
            const data = await response.json();
            apiTestResult = `API call successful. Configs count: ${Array.isArray(data) ? data.length : 'N/A'}`;
          } else {
            const errorData = await response.json();
            apiTestError = `API call failed: ${response.status} - ${errorData.error || 'Unknown error'}`;
          }
        } catch (err) {
          apiTestError = `API call error: ${err instanceof Error ? err.message : String(err)}`;
        }
      }
      
      setDebugInfo({
        hasSession: !!session,
        userId: session?.user?.id,
        userEmail: session?.user?.email,
        sessionError: sessionError?.message,
        apiTestResult,
        apiTestError,
        timestamp: new Date().toISOString()
      });
    } catch (err) {
      setDebugInfo({
        hasSession: false,
        sessionError: err instanceof Error ? err.message : String(err),
        timestamp: new Date().toISOString()
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkAuthStatus();
    
    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(() => {
      checkAuthStatus();
    });

    return () => subscription.unsubscribe();
  }, []);

  if (loading) {
    return (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <p className="text-blue-600">Checking authentication status...</p>
      </div>
    );
  }

  if (!debugInfo) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <p className="text-red-600">Failed to check authentication status</p>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 space-y-3">
      <h3 className="font-semibold text-gray-900">Authentication Debug Info</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
        <div>
          <strong>Session Status:</strong>
          <span className={`ml-2 px-2 py-1 rounded text-xs ${
            debugInfo.hasSession 
              ? 'bg-green-100 text-green-800' 
              : 'bg-red-100 text-red-800'
          }`}>
            {debugInfo.hasSession ? 'Active' : 'None'}
          </span>
        </div>
        
        {debugInfo.userId && (
          <div>
            <strong>User ID:</strong>
            <span className="ml-2 font-mono text-xs">{debugInfo.userId}</span>
          </div>
        )}
        
        {debugInfo.userEmail && (
          <div>
            <strong>Email:</strong>
            <span className="ml-2">{debugInfo.userEmail}</span>
          </div>
        )}
        
        {debugInfo.sessionError && (
          <div className="col-span-full">
            <strong>Session Error:</strong>
            <span className="ml-2 text-red-600">{debugInfo.sessionError}</span>
          </div>
        )}
        
        {debugInfo.apiTestResult && (
          <div className="col-span-full">
            <strong>API Test:</strong>
            <span className="ml-2 text-green-600">{debugInfo.apiTestResult}</span>
          </div>
        )}
        
        {debugInfo.apiTestError && (
          <div className="col-span-full">
            <strong>API Test Error:</strong>
            <span className="ml-2 text-red-600">{debugInfo.apiTestError}</span>
          </div>
        )}
      </div>
      
      <div className="pt-2 border-t border-gray-200 flex items-center justify-between">
        <div>
          <button
            onClick={checkAuthStatus}
            className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 mr-2"
          >
            Refresh
          </button>
          {debugInfo.hasSession && (
            <button
              onClick={handleSignOut}
              className="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
            >
              Sign Out
            </button>
          )}
        </div>
        <span className="text-xs text-gray-500">
          Last checked: {new Date(debugInfo.timestamp).toLocaleTimeString()}
        </span>
      </div>
    </div>
  );
}

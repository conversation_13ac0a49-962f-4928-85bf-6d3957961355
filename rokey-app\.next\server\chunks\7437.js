exports.id=7437,exports.ids=[7437],exports.modules={3047:(e,t,s)=>{"use strict";s.d(t,{default:()=>u});var r=s(43210),a=s(16189);class i{constructor(e=100){this.cache=new Map,this.cleanupInterval=null,this.maxSize=e,this.startCleanup()}set(e,t,s={}){let{ttl:r=3e5,tags:a=[],priority:i="medium",serialize:n=!1}=s,l={data:n?JSON.parse(JSON.stringify(t)):t,timestamp:Date.now(),ttl:r,accessCount:0,lastAccessed:Date.now(),tags:a,priority:i};this.cache.size>=this.maxSize&&this.evictLeastUsed(),this.cache.set(e,l)}get(e){let t=this.cache.get(e);return t?this.isExpired(t)?(this.cache.delete(e),null):(t.accessCount++,t.lastAccessed=Date.now(),t.data):null}getStale(e){let t=this.cache.get(e);if(!t)return{data:null,isStale:!1};let s=this.isExpired(t);return t.accessCount++,t.lastAccessed=Date.now(),{data:t.data,isStale:s}}has(e){let t=this.cache.get(e);return!!t&&(!this.isExpired(t)||(this.cache.delete(e),!1))}delete(e){return this.cache.delete(e)}invalidateByTags(e){let t=0;for(let[s,r]of this.cache.entries())r.tags.some(t=>e.includes(t))&&(this.cache.delete(s),t++);return t}clear(){this.cache.clear()}getStats(){let e=Array.from(this.cache.values()),t=Date.now();return{size:this.cache.size,maxSize:this.maxSize,hitRate:this.calculateHitRate(),averageAge:e.reduce((e,s)=>e+(t-s.timestamp),0)/e.length||0,totalAccesses:e.reduce((e,t)=>e+t.accessCount,0),expiredEntries:e.filter(e=>this.isExpired(e)).length,priorityDistribution:{high:e.filter(e=>"high"===e.priority).length,medium:e.filter(e=>"medium"===e.priority).length,low:e.filter(e=>"low"===e.priority).length}}}async preload(e,t,s={}){let r=this.get(e);if(r)return this.backgroundRefresh(e,t,s),r;let a=await t();return this.set(e,a,s),a}async backgroundRefresh(e,t,s){try{let r=await t();this.set(e,r,s)}catch(e){}}isExpired(e){return Date.now()-e.timestamp>e.ttl}evictLeastUsed(){if(0===this.cache.size)return;let[e]=Array.from(this.cache.entries()).sort(([,e],[,t])=>{let s={low:0,medium:1,high:2},r=s[e.priority]-s[t.priority];return 0!==r?r:e.lastAccessed-t.lastAccessed})[0];this.cache.delete(e)}calculateHitRate(){let e=Array.from(this.cache.values()).reduce((e,t)=>e+t.accessCount,0);return e>0?this.cache.size/e*100:0}startCleanup(){this.cleanupInterval=setInterval(()=>{this.cleanup()},6e4)}cleanup(){Date.now();let e=[];for(let[t,s]of this.cache.entries())this.isExpired(s)&&e.push(t);e.forEach(e=>this.cache.delete(e)),e.length}destroy(){this.cleanupInterval&&clearInterval(this.cleanupInterval),this.cache.clear()}}let n=new i(200),l={static:{ttl:864e5,priority:"high",tags:["static"]},user:{ttl:12e4,priority:"high",tags:["user"]},system:{ttl:3e4,priority:"low",tags:["system"]},pricing:{ttl:36e5,priority:"medium",tags:["pricing"]}},o={LANDING_FEATURES:"landing:features",PRICING_TIERS:"pricing:tiers",PRICING_COMPARISON:"pricing:comparison",SYSTEM_STATUS:"system:status",SYSTEM_MODELS:"system:models",USER_CONFIGS:"user:configs",USER_ANALYTICS:"user:analytics"};class c{static getInstance(){return c.instance||(c.instance=new c),c.instance}trackNavigation(e,t){let s=`${e}->${t}`,r=this.userBehavior.get(s)||0;this.userBehavior.set(s,r+1),r>2&&this.schedulePrefetch(t)}schedulePrefetch(e){this.prefetchQueue.has(e)||(this.prefetchQueue.add(e),this.processPrefetchQueue())}async processPrefetchQueue(){if(!this.isProcessing&&0!==this.prefetchQueue.size){for(let e of(this.isProcessing=!0,this.prefetchQueue)){try{await this.prefetchRoute(e),this.prefetchQueue.delete(e)}catch(e){}await new Promise(e=>setTimeout(e,100))}this.isProcessing=!1}}async prefetchRoute(e){let t={"/dashboard":()=>this.prefetchDashboardData(),"/pricing":()=>this.prefetchPricingData(),"/auth/signup":()=>this.prefetchAuthData(),"/features":()=>this.prefetchFeaturesData()}[e];t&&await t()}async prefetchDashboardData(){let e=[this.cacheIfNotExists(o.USER_CONFIGS,"/api/custom-configs",l.user),this.cacheIfNotExists(o.USER_ANALYTICS,"/api/analytics",l.user),this.cacheIfNotExists(o.SYSTEM_STATUS,"/api/system-status",l.system)];await Promise.allSettled(e)}async prefetchPricingData(){let e=[this.cacheIfNotExists(o.PRICING_TIERS,"/api/pricing/tiers",l.pricing),this.cacheIfNotExists(o.PRICING_COMPARISON,"/api/pricing/comparison",l.pricing)];await Promise.allSettled(e)}async prefetchAuthData(){let e=[this.cacheIfNotExists(o.PRICING_TIERS,"/api/pricing/tiers",l.pricing)];await Promise.allSettled(e)}async prefetchFeaturesData(){let e=[this.cacheIfNotExists(o.LANDING_FEATURES,"/api/features",l.static),this.cacheIfNotExists(o.SYSTEM_MODELS,"/api/models",l.static)];await Promise.allSettled(e)}async cacheIfNotExists(e,t,s){let r=n.get(e);if(r)return r;try{let r=await fetch(t);if(r.ok){let t=await r.json();return n.set(e,t,s),t}}catch(e){}}constructor(){this.prefetchQueue=new Set,this.isProcessing=!1,this.userBehavior=new Map}}function u({enableUserBehaviorTracking:e=!0,enableNavigationTracking:t=!0,enableInteractionTracking:s=!0}){(0,a.usePathname)(),(0,r.useRef)(""),(0,r.useRef)(0);let{exportMetrics:i}=function(e,t={}){let{enableMonitoring:s=!0,enableMemoryTracking:a=!0,enableBundleAnalysis:i=!1,enableCacheTracking:n=!0,warningThresholds:l={renderTime:100,memoryUsage:0x3200000,bundleSize:1048576}}=t,[o,c]=(0,r.useState)({renderTime:0}),u=(0,r.useRef)(0),d=(0,r.useRef)(0),h=(0,r.useRef)(0),m=(0,r.useCallback)(()=>{s&&(u.current=performance.now())},[s]),p=(0,r.useCallback)(()=>{if(!s||!u.current)return;let e=performance.now()-u.current;c(t=>({...t,renderTime:e})),l.renderTime,u.current=0},[e,s,l.renderTime]),g=(0,r.useCallback)(()=>{if(!a||!("memory"in performance))return;let e=performance.memory,t={used:e.usedJSHeapSize,total:e.totalJSHeapSize,limit:e.jsHeapSizeLimit};c(e=>({...e,memoryUsage:t})),t.used,l.memoryUsage},[e,a,l.memoryUsage]),f=(0,r.useCallback)(()=>{if(!i)return;let e=performance.getEntriesByType("resource"),t=0;e.forEach(e=>{e.name.includes(".js")&&e.transferSize&&(t+=e.transferSize)}),c(e=>({...e,bundleSize:t})),l.bundleSize},[i,l.bundleSize]),x=(0,r.useCallback)(()=>{if(!n)return;let e=h.current>0?d.current/h.current*100:0;c(t=>({...t,cacheHitRate:e}))},[n]),y=(0,r.useCallback)(()=>{let e=[];return o.renderTime>100&&(e.push("Consider memoizing expensive calculations"),e.push("Use React.memo for component optimization"),e.push("Implement virtualization for large lists")),o.memoryUsage&&o.memoryUsage.used>0x3200000&&(e.push("Check for memory leaks"),e.push("Optimize image sizes and formats"),e.push("Implement proper cleanup in useEffect")),o.bundleSize&&o.bundleSize>1048576&&(e.push("Implement code splitting"),e.push("Use dynamic imports for heavy components"),e.push("Remove unused dependencies")),void 0!==o.cacheHitRate&&o.cacheHitRate<70&&(e.push("Improve caching strategy"),e.push("Implement service worker caching"),e.push("Use browser cache headers")),e},[o]),b=(0,r.useCallback)(()=>({component:e,timestamp:new Date().toISOString(),metrics:o,suggestions:y(),userAgent:navigator.userAgent,url:window.location.href}),[e,o,y]);return{metrics:o,startMeasurement:m,endMeasurement:p,trackMemoryUsage:g,analyzeBundleSize:f,trackCacheHitRate:x,getOptimizationSuggestions:y,exportMetrics:b}}("PerformanceTracker");return null}c.getInstance()},11016:(e,t,s)=>{"use strict";s.d(t,{R:()=>i});var r=s(43210),a=s(79481);function i(){let[e,t]=(0,r.useState)(null),[s,i]=(0,r.useState)(null),[n,l]=(0,r.useState)(null),[o,c]=(0,r.useState)(!0),[u,d]=(0,r.useState)(null);(0,a.u)();let h=async e=>{try{let t=await fetch(`/api/stripe/subscription-status?userId=${e.id}`);if(!t.ok)throw Error("Failed to fetch subscription status");let s=await t.json();i(s)}catch(e){d(e instanceof Error?e.message:"Unknown error")}},m=async e=>{try{let t=await fetch("/api/stripe/subscription-status",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:e.id})});if(!t.ok)throw Error("Failed to fetch usage status");let s=await t.json();l(s)}catch(e){d(e instanceof Error?e.message:"Unknown error")}finally{c(!1)}};return{subscriptionStatus:s,usageStatus:n,loading:o,error:u,createCheckoutSession:async t=>{if(!e)throw Error("User not authenticated");let s=await fetch("/api/stripe/create-checkout-session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({priceId:function(e){switch(e){case"starter":return"price_1RaA5xC97XFBBUvdt12n1i0T";case"professional":return"price_1RaABVC97XFBBUvdkZZc1oQB";case"enterprise":return"price_1RaADDC97XFBBUvd7j6OPJj7";default:throw Error(`Invalid tier: ${e}`)}}(t),userId:e.id,userEmail:e.email,tier:t})});if(!s.ok)throw Error((await s.json()).error||"Failed to create checkout session");let{url:r}=await s.json();window.location.href=r},openCustomerPortal:async()=>{if(!e)throw Error("User not authenticated");let t=await fetch("/api/stripe/customer-portal",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:e.id})});if(!t.ok)throw Error((await t.json()).error||"Failed to open customer portal");let{url:s}=await t.json();window.location.href=s},refreshStatus:()=>{e&&(c(!0),h(e),m(e))},isAuthenticated:!!e,user:e}}},11638:(e,t,s)=>{"use strict";s.d(t,{default:()=>a}),s(43210);var r=s(93712);function a(){let{pageTitle:e}=(0,r.rT)();return null}},34701:(e,t,s)=>{"use strict";s.d(t,{default:()=>a}),s(43210);var r=s(16189);function a(){return(0,r.useRouter)(),null}},35760:()=>{},36721:(e,t,s)=>{"use strict";s.d(t,{bu:()=>o,i9:()=>l});var r=s(60687),a=s(43210),i=s(16189);let n=(0,a.createContext)(void 0);function l({children:e}){let[t,s]=(0,a.useState)(!1),[l,o]=(0,a.useState)(null),[c,u]=(0,a.useState)([]),[d,h]=(0,a.useState)(new Set),[m,p]=(0,a.useState)(!1),g=(0,i.usePathname)(),f=(0,i.useRouter)(),x=(0,a.useRef)(null),y=(0,a.useRef)([]),b=(0,a.useRef)(null),v=(0,a.useRef)(0),w=(0,a.useRef)({}),j=(0,a.useRef)({}),N=(0,a.useCallback)(e=>{},[m]),S=(0,a.useCallback)(e=>d.has(e),[d]),k=(0,a.useCallback)(()=>{if(0===y.current.length)return;let e=y.current[y.current.length-1];y.current=[e];let{route:t,id:r}=e;N(`🚀 [OPTIMISTIC NAV] Processing navigation to: ${t} (id: ${r})`),x.current&&(clearTimeout(x.current),x.current=null),b.current=r;let a=S(t);a&&(N(`⚡ [OPTIMISTIC NAV] Using cached navigation for: ${t}`),setTimeout(()=>{b.current===r&&s(!1)},100));try{f.push(t)}catch(e){N(`❌ [OPTIMISTIC NAV] Router.push failed for: ${t}, using fallback`),window.location.href=t;return}x.current=setTimeout(()=>{if(N(`⚠️ [OPTIMISTIC NAV] Timeout reached for: ${t} (id: ${r}), current path: ${g}`),b.current===r){N(`🔄 [OPTIMISTIC NAV] Attempting fallback navigation to: ${t}`);try{window.location.href=t}catch(e){N(`❌ [OPTIMISTIC NAV] Fallback navigation failed: ${e}`)}s(!1),o(null),b.current=null}x.current=null},a?800:3e3)},[f,g,S,N]),C=(0,a.useCallback)(e=>{if(g===e||!m)return;let t=Date.now();if(t-v.current<100&&l===e)return void N(`🔄 [OPTIMISTIC NAV] Debouncing duplicate navigation to: ${e}`);if(v.current=t,w.current[e]||(w.current[e]=0),w.current[e]++,j.current[e]&&clearTimeout(j.current[e]),j.current[e]=setTimeout(()=>{w.current[e]=0},2e3),w.current[e]>=3){N(`🚨 [OPTIMISTIC NAV] Force navigation escape hatch for: ${e}`),w.current[e]=0;return}x.current&&(clearTimeout(x.current),x.current=null),s(!0),o(e);let r=`nav_${t}_${Math.random().toString(36).substr(2,9)}`;y.current=[{route:e,timestamp:t,id:r}],k()},[g,l,k,N,m]),I=(0,a.useCallback)(()=>{x.current&&(clearTimeout(x.current),x.current=null),s(!1),o(null),b.current=null,y.current=[]},[]);return(0,r.jsx)(n.Provider,{value:{isNavigating:t,targetRoute:l,navigateOptimistically:C,clearNavigation:I,isPageCached:S,navigationHistory:c},children:e})}function o(){return(0,a.useContext)(n)||null}},38783:(e,t,s)=>{"use strict";s.d(t,{default:()=>ee});var r=s(60687),a=s(16189),i=s(50549),n=s(36721),l=s(43210),o=s(85814),c=s.n(o),u=s(6510),d=s(52238),h=s(51426),m=s(27010),p=s(83188),g=s(34944),f=s(93712),x=s(11016),y=s(79481);function b(){let{toggleSidebar:e}=(0,i.c)(),{breadcrumb:t}=(0,f.rT)(),{user:s,subscriptionStatus:a}=(0,x.R)(),[n,o]=(0,l.useState)(!1),b=(0,y.u)(),v=s?.user_metadata?.first_name||s?.user_metadata?.full_name?.split(" ")[0]||"User",w=v.charAt(0).toUpperCase()+(s?.user_metadata?.last_name?.charAt(0)?.toUpperCase()||v.charAt(1)?.toUpperCase()||"U"),j=a?.hasActiveSubscription?a?.tier==="starter"?"Starter Plan":a?.tier==="professional"?"Professional Plan":a?.tier==="enterprise"?"Enterprise Plan":"Starter Plan":"Starter Plan",N=async()=>{try{await b.auth.signOut(),window.location.href="/auth/signin"}catch(e){}};return(0,r.jsx)("nav",{className:"header border-b border-gray-200 bg-white/95 backdrop-blur-sm w-full",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("button",{onClick:e,className:"lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200",title:"Toggle sidebar",children:(0,r.jsx)(u.A,{className:"h-6 w-6 text-gray-600"})}),(0,r.jsx)("div",{className:"lg:hidden",children:(0,r.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"RouKey"})}),(0,r.jsxs)("div",{className:"hidden lg:flex items-center space-x-2 text-sm text-gray-600",children:[(0,r.jsx)("span",{children:t.title}),(0,r.jsx)("span",{children:"/"}),(0,r.jsx)("span",{className:"text-gray-900 font-medium",children:t.subtitle})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-4",children:[(0,r.jsx)("div",{className:"hidden xl:block",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:"text",placeholder:"Search...",className:"w-64 pl-10 pr-4 py-2.5 text-sm bg-white border border-gray-200 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-500 transition-all duration-200"}),(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)("svg",{className:"h-4 w-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})})]})}),(0,r.jsxs)("button",{className:"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 relative",children:[(0,r.jsx)(d.A,{className:"h-5 w-5 text-gray-600"}),(0,r.jsx)("span",{className:"absolute -top-1 -right-1 h-3 w-3 bg-orange-500 rounded-full"})]}),(0,r.jsxs)("div",{className:"hidden sm:block relative",children:[(0,r.jsxs)("button",{onClick:()=>o(!n),className:"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 flex items-center space-x-1",children:[(0,r.jsx)(h.A,{className:"h-5 w-5 text-gray-600"}),(0,r.jsx)(m.A,{className:`h-3 w-3 text-gray-600 transition-transform duration-200 ${n?"rotate-180":""}`})]}),n&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"fixed inset-0 z-10",onClick:()=>o(!1)}),(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-20",children:[(0,r.jsxs)(c(),{href:"/dashboard/settings",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200",onClick:()=>o(!1),children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-3 text-gray-500"}),"Settings"]}),(0,r.jsxs)(c(),{href:"/dashboard/billing",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200",onClick:()=>o(!1),children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-3 text-gray-500"}),"Billing"]}),(0,r.jsx)("hr",{className:"my-1 border-gray-200"}),(0,r.jsxs)("button",{onClick:N,className:"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 mr-3 text-red-500"}),"Sign Out"]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3 px-2 sm:px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer",children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-gradient-to-br from-orange-400 to-orange-500 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-semibold text-sm",children:w})}),(0,r.jsxs)("div",{className:"hidden md:block",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:v}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:j})]})]})]})]})})})}var v=s(30474),w=s(20816),j=s(97450),N=s(86297),S=s(10799),k=s(61245),C=s(45428),I=s(45994),P=s(71155),R=s(56439),A=s(21134),E=s(13530),T=s(86870),D=s(54293),M=s(78129);class ${setRouter(e){this.router=e}async prefetchRoute(e,t={}){if(!this.router)return;let{priority:s="low",delay:r=0,condition:a}=t;if(a&&!a())return;let i=this.prefetchedRoutes.get(e);i&&i.prefetched&&Date.now()-i.timestamp<3e5||(this.prefetchQueue.push({route:e,options:t}),this.prefetchedRoutes.set(e,{route:e,timestamp:Date.now(),prefetched:!1}),this.processQueue())}async processQueue(){if(!this.isProcessing&&0!==this.prefetchQueue.length){for(this.isProcessing=!0,this.prefetchQueue.sort((e,t)=>{let s={high:0,low:1};return s[e.options.priority||"low"]-s[t.options.priority||"low"]});this.prefetchQueue.length>0;){let{route:e,options:t}=this.prefetchQueue.shift();try{if(t.delay&&t.delay>0&&await new Promise(e=>setTimeout(e,t.delay)),t.condition&&!t.condition())continue;await this.router.prefetch(e),await this.prefetchBundles(e);let s=this.prefetchedRoutes.get(e);s&&(s.prefetched=!0,s.timestamp=Date.now()),await new Promise(e=>setTimeout(e,50))}catch(e){}}this.isProcessing=!1}}async prefetchBundles(e){try{["https://fonts.googleapis.com","https://fonts.gstatic.com"].forEach(e=>{if(document.querySelector(`link[href="${e}"][rel="preconnect"]`))return;let t=document.createElement("link");t.rel="preconnect",t.href=e,t.crossOrigin="anonymous",document.head.appendChild(t)})}catch(e){}}cleanup(){let e=Date.now();for(let[t,s]of this.prefetchedRoutes.entries())e-s.timestamp>6e5&&this.prefetchedRoutes.delete(t)}constructor(){this.prefetchedRoutes=new Map,this.router=null,this.prefetchQueue=[],this.isProcessing=!1}}let O=new $,U=()=>{let e=(0,a.useRouter)(),t=(0,l.useRef)();(0,l.useEffect)(()=>(O.setRouter(e),t.current=setInterval(()=>{O.cleanup()},3e5),()=>{t.current&&clearInterval(t.current)}),[e]);let s=(0,l.useCallback)((e,t)=>{O.prefetchRoute(e,t)},[]),r=(0,l.useCallback)((e,t=100)=>({onMouseEnter:()=>{s(e,{priority:"high",delay:t})}}),[s]),i=(0,l.useCallback)((e,t)=>{if(!t)return;let r=new IntersectionObserver(t=>{t.forEach(t=>{t.isIntersecting&&(s(e,{priority:"low",delay:200}),r.disconnect())})},{threshold:.1});return r.observe(t),()=>r.disconnect()},[s]);return{prefetchRoute:s,prefetchOnHover:r,prefetchOnVisible:i}},z=()=>{let{prefetchRoute:e}=U(),t=(0,l.useRef)({lastActivity:Date.now(),isIdle:!1,mouseMovements:0});return(0,l.useEffect)(()=>{let e,s,r=()=>{t.current.lastActivity=Date.now(),t.current.isIdle=!1,clearTimeout(e),e=setTimeout(()=>{t.current.isIdle=!0},3e3)},a=()=>{t.current.mouseMovements++,r()},i=()=>{r()};return document.addEventListener("mousemove",a),document.addEventListener("keypress",i),document.addEventListener("click",r),document.addEventListener("scroll",r),s=setInterval(()=>{t.current.mouseMovements=0},1e4),()=>{document.removeEventListener("mousemove",a),document.removeEventListener("keypress",i),document.removeEventListener("click",r),document.removeEventListener("scroll",r),clearTimeout(e),clearInterval(s)}},[]),{prefetchWhenIdle:(0,l.useCallback)(s=>{let r=setInterval(()=>{t.current.isIdle&&t.current.mouseMovements<5&&s.forEach((s,r)=>{e(s,{priority:"low",delay:500*r,condition:()=>t.current.isIdle})})},2e3);return()=>clearInterval(r)},[e]),isUserIdle:()=>t.current.isIdle}};var L=s(48427);let _="rokey_navigation_patterns";function F(){let[e,t]=(0,l.useState)(null),[s,r]=(0,l.useState)([]),i=(0,a.usePathname)(),{prefetchRoute:n}=U(),o=(0,l.useRef)(Date.now());function c(){let e=new Date().getHours();return e>=6&&e<12?"morning":e>=12&&e<17?"afternoon":e>=17&&e<21?"evening":"night"}(0,l.useRef)(Date.now());let u=(0,l.useCallback)((s,r)=>{if(!e||s===r)return;let a=Date.now(),i=a-o.current;o.current=a,t(e=>{if(!e)return null;let t=[...e.patterns],n=t.find(e=>e.from===s&&e.to===r);n?(n.frequency+=1,n.lastUsed=a,n.avgTimeSpent=(n.avgTimeSpent+i)/2):t.push({from:s,to:r,frequency:1,lastUsed:a,avgTimeSpent:i});let l=new Map;t.forEach(e=>{l.set(e.to,(l.get(e.to)||0)+e.frequency)});let o=Array.from(l.entries()).sort((e,t)=>t[1]-e[1]).slice(0,5).map(([e])=>e),u={...e,patterns:t,totalNavigations:e.totalNavigations+1,preferredRoutes:o,timeOfDay:c()};try{localStorage.setItem(_,JSON.stringify(u))}catch(e){}return u})},[e]);(0,l.useCallback)(()=>e&&i?(c(),[...new Set([...e.patterns.filter(e=>e.from===i&&e.frequency>=2).sort((e,t)=>{let s=e.frequency*(1+(Date.now()-e.lastUsed)/864e5);return t.frequency*(1+(Date.now()-t.lastUsed)/864e5)-s}).slice(0,3).map(e=>e.to),...e.patterns.filter(e=>2>=Math.abs(new Date(e.lastUsed).getHours()-new Date().getHours())).sort((e,t)=>t.frequency-e.frequency).slice(0,2).map(e=>e.to)])].slice(0,4)):[],[e,i]),(0,l.useRef)(i);let d=(0,l.useCallback)(()=>{if(!e)return[];let t=[];e.preferredRoutes.length>0&&t.push(`Most visited: ${e.preferredRoutes[0]}`),e.totalNavigations>10&&t.push(`${e.totalNavigations} total navigations this session`);let s=e.patterns.filter(e=>1>=Math.abs(new Date(e.lastUsed).getHours()-new Date().getHours()));return s.length>0&&t.push(`${s.length} patterns match current time`),t},[e]),h=(0,l.useCallback)(()=>{localStorage.removeItem(_),t({patterns:[],sessionStartTime:Date.now(),totalNavigations:0,preferredRoutes:[],timeOfDay:c()}),r([])},[]);return{predictions:s,userBehavior:e,insights:d(),trackNavigation:u,clearPatterns:h,isLearning:!!e?.totalNavigations&&e.totalNavigations>5}}let B=[{href:"/dashboard",label:"Dashboard",icon:w.A,iconSolid:P.A,description:"Overview & analytics"},{href:"/my-models",label:"My Models",icon:j.A,iconSolid:R.A,description:"API key management"},{href:"/playground",label:"Playground",icon:N.A,iconSolid:A.A,description:"Test your models"},{href:"/routing-setup",label:"Routing Setup",icon:S.A,iconSolid:E.A,description:"Configure routing"},{href:"/logs",label:"Logs",icon:k.A,iconSolid:T.A,description:"Request history"},{href:"/training",label:"Prompt Engineering",icon:C.A,iconSolid:D.A,description:"Custom prompts"},{href:"/analytics",label:"Analytics",icon:I.A,iconSolid:M.A,description:"Advanced insights"}];function H(){let e=(0,a.usePathname)(),{isCollapsed:t,isHovered:s,isHoverDisabled:o,setHovered:u}=(0,i.c)(),{navigateOptimistically:d}=(0,n.bu)()||{navigateOptimistically:()=>{}},{prefetchOnHover:h}=U(),{prefetchWhenIdle:m}=z(),{prefetchChatHistory:p}=(0,L.l2)(),{predictions:g,isLearning:f}=F(),x=function(){(0,a.usePathname)();let[e,t]=(0,l.useState)([]);return e}(),y=!t||s;return(0,r.jsx)("aside",{className:`sidebar flex flex-col h-full flex-shrink-0 transition-all duration-200 ease-out bg-gray-900 ${y?"w-64":"w-16"}`,onMouseEnter:()=>!o&&u(!0),onMouseLeave:()=>!o&&u(!1),children:(0,r.jsx)("div",{className:"flex-1 relative overflow-hidden",children:(0,r.jsxs)("div",{className:`p-6 transition-all duration-200 ease-out ${y?"px-6":"px-3"}`,children:[(0,r.jsx)("div",{className:`mb-8 pt-4 transition-all duration-200 ease-out ${y?"":"text-center"}`,children:(0,r.jsxs)("div",{className:"relative overflow-hidden",children:[(0,r.jsx)("div",{className:`transition-all duration-200 ease-out ${y?"opacity-0 scale-75 -translate-y-2":"opacity-100 scale-100 translate-y-0"} ${y?"absolute":"relative"} w-8 h-8 bg-white rounded-lg flex items-center justify-center mx-auto p-0.5`,children:(0,r.jsx)(v.default,{src:"/roukey_logo.png",alt:"RouKey",width:28,height:28,className:"object-cover"})}),(0,r.jsxs)("div",{className:`transition-all duration-200 ease-out ${y?"opacity-100 scale-100 translate-y-0":"opacity-0 scale-75 translate-y-2"} ${y?"relative":"absolute top-0 left-0 w-full"}`,children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-white tracking-tight whitespace-nowrap",children:"RouKey"}),(0,r.jsx)("p",{className:"text-sm text-gray-400 mt-1 whitespace-nowrap",children:"Smart LLM Router"})]})]})}),(0,r.jsx)("nav",{className:"space-y-2",children:B.map(t=>{let s=e===t.href||e.startsWith(t.href+"/"),a=s?t.iconSolid:t.icon,i=g.includes(t.href),n=x.find(e=>e.route===t.href),l="/playground"===t.href?{onMouseEnter:()=>{if("/playground"===t.href){h(t.href,50).onMouseEnter();let e=new URLSearchParams(window.location.search).get("config");e&&p(e)}}}:h(t.href,50);return(0,r.jsx)(c(),{href:t.href,onClick:e=>{e.preventDefault(),d(t.href)},className:`sidebar-nav-item group flex items-center transition-all duration-200 ease-out w-full text-left ${s?"active":""} ${y?"":"collapsed"}`,title:y?void 0:t.label,...l,children:(0,r.jsxs)("div",{className:"relative flex items-center w-full overflow-hidden",children:[(0,r.jsxs)("div",{className:`relative flex items-center justify-center transition-all duration-200 ease-out ${y?"w-5 h-5 mr-3":"w-10 h-10 rounded-xl"} ${!y&&s?"bg-white shadow-sm":!y?"bg-transparent hover:bg-white/10":""}`,children:[(0,r.jsx)(a,{className:`transition-all duration-200 ease-out h-5 w-5 ${s?"text-orange-500":"text-white"}`}),i&&!s&&(0,r.jsx)("div",{className:`absolute rounded-full bg-blue-400 animate-pulse transition-all duration-200 ease-out ${y?"-top-1 -right-1 w-2 h-2":"-top-1 -right-1 w-3 h-3"}`,title:"Predicted next destination"})]}),(0,r.jsxs)("div",{className:`flex-1 transition-all duration-200 ease-out ${y?"opacity-100 translate-x-0 max-w-full":"opacity-0 translate-x-4 max-w-0"}`,children:[(0,r.jsxs)("div",{className:"flex items-center justify-between whitespace-nowrap",children:[(0,r.jsx)("div",{className:"font-medium text-sm",children:t.label}),n&&!s&&(0,r.jsx)("span",{className:`text-xs px-1.5 py-0.5 rounded-full ml-2 ${"high"===n.priority?"bg-blue-500/20 text-blue-300":"bg-gray-500/20 text-gray-300"}`,children:"high"===n.priority?"!":"\xb7"})]}),(0,r.jsx)("div",{className:`text-xs transition-colors duration-200 whitespace-nowrap ${s?"text-orange-400":"text-gray-400"}`,children:n?n.reason:t.description})]})]})},t.href)})})]})})})}let Q=()=>(0,r.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[1,2,3].map(e=>(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-4"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"})]},e))}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,r.jsx)("div",{className:"space-y-3",children:[1,2,3,4].map(e=>(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))})]})]}),W=()=>(0,r.jsxs)("div",{className:"space-y-8 animate-pulse",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded w-1/2 mx-auto mb-4"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[1,2,3].map(e=>(0,r.jsxs)("div",{className:"bg-white p-8 rounded-2xl border-2",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2 mx-auto mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mx-auto mb-4"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-1/3 mx-auto"})]}),(0,r.jsx)("div",{className:"space-y-3 mb-8",children:[1,2,3,4,5].map(e=>(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))}),(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded"})]},e))})]}),J=()=>(0,r.jsxs)("div",{className:"space-y-8 animate-pulse",children:[(0,r.jsxs)("div",{className:"text-center py-20 bg-gradient-to-br from-slate-50 to-blue-50",children:[(0,r.jsx)("div",{className:"h-16 bg-gray-200 rounded w-2/3 mx-auto mb-6"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,r.jsx)("div",{className:"py-20",children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-12",children:[1,2,3,4,5,6].map(e=>(0,r.jsx)("div",{className:"bg-white rounded-2xl p-8 border",children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-xl"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2 mb-3"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded mb-4"}),(0,r.jsx)("div",{className:"space-y-2",children:[1,2,3].map(e=>(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-3/4"},e))})]})]})},e))})})]}),K=()=>(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50",children:(0,r.jsxs)("div",{className:"bg-white p-8 rounded-2xl shadow-lg border w-full max-w-md animate-pulse",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/2 mx-auto mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded"})]})]})}),q=()=>(0,r.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 mb-4"}),(0,r.jsx)("div",{className:"space-y-3",children:[1,2,3].map(e=>(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))}),(0,r.jsx)("div",{className:"h-32 bg-gray-200 rounded mt-4"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded mt-4"})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 mb-4"}),(0,r.jsx)("div",{className:"h-64 bg-gray-200 rounded"})]})]})]}),G=()=>(0,r.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-2/3"}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg border",children:(0,r.jsx)("div",{className:"space-y-4",children:[1,2,3,4,5].map(e=>(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))})})]});function V({targetRoute:e,children:t}){let s,[i,o]=(0,l.useState)(!0),[c,u]=(0,l.useState)(!1),d=(0,a.usePathname)();(0,l.useRef)();let{isPageCached:h}=(0,n.bu)()||{isPageCached:()=>!1};return d!==e&&i||d===e&&i&&!c?(0,r.jsx)("div",{className:"optimistic-loading-container",children:(s=e).startsWith("/dashboard")?(0,r.jsx)(Q,{}):s.startsWith("/pricing")?(0,r.jsx)(W,{}):s.startsWith("/features")?(0,r.jsx)(J,{}):s.startsWith("/auth/")?(0,r.jsx)(K,{}):s.startsWith("/playground")?(0,r.jsx)(q,{}):(0,r.jsx)(G,{})}):(0,r.jsx)("div",{className:`transition-opacity duration-300 ${c?"opacity-100":"opacity-0"}`,children:t})}let Y={maxConcurrent:3,idleTimeout:2e3,hoverDelay:100,backgroundDelay:5e3};function X({children:e}){let{isCollapsed:t,collapseSidebar:s}=(0,i.c)(),{isNavigating:o,targetRoute:c,isPageCached:u}=(0,n.bu)()||{isNavigating:!1,targetRoute:null,isPageCached:()=>!1};return!function(e={}){let t=(0,a.usePathname)();(0,a.useRouter)();let{predictions:s,isLearning:r}=F(),{prefetchRoute:i}=U(),n={...Y,...e},o=(0,l.useRef)([]),c=(0,l.useRef)(new Set),u=(0,l.useRef)(null),d=(0,l.useCallback)(()=>{let e={immediate:[],onIdle:[],onHover:[],background:[]};switch(t){case"/dashboard":e.immediate=["/playground"],e.onIdle=["/my-models","/logs"],e.background=["/routing-setup","/analytics"];break;case"/my-models":e.immediate=["/playground","/routing-setup"],e.onIdle=["/logs"],e.background=["/dashboard","/analytics"];break;case"/playground":e.immediate=["/logs"],e.onIdle=["/my-models"],e.background=["/dashboard","/training"];break;case"/logs":e.immediate=["/playground"],e.onIdle=["/analytics"],e.background=["/my-models","/dashboard"];break;case"/routing-setup":e.immediate=["/playground"],e.onIdle=["/my-models"],e.background=["/logs","/dashboard"];break;default:e.onIdle=["/dashboard","/playground"]}return r&&s.length>0&&(s.slice(0,2).forEach(t=>{e.immediate.includes(t)||e.immediate.unshift(t)}),s.slice(2).forEach(t=>{e.onIdle.includes(t)||e.onIdle.push(t)})),Object.keys(e).forEach(s=>{e[s]=e[s].filter(e=>e!==t)}),e},[t,s,r]),h=(0,l.useCallback)(async(e,t="medium")=>{if(c.current.has(e)||c.current.size>=n.maxConcurrent)return void o.current.push(e);c.current.add(e);try{await i(e,{priority:"medium"===t?"low":t,delay:"high"===t?0:"medium"===t?100:300})}catch(e){}finally{if(c.current.delete(e),o.current.length>0){let e=o.current.shift();e&&setTimeout(()=>h(e,"low"),100)}}},[i,n.maxConcurrent]);(0,l.useCallback)(e=>({onMouseEnter:()=>{setTimeout(()=>{h(e,"high")},n.hoverDelay)}}),[h,n.hoverDelay]),(0,l.useCallback)(()=>({activePreloads:Array.from(c.current),queuedPreloads:[...o.current],strategy:d()}),[d]),(0,l.useCallback)(()=>{c.current.clear(),o.current=[],u.current&&(cancelIdleCallback(u.current),u.current=null)},[]),c.current.size}({maxConcurrent:2,idleTimeout:1500,backgroundDelay:3e3}),(0,r.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,r.jsx)("div",{className:"hidden lg:block fixed left-0 top-0 h-full z-40",children:(0,r.jsx)(H,{})}),(0,r.jsxs)("div",{className:`lg:hidden fixed inset-0 z-50 ${t?"pointer-events-none":""}`,children:[(0,r.jsx)("div",{onClick:s,className:`absolute inset-0 bg-black transition-opacity duration-200 ease-out cursor-pointer ${t?"opacity-0":"opacity-50"}`}),(0,r.jsx)("div",{className:`absolute left-0 top-0 h-full transform transition-transform duration-200 ease-out ${t?"-translate-x-full":"translate-x-0"}`,children:(0,r.jsx)(H,{})})]}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden min-w-0 lg:ml-64",children:[(0,r.jsx)("div",{className:"fixed top-0 right-0 left-0 lg:left-64 z-30",children:(0,r.jsx)(b,{})}),(0,r.jsx)("main",{className:"flex-1 overflow-y-auto content-area mt-16",children:(0,r.jsx)("div",{className:"p-4 sm:p-6 lg:p-8 max-w-7xl mx-auto w-full",children:(0,r.jsx)("div",{className:"page-transition",children:o&&c?(0,r.jsx)(V,{targetRoute:c,children:e}):e})})})]})]})}function Z({children:e}){return(0,r.jsx)(l.Suspense,{fallback:(0,r.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,r.jsx)("div",{className:"hidden lg:block fixed left-0 top-0 h-full w-64 bg-gray-900 animate-pulse"}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden min-w-0 lg:ml-64",children:[(0,r.jsx)("div",{className:"fixed top-0 right-0 left-0 lg:left-64 h-16 bg-white border-b border-gray-200 animate-pulse"}),(0,r.jsx)("main",{className:"flex-1 overflow-y-auto mt-16",children:(0,r.jsx)("div",{className:"p-4 sm:p-6 lg:p-8 max-w-7xl mx-auto w-full",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 animate-pulse"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((e,t)=>(0,r.jsx)("div",{className:"h-32 bg-gray-200 rounded animate-pulse"},t))})]})})})]})]}),children:(0,r.jsx)(X,{children:e})})}function ee({children:e}){let t=(0,a.usePathname)();return"/"===t||t.startsWith("/pricing")||t.startsWith("/features")||t.startsWith("/about")||t.startsWith("/auth/")?(0,r.jsx)(r.Fragment,{children:e}):(0,r.jsx)(i.G,{children:(0,r.jsx)(n.i9,{children:(0,r.jsx)(Z,{children:e})})})}},39727:()=>{},44366:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ConditionalLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\ConditionalLayout.tsx","default")},47990:()=>{},48427:(e,t,s)=>{"use strict";s.d(t,{l2:()=>c,mx:()=>o});var r=s(43210);let a=new Map,i={hits:0,misses:0},n=new Set,l=!1,o=({configId:e,enablePrefetch:t=!0,cacheTimeout:s=3e5,staleTimeout:o=3e4})=>{let[c,u]=(0,r.useState)([]),[d,h]=(0,r.useState)(!1),[m,p]=(0,r.useState)(!1),[g,f]=(0,r.useState)(null),x=(0,r.useRef)(null),y=(0,r.useRef)(null),b=(0,r.useCallback)(async(e,r=!1,l=!1)=>{let c=a.get(e),u=Date.now();if(!r&&c&&u-c.timestamp<s)return i.hits++,u-c.timestamp>o&&!c.isStale&&(c.isStale=!0,t&&(n.add(e),v())),c.data;i.misses++,x.current&&x.current.abort(),x.current=new AbortController;try{let t=`/api/chat/conversations?custom_api_config_id=${e}`,s=await fetch(t,{signal:x.current.signal,headers:{"Cache-Control":"no-cache","X-Requested-With":"XMLHttpRequest"}});if(!s.ok)throw Error(`Failed to fetch chat history: ${s.status} ${s.statusText}`);let r=await s.json();return a.set(e,{data:r,timestamp:u,isStale:!1}),r}catch(e){if("AbortError"===e.name)throw e;if(c&&c.data.length>0)return c.data;throw e}},[s,o,t]),v=(0,r.useCallback)(async()=>{if(!l&&0!==n.size){l=!0;try{let e=Array.from(n);for(let t of(n.clear(),e))try{await b(t,!0,!0),await new Promise(e=>setTimeout(e,100))}catch(e){}}finally{l=!1}}},[b]),w=(0,r.useCallback)(async(t=!1)=>{if(!e)return;let s=a.get(e);!t&&s&&s.data.length>0&&(u(s.data),p(s.isStale),f(null)),h(!0),y.current=e;try{let s=await b(e,t);y.current===e&&(u(s),p(!1),f(null))}catch(t){"AbortError"!==t.name&&y.current===e&&f(`Failed to load chat history: ${t.message}`)}finally{y.current===e&&h(!1)}},[e,b]),j=(0,r.useCallback)(async e=>{t&&(n.add(e),v())},[t,v]),N=(0,r.useCallback)(e=>{e?a.delete(e):a.clear()},[]),S=(0,r.useCallback)(()=>({size:a.size,hits:i.hits,misses:i.misses}),[]);return(0,r.useEffect)(()=>{e?w():(u([]),h(!1),f(null),p(!1))},[e,w]),(0,r.useEffect)(()=>()=>{x.current&&x.current.abort()},[]),{chatHistory:c,isLoading:d,isStale:m,error:g,refetch:w,prefetch:j,invalidateCache:N,getCacheStats:S}},c=()=>{let e=(0,r.useRef)(new Set);return{prefetchChatHistory:(0,r.useCallback)(async t=>{e.current.has(t)||(e.current.add(t),n.add(t),setTimeout(()=>{n.size>0&&(async()=>{if(!l){l=!0;try{let e=Array.from(n);for(let t of(n.clear(),e)){try{let e=`/api/chat/conversations?custom_api_config_id=${t}`,s=await fetch(e,{headers:{"X-Prefetch":"true"}});if(s.ok){let e=await s.json();a.set(t,{data:e,timestamp:Date.now(),isStale:!1})}}catch(e){}await new Promise(e=>setTimeout(e,100))}}finally{l=!1}}})()},200))},[])}}},49831:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},50549:(e,t,s)=>{"use strict";s.d(t,{G:()=>n,c:()=>l});var r=s(60687),a=s(43210);let i=(0,a.createContext)(void 0);function n({children:e}){let[t,s]=(0,a.useState)(!0),[n,l]=(0,a.useState)(!1),[o,c]=(0,a.useState)(!1);return(0,r.jsx)(i.Provider,{value:{isCollapsed:t,isHovered:n,isHoverDisabled:o,toggleSidebar:()=>s(!t),collapseSidebar:()=>s(!0),expandSidebar:()=>s(!1),setHovered:e=>{o||l(e)},setHoverDisabled:e=>{c(e),e&&l(!1)}},children:e})}function l(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useSidebar must be used within a SidebarProvider");return e}},61135:()=>{},62732:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentTitleUpdater.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\DocumentTitleUpdater.tsx","default")},69539:(e,t,s)=>{Promise.resolve().then(s.bind(s,27833)),Promise.resolve().then(s.t.bind(s,47429,23)),Promise.resolve().then(s.bind(s,44366)),Promise.resolve().then(s.bind(s,62732)),Promise.resolve().then(s.bind(s,91695)),Promise.resolve().then(s.bind(s,78676))},70058:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=70058,e.exports=t},78676:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\PerformanceTracker.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\PerformanceTracker.tsx","default")},79481:(e,t,s)=>{"use strict";s.d(t,{u:()=>a});var r=s(79384);function a(){return(0,r.createBrowserClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8")}},80095:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},83611:(e,t,s)=>{Promise.resolve().then(s.bind(s,72847)),Promise.resolve().then(s.t.bind(s,79167,23)),Promise.resolve().then(s.bind(s,38783)),Promise.resolve().then(s.bind(s,11638)),Promise.resolve().then(s.bind(s,34701)),Promise.resolve().then(s.bind(s,3047))},91695:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalPrefetcher.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\GlobalPrefetcher.tsx","default")},93712:(e,t,s)=>{"use strict";s.d(t,{rT:()=>l});var r=s(16189),a=s(43210);let i={"/dashboard":{title:"Dashboard",subtitle:"Overview & analytics"},"/playground":{title:"Playground",subtitle:"Test your models",parent:"/dashboard"},"/my-models":{title:"My Models",subtitle:"API key management",parent:"/dashboard"},"/routing-setup":{title:"Routing Setup",subtitle:"Configure routing",parent:"/dashboard"},"/logs":{title:"Logs",subtitle:"Request history",parent:"/dashboard"},"/training":{title:"Prompt Engineering",subtitle:"Custom prompts",parent:"/dashboard"},"/analytics":{title:"Analytics",subtitle:"Advanced insights",parent:"/dashboard"},"/add-keys":{title:"Add Keys",subtitle:"API key setup",parent:"/my-models"}},n=[{pattern:/^\/my-models\/([^\/]+)$/,getConfig:e=>({title:"Manage Keys",subtitle:"API key management",parent:"/my-models"})},{pattern:/^\/routing-setup\/([^\/]+)$/,getConfig:e=>({title:"Routing Configuration",subtitle:"Advanced routing setup",parent:"/routing-setup"})},{pattern:/^\/routing-setup\/([^\/]+)$/,getConfig:e=>({title:"Routing Setup",subtitle:"Advanced configuration",parent:"/routing-setup"})},{pattern:/^\/playground\?config=([^&]+)/,getConfig:(e,t)=>({title:"Playground",subtitle:"Testing configuration",parent:"/playground"})}];function l(){let e=(0,r.usePathname)(),t=(0,r.useSearchParams)(),s=(0,a.useMemo)(()=>{let s=i[e];if(s)return{title:s.title,subtitle:s.subtitle,parent:s.parent};for(let s of n){let r=e.match(s.pattern);if(r)return s.getConfig(r,t)}let r=e+(t.toString()?`?${t.toString()}`:"");for(let e of n){let s=r.match(e.pattern);if(s)return e.getConfig(s,t)}return{title:"Dashboard",subtitle:"Overview",parent:void 0}},[e,t]),l=(0,a.useMemo)(()=>{let t=[];if(s.parent&&i[s.parent]){let e=i[s.parent];t.push({title:e.title,subtitle:e.subtitle,href:s.parent,isActive:!1})}return t.push({title:s.title,subtitle:s.subtitle,href:e,isActive:!0}),t},[s,e]);return{breadcrumb:(0,a.useMemo)(()=>({title:s.title,subtitle:s.subtitle}),[s]),breadcrumbTrail:l,pageTitle:(0,a.useMemo)(()=>`${s.title} - RouKey`,[s]),currentPage:{title:s.title,subtitle:s.subtitle,path:e}}}},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p,metadata:()=>m});var r=s(37413),a=s(91326),i=s.n(a),n=s(61120);s(61135),s(35760);var l=s(44366),o=s(62732),c=s(78676),u=s(91695),d=s(36162),h=s(27833);let m={title:"RouKey - Smart LLM Key Router",description:"Advanced LLM API key routing and management"};function p({children:e}){return(0,r.jsxs)("html",{lang:"en",className:i().variable,children:[(0,r.jsxs)("head",{children:[(0,r.jsx)("link",{rel:"preload",href:"/api/custom-configs",as:"fetch",crossOrigin:"anonymous"}),(0,r.jsx)("link",{rel:"preload",href:"/api/system-status",as:"fetch",crossOrigin:"anonymous"}),(0,r.jsx)("link",{rel:"dns-prefetch",href:"//fonts.googleapis.com"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:""}),(0,r.jsx)("link",{rel:"prefetch",href:"/dashboard"}),(0,r.jsx)("link",{rel:"prefetch",href:"/playground"}),(0,r.jsx)("link",{rel:"prefetch",href:"/logs"}),(0,r.jsx)("link",{rel:"prefetch",href:"/my-models"})]}),(0,r.jsxs)("body",{className:"font-sans antialiased",children:[(0,r.jsx)(n.Suspense,{fallback:null,children:(0,r.jsx)(o.default,{})}),(0,r.jsx)(u.default,{}),(0,r.jsx)(c.default,{enableUserBehaviorTracking:!0,enableNavigationTracking:!0,enableInteractionTracking:!0}),(0,r.jsx)(l.default,{children:e}),(0,r.jsx)(h.SpeedInsights,{}),!1,(0,r.jsx)(d.default,{id:"sw-register",strategy:"afterInteractive",children:`
            if ('serviceWorker' in navigator) {
              window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                  .then(function(registration) {
                    console.log('✅ Service Worker registered successfully');

                    // Preload critical data after SW is ready
                    if (window.location.pathname === '/') {
                      // Preload landing page data
                      fetch('/api/system-status').catch(() => {});

                      // Prefetch all critical pages immediately
                      setTimeout(() => {
                        const criticalPages = ['/features', '/pricing', '/about', '/auth/signin', '/auth/signup'];
                        criticalPages.forEach(page => {
                          const link = document.createElement('link');
                          link.rel = 'prefetch';
                          link.href = page;
                          document.head.appendChild(link);
                        });
                      }, 500); // Much faster prefetching
                    }
                  })
                  .catch(function(registrationError) {
                    console.warn('⚠️ Service Worker registration failed:', registrationError);
                  });
              });
            }
          `})]})]})}}};
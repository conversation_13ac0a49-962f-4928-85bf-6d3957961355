(()=>{var e={};e.id=2335,e.ids=[2335],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{x:()=>n});var s=r(34386),i=r(44999);async function n(){let e=await (0,i.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},20275:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>g,serverHooks:()=>j,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{DELETE:()=>l,GET:()=>d,POST:()=>c,PUT:()=>p});var i=r(96559),n=r(48088),a=r(37719),o=r(32190),u=r(2507);async function d(e){let t=await (0,u.x)(),{searchParams:r}=new URL(e.url),s=r.get("custom_api_config_id"),i="true"===r.get("active_only");if(!s)return o.NextResponse.json({error:"custom_api_config_id query parameter is required"},{status:400});try{let e=t.from("training_jobs").select("*").eq("custom_api_config_id",s);e=i?e.eq("status","completed").order("created_at",{ascending:!1}).limit(1):e.order("created_at",{ascending:!1});let{data:r,error:n}=await e;if(n)return o.NextResponse.json({error:"Failed to fetch training jobs",details:n.message},{status:500});if(i&&r&&r.length>0){let e=r[0];return o.NextResponse.json({has_training:!0,training_data:e.training_data,job_id:e.id,created_at:e.created_at},{status:200})}return o.NextResponse.json(i?{has_training:!1}:r||[],{status:200})}catch(e){return o.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}async function c(e){let t=await (0,u.x)();try{let{custom_api_config_id:r,name:s,description:i,training_data:n,parameters:a}=await e.json();if(!r||!s)return o.NextResponse.json({error:"Missing required fields: custom_api_config_id, name"},{status:400});let{data:u,error:d}=await t.from("training_jobs").select("id, name, status, created_at").eq("custom_api_config_id",r).order("created_at",{ascending:!1}).limit(1);if(d);else if(u&&u.length>0){let e=u[0];return o.NextResponse.json({...e,warning:"Training job already exists for this configuration. Returned existing job to prevent data loss.",recommendation:"Use PUT method to update existing training job instead."},{status:200})}let{data:c,error:p}=await t.from("training_jobs").insert({custom_api_config_id:r,name:s,description:i,training_data:n,parameters:a,status:"completed",progress_percentage:100,started_at:new Date().toISOString(),completed_at:new Date().toISOString()}).select().single();if(p)return o.NextResponse.json({error:"Failed to create training job",details:p.message},{status:500});return o.NextResponse.json(c,{status:201})}catch(e){if("SyntaxError"===e.name)return o.NextResponse.json({error:"Invalid request body: Malformed JSON."},{status:400});return o.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}async function p(e){let t=await (0,u.x)(),{searchParams:r}=new URL(e.url),s=r.get("id");if(!s)return o.NextResponse.json({error:"id query parameter is required"},{status:400});try{let r=await e.json(),{data:i,error:n}=await t.from("training_jobs").select("id, name, status, custom_api_config_id, created_at").eq("id",s).single();if(n)return o.NextResponse.json({error:"Failed to verify training job exists",details:n.message},{status:500});if(!i)return o.NextResponse.json({error:"Training job not found"},{status:404});let{data:a,error:u}=await t.from("training_jobs").update({...r,updated_at:new Date().toISOString()}).eq("id",s).select().single();if(u)return o.NextResponse.json({error:"Failed to update training job",details:u.message},{status:500});return o.NextResponse.json(a,{status:200})}catch(e){if("SyntaxError"===e.name)return o.NextResponse.json({error:"Invalid request body: Malformed JSON."},{status:400});return o.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}async function l(e){let t=await (0,u.x)(),{searchParams:r}=new URL(e.url),s=r.get("id");if(!s)return o.NextResponse.json({error:"id query parameter is required"},{status:400});try{let{error:e}=await t.from("training_jobs").delete().eq("id",s);if(e)return o.NextResponse.json({error:"Failed to delete training job",details:e.message},{status:500});return o.NextResponse.json({message:"Training job deleted successfully"},{status:200})}catch(e){return o.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}let g=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/training/jobs/route",pathname:"/api/training/jobs",filename:"route",bundlePath:"app/api/training/jobs/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\training\\jobs\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:m,serverHooks:j}=g;function f(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:m})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,3410],()=>r(20275));module.exports=s})();
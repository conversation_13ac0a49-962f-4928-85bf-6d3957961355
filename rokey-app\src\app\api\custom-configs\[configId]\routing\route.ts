import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { z } from 'zod';

// Define the allowed routing strategy types
const ALLOWED_ROUTING_STRATEGIES = [
  'none',
  'intelligent_role',
  'complexity_round_robin',
  'auto_optimal',
  'strict_fallback',
] as const;

const RoutingStrategyEnum = z.enum(ALLOWED_ROUTING_STRATEGIES);

const UpdateRoutingSettingsSchema = z.object({
  routing_strategy: RoutingStrategyEnum,
  routing_strategy_params: z.record(z.any()).nullable().optional(), // JSONB can be null or an object
});

interface RouteParams {
  params: Promise<{
    configId: string;
  }>;
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  const supabase = createSupabaseServerClientFromRequest(request);
  const { configId } = await params;

  // Get authenticated user from session
  const { data: { session }, error: sessionError } = await supabase.auth.getSession();
  if (sessionError || !session?.user) {
    console.error('Authentication error in PUT routing settings:', sessionError);
    return NextResponse.json({ error: 'Unauthorized: You must be logged in to update routing settings.' }, { status: 401 });
  }

  if (!configId || typeof configId !== 'string') {
    return NextResponse.json({ error: 'Invalid configuration ID.' }, { status: 400 });
  }

  let requestBody;
  try {
    requestBody = await request.json();
  } catch (e) {
    return NextResponse.json({ error: 'Invalid JSON request body.' }, { status: 400 });
  }

  const validationResult = UpdateRoutingSettingsSchema.safeParse(requestBody);

  if (!validationResult.success) {
    return NextResponse.json(
      { error: 'Invalid request body.', issues: validationResult.error.flatten().fieldErrors },
      { status: 400 }
    );
  }

  const { routing_strategy, routing_strategy_params } = validationResult.data;

  try {
    // Verify the user owns the custom_api_config or is an admin (for future use)
    // const { data: configData, error: configError } = await supabase
    //   .from('custom_api_configs')
    //   .select('id, user_id')
    //   .eq('id', configId)
    //   .eq('user_id', user.id) // Ensure the authenticated user owns this config
    //   .single();

    // if (configError || !configData) {
    //   if (configError && configError.code === 'PGRST116') { // No rows found
    //     return NextResponse.json({ error: 'Configuration not found or you do not have permission to modify it.' }, { status: 404 });
    //   }
    //   console.error('Error fetching config for permission check:', configError);
    //   return NextResponse.json({ error: 'Error verifying configuration ownership.' }, { status: 500 });
    // }

    // Perform the update
    const { error: updateError } = await supabase
      .from('custom_api_configs')
      .update({
        routing_strategy: routing_strategy,
        routing_strategy_params: routing_strategy_params,
        updated_at: new Date().toISOString(),
      })
      .eq('id', configId);

    if (updateError) {
      console.error('Error updating routing settings in Supabase:', updateError);
      return NextResponse.json({ error: 'Failed to update routing settings.', details: updateError.message }, { status: 500 });
    }

    return NextResponse.json({ message: 'Routing settings updated successfully.', routing_strategy, routing_strategy_params }, { status: 200 });

  } catch (err: any) {
    console.error('Unexpected error in PUT /custom-configs/[configId]/routing:', err);
    return NextResponse.json({ error: 'An unexpected server error occurred.', details: err.message }, { status: 500 });
  }
}

// Optional: OPTIONS handler for CORS preflight if your frontend is on a different domain/port during dev
export async function OPTIONS() {
  return NextResponse.json({}, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*', // Adjust for production
      'Access-Control-Allow-Methods': 'PUT, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
} 
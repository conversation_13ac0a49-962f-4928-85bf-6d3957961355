"use strict";exports.id=2646,exports.ids=[2646],exports.modules={35291:(e,t,o)=>{o.d(t,{AnalyticsSkeleton:()=>c,ConfigSelectorSkeleton:()=>i,MessageSkeleton:()=>s,MyModelsSkeleton:()=>a,RoutingSetupSkeleton:()=>l});var n=o(60687);o(76180),o(43210);let r=({className:e="",variant:t="text",width:o="100%",height:r="1rem",lines:s=1})=>{let i="animate-pulse bg-gray-200 rounded",a=()=>{switch(t){case"circular":return"rounded-full";case"rectangular":return"rounded-lg";default:return"rounded"}},l={width:"number"==typeof o?`${o}px`:o,height:"number"==typeof r?`${r}px`:r};return s>1?(0,n.jsx)("div",{className:`space-y-2 ${e}`,children:Array.from({length:s}).map((e,t)=>(0,n.jsx)("div",{className:`${i} ${a()}`,style:{...l,width:t===s-1?"75%":l.width}},t))}):(0,n.jsx)("div",{className:`${i} ${a()} ${e}`,style:l})},s=()=>(0,n.jsx)("div",{className:"space-y-6 py-8",children:Array.from({length:3}).map((e,t)=>(0,n.jsx)("div",{className:`flex ${t%2==0?"justify-end":"justify-start"}`,children:(0,n.jsx)("div",{className:`max-w-3xl p-4 rounded-2xl ${t%2==0?"bg-orange-50":"bg-white border border-gray-200"}`,children:(0,n.jsx)(r,{lines:3,height:"1rem"})})},t))}),i=()=>(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)(r,{variant:"circular",width:32,height:32}),(0,n.jsx)(r,{width:"8rem",height:"1.5rem"})]}),a=()=>(0,n.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(r,{height:"2.5rem",width:"10rem",className:"mb-2"}),(0,n.jsx)(r,{height:"1.25rem",width:"18rem"})]}),(0,n.jsx)(r,{variant:"rectangular",height:"2.5rem",width:"10rem"})]}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((e,t)=>(0,n.jsxs)("div",{className:"card p-6",children:[(0,n.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)(r,{height:"1.5rem",width:"8rem",className:"mb-2"}),(0,n.jsx)(r,{height:"1rem",width:"12rem"})]}),(0,n.jsx)(r,{variant:"circular",width:32,height:32})]}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)(r,{height:"0.875rem",width:"4rem"}),(0,n.jsx)(r,{height:"0.875rem",width:"2rem"})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)(r,{height:"0.875rem",width:"5rem"}),(0,n.jsx)(r,{height:"0.875rem",width:"3rem"})]})]})]},t))})]}),l=()=>(0,n.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)(r,{height:"3rem",width:"16rem",className:"mx-auto mb-4"}),(0,n.jsx)(r,{height:"1.25rem",width:"24rem",className:"mx-auto"})]}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:Array.from({length:4}).map((e,t)=>(0,n.jsxs)("div",{className:"card p-6",children:[(0,n.jsxs)("div",{className:"flex items-center mb-4",children:[(0,n.jsx)(r,{variant:"circular",width:48,height:48,className:"mr-4"}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)(r,{height:"1.5rem",width:"10rem",className:"mb-2"}),(0,n.jsx)(r,{height:"1rem",width:"8rem"})]})]}),(0,n.jsx)(r,{lines:3,height:"0.875rem"})]},t))})]}),c=()=>(0,n.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(r,{height:"2.5rem",width:"9rem",className:"mb-2"}),(0,n.jsx)(r,{height:"1.25rem",width:"18rem"})]}),(0,n.jsxs)("div",{className:"flex space-x-3",children:[(0,n.jsx)(r,{variant:"rectangular",height:"2.5rem",width:"8rem"}),(0,n.jsx)(r,{variant:"rectangular",height:"2.5rem",width:"6rem"})]})]}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:Array.from({length:3}).map((e,t)=>(0,n.jsxs)("div",{className:"card p-6",children:[(0,n.jsx)(r,{height:"1.25rem",width:"6rem",className:"mb-4"}),(0,n.jsx)(r,{height:"2.5rem",width:"5rem",className:"mb-2"}),(0,n.jsx)(r,{height:"1rem",width:"8rem"})]},t))}),(0,n.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{className:"card p-6",children:[(0,n.jsx)(r,{height:"1.5rem",width:"12rem",className:"mb-6"}),(0,n.jsx)(r,{variant:"rectangular",height:"24rem"})]}),(0,n.jsxs)("div",{className:"card p-6",children:[(0,n.jsx)(r,{height:"1.5rem",width:"10rem",className:"mb-6"}),(0,n.jsx)(r,{variant:"rectangular",height:"24rem"})]})]})]})},47417:(e,t,o)=>{o.d(t,{AnalyticsSkeleton:()=>l,ConfigSelectorSkeleton:()=>s,MessageSkeleton:()=>r,MyModelsSkeleton:()=>i,RoutingSetupSkeleton:()=>a});var n=o(12907);(0,n.registerClientReference)(function(){throw Error("Attempted to call LoadingSkeleton() from the server but LoadingSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","LoadingSkeleton"),(0,n.registerClientReference)(function(){throw Error("Attempted to call ChatHistorySkeleton() from the server but ChatHistorySkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","ChatHistorySkeleton"),(0,n.registerClientReference)(function(){throw Error("Attempted to call EnhancedChatHistorySkeleton() from the server but EnhancedChatHistorySkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","EnhancedChatHistorySkeleton");let r=(0,n.registerClientReference)(function(){throw Error("Attempted to call MessageSkeleton() from the server but MessageSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","MessageSkeleton"),s=(0,n.registerClientReference)(function(){throw Error("Attempted to call ConfigSelectorSkeleton() from the server but ConfigSelectorSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","ConfigSelectorSkeleton");(0,n.registerClientReference)(function(){throw Error("Attempted to call DashboardSkeleton() from the server but DashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","DashboardSkeleton");let i=(0,n.registerClientReference)(function(){throw Error("Attempted to call MyModelsSkeleton() from the server but MyModelsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","MyModelsSkeleton"),a=(0,n.registerClientReference)(function(){throw Error("Attempted to call RoutingSetupSkeleton() from the server but RoutingSetupSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","RoutingSetupSkeleton");(0,n.registerClientReference)(function(){throw Error("Attempted to call TrainingSkeleton() from the server but TrainingSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","TrainingSkeleton");let l=(0,n.registerClientReference)(function(){throw Error("Attempted to call AnalyticsSkeleton() from the server but AnalyticsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","AnalyticsSkeleton");(0,n.registerClientReference)(function(){throw Error("Attempted to call PlaygroundSkeleton() from the server but PlaygroundSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","PlaygroundSkeleton"),(0,n.registerClientReference)(function(){throw Error("Attempted to call LogsSkeleton() from the server but LogsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","LogsSkeleton"),(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LoadingSkeleton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","default")},70440:(e,t,o)=>{o.r(t),o.d(t,{default:()=>r});var n=o(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]}};
(()=>{var e={};e.id=5771,e.ids=[5771],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{Q:()=>n,x:()=>a});var s=r(34386),i=r(44999);async function a(){let e=await (0,i.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}function n(e){return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,r){},remove(e,t){}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},96627:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>c,serverHooks:()=>g,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{POST:()=>p});var i=r(96559),a=r(48088),n=r(37719),o=r(32190),u=r(2507);async function p(e){let t=await (0,u.x)();try{let s,{custom_api_config_id:i,name:a,description:n,training_data:u,parameters:p}=await e.json();if(!i||!a)return o.NextResponse.json({error:"Missing required fields: custom_api_config_id, name"},{status:400});let{data:c,error:d}=await t.from("training_jobs").select("id, name, description, status, training_data, parameters, created_at, updated_at").eq("custom_api_config_id",i).eq("status","completed").order("created_at",{ascending:!1}).limit(1).maybeSingle();if(d)return o.NextResponse.json({error:"Failed to check existing training job",details:d.message},{status:500});let l=!1;if(c){let{data:e,error:r}=await t.from("training_jobs").update({name:a,description:n,training_data:u,parameters:p,updated_at:new Date().toISOString()}).eq("id",c.id).select().single();if(r)return o.NextResponse.json({error:"Failed to update training job",details:r.message},{status:500});s=e,l=!1}else{let{data:e,error:r}=await t.from("training_jobs").insert({custom_api_config_id:i,name:a,description:n,training_data:u,parameters:p,status:"completed",progress_percentage:100,started_at:new Date().toISOString(),completed_at:new Date().toISOString()}).select().single();if(r)return o.NextResponse.json({error:"Failed to create training job",details:r.message},{status:500});s=e,l=!0}if(!l)try{let{trainingDataCache:e}=await r.e(2842).then(r.bind(r,2842));e.invalidate(i)}catch(e){}return o.NextResponse.json({id:s.id,custom_api_config_id:s.custom_api_config_id,name:s.name,description:s.description,status:s.status,training_data:s.training_data,parameters:s.parameters,created_at:s.created_at,updated_at:s.updated_at,operation:l?"created":"updated",message:l?"New training job created successfully":"Existing training job updated successfully - all files preserved"},{status:l?201:200})}catch(e){if("SyntaxError"===e.name)return o.NextResponse.json({error:"Invalid request body: Malformed JSON."},{status:400});return o.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}let c=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/training/jobs/upsert/route",pathname:"/api/training/jobs/upsert",filename:"route",bundlePath:"app/api/training/jobs/upsert/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\training\\jobs\\upsert\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:g}=c;function m(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,3410],()=>r(96627));module.exports=s})();
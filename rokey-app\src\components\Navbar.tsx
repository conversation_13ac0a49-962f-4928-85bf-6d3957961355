'use client';

import Link from 'next/link';
import { useState } from 'react';
import { UserCircleIcon, BellIcon, Cog6ToothIcon, Bars3Icon, ArrowRightOnRectangleIcon, ChevronDownIcon } from '@heroicons/react/24/outline';
import { useSidebar } from '@/contexts/SidebarContext';
import { useBreadcrumb } from '@/hooks/useBreadcrumb';
import { useSubscription } from '@/hooks/useSubscription';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';

export default function Navbar() {
  const { toggleSidebar } = useSidebar();
  const { breadcrumb } = useBreadcrumb();
  const { user, subscriptionStatus } = useSubscription();
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const supabase = createSupabaseBrowserClient();

  // Get user display info
  const firstName = user?.user_metadata?.first_name || user?.user_metadata?.full_name?.split(' ')[0] || 'User';
  const initials = firstName.charAt(0).toUpperCase() + (user?.user_metadata?.last_name?.charAt(0)?.toUpperCase() || firstName.charAt(1)?.toUpperCase() || 'U');

  // Fix subscription tier display - show actual tier names instead of "Free Plan"
  const planName = subscriptionStatus?.hasActiveSubscription
    ? (subscriptionStatus?.tier === 'starter' ? 'Starter Plan' :
       subscriptionStatus?.tier === 'professional' ? 'Professional Plan' :
       subscriptionStatus?.tier === 'enterprise' ? 'Enterprise Plan' :
       'Starter Plan')
    : 'Starter Plan'; // Default to Starter Plan instead of Free Plan

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut();
      window.location.href = '/auth/signin';
    } catch (err) {
      console.error('Sign out error:', err);
    }
  };
  return (
    <nav className="header border-b border-gray-200 bg-white/95 backdrop-blur-sm w-full">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Left side - Mobile menu button and breadcrumb */}
          <div className="flex items-center space-x-4">
            {/* Mobile menu button - visible on mobile only */}
            <button
              onClick={toggleSidebar}
              className="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
              title="Toggle sidebar"
            >
              <Bars3Icon className="h-6 w-6 text-gray-600" />
            </button>

            {/* Mobile logo - visible on mobile only */}
            <div className="lg:hidden">
              <h1 className="text-xl font-bold text-gray-900">RoKey</h1>
            </div>

            {/* Dynamic Breadcrumb - hidden on mobile */}
            <div className="hidden lg:flex items-center space-x-2 text-sm text-gray-600">
              <span>{breadcrumb.title}</span>
              <span>/</span>
              <span className="text-gray-900 font-medium">{breadcrumb.subtitle}</span>
            </div>
          </div>

          {/* Right side - responsive */}
          <div className="flex items-center space-x-2 sm:space-x-4">
            {/* Search - Hidden on mobile, visible on larger screens */}
            <div className="hidden xl:block">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search..."
                  className="w-64 pl-10 pr-4 py-2.5 text-sm bg-white border border-gray-200 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-500 transition-all duration-200"
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Notifications - Always visible */}
            <button className="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 relative">
              <BellIcon className="h-5 w-5 text-gray-600" />
              <span className="absolute -top-1 -right-1 h-3 w-3 bg-orange-500 rounded-full"></span>
            </button>

            {/* Settings Dropdown - Hidden on mobile */}
            <div className="hidden sm:block relative">
              <button
                onClick={() => setIsSettingsOpen(!isSettingsOpen)}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 flex items-center space-x-1"
              >
                <Cog6ToothIcon className="h-5 w-5 text-gray-600" />
                <ChevronDownIcon className={`h-3 w-3 text-gray-600 transition-transform duration-200 ${isSettingsOpen ? 'rotate-180' : ''}`} />
              </button>

              {/* Settings Dropdown Menu */}
              {isSettingsOpen && (
                <>
                  {/* Backdrop */}
                  <div
                    className="fixed inset-0 z-10"
                    onClick={() => setIsSettingsOpen(false)}
                  />

                  {/* Dropdown */}
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-20">
                    <Link
                      href="/dashboard/settings"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200"
                      onClick={() => setIsSettingsOpen(false)}
                    >
                      <Cog6ToothIcon className="h-4 w-4 mr-3 text-gray-500" />
                      Settings
                    </Link>

                    <Link
                      href="/dashboard/billing"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200"
                      onClick={() => setIsSettingsOpen(false)}
                    >
                      <UserCircleIcon className="h-4 w-4 mr-3 text-gray-500" />
                      Billing
                    </Link>

                    <hr className="my-1 border-gray-200" />

                    <button
                      onClick={handleSignOut}
                      className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200"
                    >
                      <ArrowRightOnRectangleIcon className="h-4 w-4 mr-3 text-red-500" />
                      Sign Out
                    </button>
                  </div>
                </>
              )}
            </div>

            {/* User Profile - Responsive */}
            <div className="flex items-center space-x-3 px-2 sm:px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer">
              <div className="w-8 h-8 rounded-full bg-gradient-to-br from-orange-400 to-orange-500 flex items-center justify-center">
                <span className="text-white font-semibold text-sm">{initials}</span>
              </div>
              <div className="hidden md:block">
                <p className="text-sm font-medium text-gray-900">{firstName}</p>
                <p className="text-xs text-gray-500">{planName}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}
(()=>{var e={};e.id=2303,e.ids=[2303],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7117:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var t=r(65239),i=r(48088),n=r(88170),a=r.n(n),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(s,l);let d={children:["",{children:["debug-session",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,39189)),"C:\\RoKey App\\rokey-app\\src\\app\\debug-session\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\debug-session\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/debug-session/page",pathname:"/debug-session",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8415:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var t=r(60687),i=r(43210),n=r(79481);function a(){let[e,s]=(0,i.useState)(null),[r,a]=(0,i.useState)(!0),o=(0,n.u)(),l=async()=>{try{a(!0);let{data:e,error:s}=await o.auth.signInWithPassword({email:"<EMAIL>",password:"test123456"});s?alert("Sign in error: "+s.message):alert("Sign in successful!")}catch(e){alert("Sign in error: "+(e instanceof Error?e.message:String(e)))}finally{a(!1)}},d=async()=>{try{a(!0),await o.auth.signOut(),alert("Signed out successfully!")}catch(e){alert("Sign out error: "+(e instanceof Error?e.message:String(e)))}finally{a(!1)}};return r?(0,t.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,t.jsx)("p",{children:"Loading session info..."})]})}):(0,t.jsx)("div",{className:"min-h-screen bg-gray-100 p-8",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Session Debug Page"}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 mb-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Current Session Info"}),(0,t.jsx)("pre",{className:"bg-gray-100 p-4 rounded text-sm overflow-auto",children:JSON.stringify(e,null,2)})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Test Actions"}),(0,t.jsxs)("div",{className:"space-x-4",children:[(0,t.jsx)("button",{onClick:l,className:"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600",disabled:r,children:"Test Sign In"}),(0,t.jsx)("button",{onClick:d,className:"bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600",disabled:r,children:"Test Sign Out"}),(0,t.jsx)("button",{onClick:()=>window.location.reload(),className:"bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600",children:"Refresh Page"})]})]}),(0,t.jsxs)("div",{className:"mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"font-semibold text-yellow-800 mb-2",children:"Instructions:"}),(0,t.jsxs)("ol",{className:"list-decimal list-inside text-yellow-700 space-y-1",children:[(0,t.jsx)("li",{children:"Check the current session info above"}),(0,t.jsx)("li",{children:'Try the "Test Sign In" button (update the email/password in the code if needed)'}),(0,t.jsx)("li",{children:"Check if session info updates after sign in"}),(0,t.jsx)("li",{children:"Try navigating to /checkout after successful sign in"})]})]})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},39189:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\debug-session\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\debug-session\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59183:(e,s,r)=>{Promise.resolve().then(r.bind(r,39189))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67431:(e,s,r)=>{Promise.resolve().then(r.bind(r,8415))},70440:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i});var t=r(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,1641,1658,7437],()=>r(7117));module.exports=t})();
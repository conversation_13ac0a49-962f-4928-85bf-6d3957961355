(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7637],{3408:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>i});var s=n(95155),r=n(12115);function i(){let[e,t]=(0,r.useState)([]),[n,i]=(0,r.useState)(""),[a,o]=(0,r.useState)([]),[l,c]=(0,r.useState)(!1),[d,u]=(0,r.useState)(null),[m,h]=(0,r.useState)(null),[p,g]=(0,r.useState)(""),x=async e=>{if(e)try{let n=await fetch("/api/training/jobs?custom_api_config_id=".concat(e));if(n.ok){let e=await n.json();if(e.length>0){var t;let n=e[0];(null==(t=n.training_data)?void 0:t.raw_prompts)&&g(n.training_data.raw_prompts)}}}catch(e){}};(0,r.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/custom-configs");if(!e.ok)throw Error("Failed to fetch configurations");let n=await e.json();t(n),n.length>0&&(i(n[0].id),x(n[0].id))}catch(e){u("Failed to load configurations: ".concat(e.message))}})()},[]),(0,r.useEffect)(()=>{n&&x(n)},[n]);let f=e=>{let t={system_instructions:"",examples:[],behavior_guidelines:"",general_instructions:""};for(let n of e.split("\n").filter(e=>e.trim())){let e=n.trim();if(e.startsWith("SYSTEM:"))t.system_instructions+=e.replace("SYSTEM:","").trim()+"\n";else if(e.startsWith("BEHAVIOR:"))t.behavior_guidelines+=e.replace("BEHAVIOR:","").trim()+"\n";else if(e.includes("→")||e.includes("->")){let n=e.includes("→")?"→":"->",s=e.split(n);if(s.length>=2){let e=s[0].trim(),r=s.slice(1).join(n).trim();t.examples.push({input:e,output:r})}}else e.length>0&&(t.general_instructions+=e+"\n")}return t},b=async()=>{if(!n||!p.trim())return void u("Please select an API configuration and provide training prompts.");if(!l){c(!0),u(null),h(null);try{var t;let s=f(p),r=(null==(t=e.find(e=>e.id===n))?void 0:t.name)||"Unknown Config",i={custom_api_config_id:n,name:"".concat(r," Training - ").concat(new Date().toLocaleDateString()),description:"Training job for ".concat(r," with ").concat(s.examples.length," examples"),training_data:{processed_prompts:s,raw_prompts:p.trim(),last_prompt_update:new Date().toISOString()},parameters:{training_type:"prompt_engineering",created_via:"training_page",version:"1.0"}},a=await fetch("/api/training/jobs/upsert",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)});if(!a.ok){let e=await a.text();throw Error("Failed to save training job: ".concat(a.status," ").concat(e))}let o=await a.json(),l="updated"===o.operation,c="".concat(l?"\uD83D\uDD04":"\uD83C\uDF89"," Prompt Engineering ").concat(l?"updated":"completed"," successfully!\n\n")+'Your "'.concat(r,'" configuration has been ').concat(l?"updated":"enhanced"," with:\n")+"• ".concat(s.examples.length," training examples\n")+"• Custom system instructions and behavior guidelines\n\n✨ All future chats using this configuration will automatically:\n• Follow your training examples\n• Apply your behavior guidelines\n• Maintain consistent personality and responses\n\n"+"\uD83D\uDE80 Try it now in the Playground to see your ".concat(l?"updated":"enhanced"," model in action!\n\n")+"\uD83D\uDCA1 Your training prompts remain here so you can modify them anytime.";h(c)}catch(e){u("Failed to create prompt engineering: ".concat(e.message))}finally{c(!1)}}};return(0,s.jsx)("div",{className:"min-h-screen bg-[#faf8f5] p-6",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Prompt Engineering"}),(0,s.jsx)("p",{className:"text-lg text-gray-600 max-w-3xl",children:"Create custom prompts to enhance your AI models with specific instructions, behavior guidelines, and examples."})]}),d&&(0,s.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 rounded-xl p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("svg",{className:"w-5 h-5 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,s.jsx)("p",{className:"text-red-800 text-sm font-medium",children:d})]})}),m&&(0,s.jsx)("div",{className:"mb-6 bg-green-50 border border-green-200 rounded-xl p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("svg",{className:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,s.jsx)("p",{className:"text-green-800 text-sm font-medium",children:m})]})}),(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg p-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Create Custom Prompts"}),(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"configSelect",className:"block text-sm font-medium text-gray-700 mb-2",children:"Select API Configuration"}),(0,s.jsxs)("select",{id:"configSelect",value:n,onChange:e=>i(e.target.value),className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm",children:[(0,s.jsx)("option",{value:"",children:"Choose which model to train..."}),e.map(e=>(0,s.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"trainingPrompts",className:"block text-sm font-medium text-gray-700 mb-2",children:"Custom Prompts & Instructions"}),(0,s.jsx)("textarea",{id:"trainingPrompts",value:p,onChange:e=>g(e.target.value),placeholder:"Enter your training prompts using these formats:\n\nSYSTEM: You are a helpful customer service agent for our company\nBEHAVIOR: Always be polite and offer solutions\n\nUser asks about returns → I'd be happy to help with your return! Let me check our policy for you.\nCustomer is frustrated → I understand your frustration. Let me see how I can resolve this for you.\n\nGeneral instructions can be written as regular text.",rows:12,className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm resize-none font-mono"}),(0,s.jsxs)("div",{className:"mt-3 bg-blue-50 border border-blue-200 rounded-lg p-3",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-blue-900 mb-2",children:"Training Format Guide:"}),(0,s.jsxs)("ul",{className:"text-xs text-blue-800 space-y-1",children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"SYSTEM:"})," Core instructions for the AI's role and personality"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"BEHAVIOR:"})," Guidelines for how the AI should behave"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Examples:"}),' Use "User input → Expected response" format']}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"General:"})," Any other instructions written as normal text"]})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center pt-6 border-t border-gray-200",children:[(0,s.jsx)("div",{className:"flex space-x-3",children:(0,s.jsx)("button",{type:"button",className:"btn-secondary",onClick:()=>{confirm("Clear all training prompts?")&&g("")},children:"Clear Form"})}),(0,s.jsx)("button",{type:"button",onClick:b,disabled:!n||!p.trim()||l,className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:l?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Processing Prompts..."]}):"Save Prompts"})]})]})]})]})})}},42005:(e,t,n)=>{Promise.resolve().then(n.bind(n,3408))}},e=>{var t=t=>e(e.s=t);e.O(0,[4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(42005)),_N_E=e.O()}]);
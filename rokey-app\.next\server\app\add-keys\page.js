(()=>{var e={};e.id=5833,e.ids=[5833],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20404:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(43210);function a(){let[e,t]=(0,s.useState)({isOpen:!1,isLoading:!1,title:"",message:"",confirmText:"Confirm",cancelText:"Cancel",type:"danger",onConfirm:()=>{}}),r=(0,s.useCallback)((e,r)=>{t({isOpen:!0,isLoading:!1,title:e.title,message:e.message,confirmText:e.confirmText||"Confirm",cancelText:e.cancelText||"Cancel",type:e.type||"danger",onConfirm:async()=>{t(e=>({...e,isLoading:!0}));try{await r(),t(e=>({...e,isOpen:!1,isLoading:!1}))}catch(e){throw t(e=>({...e,isLoading:!1})),e}}})},[]),a=(0,s.useCallback)(()=>{t(e=>({...e,isOpen:!1,isLoading:!1}))},[]);return{...e,showConfirmation:r,hideConfirmation:a}}},26403:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))})},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31371:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(60687),a=r(43210),l=r(66368),o=r(50181),i=r(20404);let n=l.MG.map(e=>({value:e.id,label:e.name})),d=e=>{let t=l.MG.find(t=>t.id===e);return t?t.models.map(e=>({value:e.id,label:e.name})):[]};function c(){let e=(0,i.Z)(),[t,r]=(0,a.useState)(n[0]?.value||"openai"),[l,c]=(0,a.useState)(""),[u,p]=(0,a.useState)(""),[m,x]=(0,a.useState)(""),[h,g]=(0,a.useState)(!1),[y,b]=(0,a.useState)(null),[f,v]=(0,a.useState)(null),[w,j]=(0,a.useState)([]),[k,N]=(0,a.useState)(!0),[C,A]=(0,a.useState)(null),P=async()=>{N(!0);let e=null;try{let e=await fetch("/api/keys");if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to fetch keys")}let t=await e.json();j(t),b(null)}catch(e){b(`Error fetching keys: ${e.message}`)}N(!1)},_=async e=>{e.preventDefault(),g(!0),b(null),v(null);try{let e=await fetch("/api/keys",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({provider:t,predefined_model_id:l,api_key_raw:u,label:m,custom_api_config_id:""})}),s=await e.json();if(!e.ok)throw Error(s.details||s.error||"Failed to save API key");v(`API key "${m}" saved successfully!`),r(n[0]?.value||"openai"),c(""),p(""),x(""),await P()}catch(e){b(e.message),v(null)}g(!1)},L=(t,r)=>{e.showConfirmation({title:"Delete API Key",message:`Are you sure you want to delete the API key "${r}"? This action cannot be undone.`,confirmText:"Delete API Key",cancelText:"Cancel",type:"danger"},async()=>{A(t),b(null),v(null);try{let e=await fetch(`/api/keys/${t}`,{method:"DELETE"}),s=await e.json();if(!e.ok)throw Error(s.details||s.error||"Failed to delete API key");v(`API key "${r}" deleted successfully!`),j(e=>e.filter(e=>e.id!==t))}catch(e){throw b(e.message),v(null),e}finally{A(null)}})},E=d(t);return(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Add API Key"}),(0,s.jsxs)("form",{onSubmit:_,className:"space-y-6 bg-gray-800 p-6 rounded-lg shadow-xl max-w-lg",children:[y&&(0,s.jsxs)("p",{className:"text-red-400 bg-red-900/50 p-3 rounded-md",children:["Error: ",y]}),f&&(0,s.jsx)("p",{className:"text-green-400 bg-green-900/50 p-3 rounded-md",children:f}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"provider",className:"block text-sm font-medium text-gray-300 mb-1",children:"Provider"}),(0,s.jsx)("select",{id:"provider",value:t,onChange:e=>r(e.target.value),className:"w-full p-2.5 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-blue-500 focus:border-blue-500",children:n.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"modelId",className:"block text-sm font-medium text-gray-300 mb-1",children:"Model ID"}),(0,s.jsx)("select",{id:"modelId",value:l,onChange:e=>c(e.target.value),disabled:!E.length,className:"w-full p-2.5 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50",children:E.length>0?E.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value)):(0,s.jsx)("option",{value:"",disabled:!0,children:"Select a provider first or no models configured"})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"apiKey",className:"block text-sm font-medium text-gray-300 mb-1",children:"API Key"}),(0,s.jsx)("input",{type:"password",id:"apiKey",value:u,onChange:e=>p(e.target.value),required:!0,className:"w-full p-2.5 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your API key"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"label",className:"block text-sm font-medium text-gray-300 mb-1",children:"Label"}),(0,s.jsx)("input",{type:"text",id:"label",value:m,onChange:e=>x(e.target.value),required:!0,className:"w-full p-2.5 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-blue-500 focus:border-blue-500",placeholder:"e.g., My Personal OpenAI Key"})]}),(0,s.jsx)("button",{type:"submit",disabled:h,className:"w-full px-4 py-2.5 text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:ring-4 focus:ring-blue-800 disabled:opacity-50 font-medium",children:h?"Saving...":"Save API Key"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Saved API Keys"}),k?(0,s.jsx)("p",{className:"text-gray-400",children:"Loading keys..."}):y&&0===w.length?(0,s.jsxs)("p",{className:"text-red-400 bg-red-900/50 p-3 rounded-md",children:["Could not load keys: ",y.replace("Error fetching keys: ","")]}):0===w.length?(0,s.jsx)("p",{className:"text-gray-400",children:"No API keys saved yet."}):(0,s.jsx)("div",{className:"overflow-x-auto bg-gray-800 p-4 rounded-lg shadow-xl",children:(0,s.jsxs)("table",{className:"min-w-full text-sm text-left text-gray-300",children:[(0,s.jsx)("thead",{className:"text-xs text-gray-400 uppercase bg-gray-700",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{scope:"col",className:"px-6 py-3",children:"Label"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-3",children:"Provider"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-3",children:"Predefined Model ID"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-3",children:"Status"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-3",children:"Created At"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-3",children:"Last Used"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-3",children:(0,s.jsx)("span",{className:"sr-only",children:"Actions"})})]})}),(0,s.jsx)("tbody",{children:w.map(e=>(0,s.jsxs)("tr",{className:"border-b border-gray-700 hover:bg-gray-700/50",children:[(0,s.jsx)("td",{className:"px-6 py-4 font-medium whitespace-nowrap text-white",children:e.label}),(0,s.jsx)("td",{className:"px-6 py-4",children:e.provider}),(0,s.jsx)("td",{className:"px-6 py-4",children:e.predefined_model_id}),(0,s.jsx)("td",{className:"px-6 py-4",children:(0,s.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full 
                        ${"active"===e.status?"bg-green-900 text-green-300":"inactive"===e.status?"bg-yellow-900 text-yellow-300":"bg-red-900 text-red-300"}`,children:e.status})}),(0,s.jsx)("td",{className:"px-6 py-4",children:new Date(e.created_at).toLocaleDateString()}),(0,s.jsx)("td",{className:"px-6 py-4",children:e.last_used_at?new Date(e.last_used_at).toLocaleDateString():"Never"}),(0,s.jsx)("td",{className:"px-6 py-4 text-right",children:(0,s.jsx)("button",{onClick:()=>L(e.id,e.label),disabled:C===e.id,className:"font-medium text-red-500 hover:text-red-400 disabled:opacity-50 disabled:cursor-not-allowed",children:C===e.id?"Deleting...":"Delete"})})]},e.id))})]})})]}),(0,s.jsx)(o.A,{isOpen:e.isOpen,onClose:e.hideConfirmation,onConfirm:e.onConfirm,title:e.title,message:e.message,confirmText:e.confirmText,cancelText:e.cancelText,type:e.type,isLoading:e.isLoading})]})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},49627:(e,t,r)=>{Promise.resolve().then(r.bind(r,31371))},50181:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(60687);r(43210);var a=r(26403),l=r(59168),o=r(81836);function i({isOpen:e,onClose:t,onConfirm:r,title:i,message:n,confirmText:d="Delete",cancelText:c="Cancel",type:u="danger",isLoading:p=!1}){let m=(()=>{switch(u){case"danger":return{iconBg:"bg-red-100",iconColor:"text-red-600",confirmButton:"bg-red-600 hover:bg-red-700 focus:ring-red-500",icon:a.A};case"warning":return{iconBg:"bg-yellow-100",iconColor:"text-yellow-600",confirmButton:"bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500",icon:l.A};default:return{iconBg:"bg-blue-100",iconColor:"text-blue-600",confirmButton:"bg-blue-600 hover:bg-blue-700 focus:ring-blue-500",icon:l.A}}})(),x=m.icon;return e?(0,s.jsxs)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:[(0,s.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300",onClick:p?void 0:t}),(0,s.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"relative w-full max-w-md transform overflow-hidden rounded-2xl bg-white shadow-2xl transition-all duration-300 scale-100 opacity-100",children:[(0,s.jsx)("div",{className:"relative px-6 pt-6",children:(0,s.jsx)("button",{onClick:t,disabled:p,className:"absolute top-4 right-4 p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,s.jsx)(o.A,{className:"h-5 w-5"})})}),(0,s.jsxs)("div",{className:"px-6 pb-6",children:[(0,s.jsx)("div",{className:"mx-auto flex h-16 w-16 items-center justify-center rounded-full mb-6",children:(0,s.jsx)("div",{className:`${m.iconBg} rounded-full p-3`,children:(0,s.jsx)(x,{className:`h-8 w-8 ${m.iconColor}`})})}),(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3 text-center",children:i}),(0,s.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed mb-8 text-center",children:n}),(0,s.jsxs)("div",{className:"flex flex-col-reverse sm:flex-row sm:justify-center gap-3",children:[(0,s.jsx)("button",{type:"button",onClick:t,disabled:p,className:"w-full sm:w-auto px-6 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200",children:c}),(0,s.jsx)("button",{type:"button",onClick:r,disabled:p,className:`w-full sm:w-auto px-6 py-3 text-sm font-medium text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 ${m.confirmButton}`,children:p?(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Processing..."]}):d})]})]})]})})]}):null}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59168:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66368:(e,t,r)=>{"use strict";r.d(t,{MG:()=>s});let s=[{id:"openai",name:"OpenAI",apiBaseUrl:"https://api.openai.com/v1/chat/completions",models:[]},{id:"google",name:"Google",apiBaseUrl:"https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",models:[]},{id:"anthropic",name:"Anthropic",apiBaseUrl:"https://api.anthropic.com/v1/chat/completions",models:[]},{id:"deepseek",name:"DeepSeek",apiBaseUrl:"https://api.deepseek.com/chat/completions",models:[]},{id:"xai",name:"xAI (Grok)",apiBaseUrl:"https://api.x.ai/v1/chat/completions",models:[]},{id:"openrouter",name:"OpenRouter",apiBaseUrl:"https://openrouter.ai/api/v1/chat/completions",models:[]}]},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},72337:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\add-keys\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\add-keys\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},78357:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),l=r(88170),o=r.n(l),i=r(30893),n={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);r.d(t,n);let d={children:["",{children:["add-keys",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,72337)),"C:\\RoKey App\\rokey-app\\src\\app\\add-keys\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\add-keys\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/add-keys/page",pathname:"/add-keys",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},81836:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))})},83971:(e,t,r)=>{Promise.resolve().then(r.bind(r,72337))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,2252,1658,7437],()=>r(78357));module.exports=s})();
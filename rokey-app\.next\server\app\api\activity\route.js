(()=>{var e={};e.id=3577,e.ids=[3577],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{Q:()=>a,x:()=>o});var s=r(34386),i=r(44999);async function o(){let e=await (0,i.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}function a(e){return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,r){},remove(e,t){}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},43741:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>p});var i=r(96559),o=r(48088),a=r(37719),n=r(32190),u=r(2507);async function p(e){try{let t=await (0,u.x)();if(!t)return n.NextResponse.json({error:"Server configuration error."},{status:500});let{searchParams:r}=new URL(e.url),s=parseInt(r.get("limit")||"10"),{data:i,error:o}=await t.from("request_logs").select(`
        id,
        request_timestamp,
        status_code,
        llm_model_name,
        llm_provider_name,
        error_message,
        cost,
        input_tokens,
        output_tokens,
        custom_api_config_id
      `).order("request_timestamp",{ascending:!1}).limit(s);if(o)return n.NextResponse.json({error:"Failed to fetch activity data"},{status:500});let{data:a,error:p}=await t.from("api_keys").select(`
        id,
        created_at,
        label,
        provider,
        predefined_model_id
      `).order("created_at",{ascending:!1}).limit(5),{data:c,error:d}=await t.from("custom_api_configs").select(`
        id,
        updated_at,
        name,
        routing_strategy
      `).order("updated_at",{ascending:!1}).limit(5),m=[];i&&i.forEach(e=>{e.status_code>=200&&e.status_code;let t=e.status_code>=400,r="Request completed",s="success";t?(r="Request failed",s="error"):e.status_code>=300&&(r="Request redirected",s="warning"),m.push({id:`request-${e.id}`,type:"request",action:r,model:e.llm_model_name||"Unknown Model",provider:e.llm_provider_name,timestamp:e.request_timestamp,status:s,details:e.error_message,cost:e.cost,tokens:e.input_tokens&&e.output_tokens?`${e.input_tokens} in, ${e.output_tokens} out`:null})}),a&&a.forEach(e=>{m.push({id:`key-${e.id}`,type:"api_key",action:"New API key added",model:e.predefined_model_id||e.provider,provider:e.provider,timestamp:e.created_at,status:"info",details:e.label})}),c&&c.forEach(e=>{m.push({id:`config-${e.id}`,type:"config",action:"Configuration updated",model:e.name,provider:e.routing_strategy,timestamp:e.updated_at,status:"info",details:`Routing strategy: ${e.routing_strategy}`})}),m.sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime());let l=m.slice(0,s).map(e=>({...e,time:function(e){let t=Math.floor((new Date().getTime()-e.getTime())/1e3);return t<60?"Just now":t<3600?`${Math.floor(t/60)} minutes ago`:t<86400?`${Math.floor(t/3600)} hours ago`:`${Math.floor(t/86400)} days ago`}(new Date(e.timestamp))}));return n.NextResponse.json({activities:l,total:m.length})}catch(e){return n.NextResponse.json({error:"Internal server error",details:e.message},{status:500})}}let c=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/activity/route",pathname:"/api/activity",filename:"route",bundlePath:"app/api/activity/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\activity\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:m,serverHooks:l}=c;function g(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:m})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,3410],()=>r(43741));module.exports=s})();
(()=>{var e={};e.id=8246,e.ids=[2842,8246],e.modules={507:(e,t,s)=>{"use strict";s.d(t,{Dc:()=>n,p2:()=>r});let r=[{id:"general_chat",name:"General Chat",description:"Handles general conversation, Q&A, and tasks not covered by other specific roles. Often the default fallback."},{id:"logic_reasoning",name:"Logic & Reasoning",description:"For tasks requiring logical deduction, problem-solving, mathematical reasoning, and complex analytical thinking."},{id:"writing",name:"Writing & Content Creation",description:"For all writing tasks, including articles, blog posts, marketing copy, creative content, essays, and more."},{id:"coding_frontend",name:"Coding - Frontend",description:"For generating and assisting with HTML, CSS, JavaScript, and frontend frameworks (React, Vue, Angular, etc.)."},{id:"coding_backend",name:"Coding - Backend",description:"For generating and assisting with server-side logic, APIs, databases, and backend frameworks (Node.js, Python, Java, etc.)."},{id:"research_synthesis",name:"Research & Synthesis",description:"For information retrieval from various sources, data analysis, and synthesizing findings into reports or summaries."},{id:"summarization_briefing",name:"Summarization & Briefing",description:"For condensing long texts, documents, or conversations into concise summaries or executive briefings."},{id:"translation_localization",name:"Translation & Localization",description:"For translating text between languages and adapting content culturally for different locales."},{id:"data_extraction_structuring",name:"Data Extraction & Structuring",description:"For identifying and extracting specific pieces of information from unstructured/semi-structured text and organizing it."},{id:"brainstorming_ideation",name:"Brainstorming & Ideation",description:"For generating new ideas, exploring concepts, and creative problem-solving sessions."},{id:"education_tutoring",name:"Education & Tutoring",description:"For explaining concepts, answering educational questions, and providing tutoring assistance across various subjects."},{id:"image_generation",name:"Image Generation",description:"For creating images from textual descriptions. Assign to keys linked to image generation models."},{id:"audio_transcription",name:"Audio Transcription",description:"For converting speech from audio files into written text. Assign to keys linked to transcription models."}],n=e=>r.find(t=>t.id===e)},2507:(e,t,s)=>{"use strict";s.d(t,{Q:()=>i,x:()=>a});var r=s(34386),n=s(44999);async function a(){let e=await (0,n.UL)();return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,s,r){try{e.set({name:t,value:s,...r})}catch(e){}},remove(t,s){try{e.set({name:t,value:"",...s})}catch(e){}}}})}function i(e){return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,s){},remove(e,t){}}})}},2842:(e,t,s)=>{"use strict";s.d(t,{trainingDataCache:()=>n});class r{set(e,t,s){this.cache.set(e,{data:t,timestamp:Date.now(),jobId:s})}get(e){let t=this.cache.get(e);return t?Date.now()-t.timestamp>this.TTL?(this.cache.delete(e),null):t:null}invalidate(e){return this.cache.delete(e)}clear(){this.cache.clear()}cleanup(){let e=Date.now();for(let[t,s]of this.cache.entries())e-s.timestamp>this.TTL&&this.cache.delete(t)}getStats(){return{size:this.cache.size,keys:Array.from(this.cache.keys())}}constructor(){this.cache=new Map,this.TTL=3e5}}let n=new r;setInterval(()=>{n.cleanup()},6e5)},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56534:(e,t,s)=>{"use strict";s.d(t,{Y:()=>c,w:()=>l});var r=s(55511),n=s.n(r);let a="aes-256-gcm",i=process.env.ROKEY_ENCRYPTION_KEY;if(!i||64!==i.length)throw Error("Invalid ROKEY_ENCRYPTION_KEY. Please set a 64-character hex string (32 bytes) for ROKEY_ENCRYPTION_KEY in your .env.local file.");let o=Buffer.from(i,"hex");function l(e){if("string"!=typeof e||0===e.length)throw Error("Encryption input must be a non-empty string.");let t=n().randomBytes(12),s=n().createCipheriv(a,o,t),r=s.update(e,"utf8","hex");r+=s.final("hex");let i=s.getAuthTag();return`${t.toString("hex")}:${i.toString("hex")}:${r}`}function c(e){if("string"!=typeof e||0===e.length)throw Error("Decryption input must be a non-empty string.");let t=e.split(":");if(3!==t.length)throw Error("Invalid encrypted text format. Expected iv:authTag:encryptedData");let s=Buffer.from(t[0],"hex"),r=Buffer.from(t[1],"hex"),i=t[2];if(12!==s.length)throw Error("Invalid IV length. Expected 12 bytes.");if(16!==r.length)throw Error("Invalid authTag length. Expected 16 bytes.");let l=n().createDecipheriv(a,o,s);l.setAuthTag(r);let c=l.update(i,"hex","utf8");return c+l.final("utf8")}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},98811:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>eS,routeModule:()=>ek,serverHooks:()=>eI,workAsyncStorage:()=>ev,workUnitAsyncStorage:()=>ex});var r={};s.r(r),s.d(r,{OPTIONS:()=>ef,POST:()=>eh});var n=s(96559),a=s(48088),i=s(37719),o=s(32190),l=s(2507),c=s(56534),d=s(507),u=s(45697);let p={defaultKeySuccess:(e=1)=>1===e?"default_key_success":`default_key_success_attempt_${e}`,allKeysFailed:e=>`default_all_${e}_attempts_failed`,roleRouting:e=>e,intelligentRoleRouting:e=>`intelligent_role_${e}`,fallbackRouting:e=>`fallback_position_${e}`};var m=s(55511),g=s.n(m),h=s(2842),f=s(55591),_=s.n(f),y=s(81630),w=s.n(y),k=s(79551);let v=new Map,x=new Map,I=new Map;function S(e,t){x.has(e)||x.set(e,{});let s=x.get(e);s[t]||(s[t]={count:0,lastUsed:0}),s[t].count++,s[t].lastUsed=Date.now()}let b=new Map;async function C(e,t,s,r,n=[]){let a=F(e);if(b.has(a))try{return await b.get(a)}catch(e){b.delete(a)}let i=A(e,t,s,r,n);b.set(a,i);try{return await i}catch(e){throw e}finally{b.delete(a)}}async function A(e,t,s,r,n=[]){let a=n?n.slice(-3).map(e=>`${e.role}: ${"string"==typeof e.content?e.content.substring(0,200):Array.isArray(e.content)?e.content.find(e=>"text"===e.type)?.text?.substring(0,200)||"[non-text]":"[unknown]"}`).join("\n"):"",i=await T(e,t,s,a);if(i.isMultiRole)return i;let o=t.map(e=>`- Role ID: "${e.id}", Name: "${e.name}", Description: "${(e.description||"N/A").substring(0,150)}"`).join("\n"),l=`Available Roles:
${o}

Recent Conversation:
${a}

Current Request: "${e.substring(0,2e3)}"

Most Appropriate Role ID:`,c=await en("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`,Connection:"keep-alive","User-Agent":"RoKey/1.0 (Classification-Optimized)",Origin:"https://rokey.app"},body:JSON.stringify({model:"gemini-2.0-flash-lite",messages:[{role:"system",content:"You are an expert task classification system. Analyze the user's request considering both the current message AND recent conversation context. Key rules: 1) If user is responding to options/choices in an ongoing task (like '1, 2, 3' after coding options), continue with same role. 2) Only switch roles for clear new tasks ('write story', 'solve math', etc.). 3) Examples: 'write code'=coding roles, 'write story'=writing roles, 'solve math'=logic_reasoning. Respond with ONLY the Role ID string."},{role:"user",content:l}],temperature:.1,max_tokens:50})},2,et.CLASSIFICATION);if(!c.ok)throw Error(`Gemini API error: ${c.status}`);let d=await c.json(),u=d.choices?.[0]?.message?.content?.trim().replace(/["'`]/g,"")||null;if(u){u=u.replace(/^(Role ID:\s*|Role:\s*|Classification:\s*)/i,"").trim();let e=t.find(e=>e.id===u);if(e||(e=t.find(e=>e.id.toLowerCase()===u.toLowerCase())),e||(e=t.find(e=>e.name&&e.name.toLowerCase()===u.toLowerCase())),!e)return{roleId:u,confidence:.5};u=e.id}else throw Error("Empty classification result");return{roleId:u,confidence:.95}}async function T(e,t,s,r=""){let n=t.map(e=>`- Role ID: "${e.id}", Name: "${e.name}", Description: "${(e.description||"N/A").substring(0,150)}"`).join("\n"),a=`You are an expert task analyzer that determines whether a user request requires multiple specialized roles or can be handled by a single role.

IMPORTANT PRINCIPLES:
1. Analyze requests carefully to identify distinct tasks that require different specialized skills
2. Simple requests like "continue", "more", "help me", or single actions should be single-role
3. Multi-role is for requests that contain multiple distinct tasks with different skill requirements
4. Use ONLY role IDs from the available roles list - never make up role names

AVAILABLE ROLE IDS (use these exact IDs):
- general_chat: General conversation and Q&A
- logic_reasoning: Mathematical reasoning and problem-solving
- writing: Articles, blog posts, marketing copy, creative content
- coding_frontend: HTML, CSS, JavaScript, React, Vue, Angular, UI/UX
- coding_backend: Server-side logic, APIs, databases, Python, Node.js, Java
- research_synthesis: Information retrieval, data analysis, research reports
- summarization_briefing: Condensing texts into summaries
- translation_localization: Language translation and cultural adaptation
- data_extraction_structuring: Extracting and organizing information from text
- brainstorming_ideation: Creative idea generation and concept development
- education_tutoring: Explaining concepts and educational assistance
- image_generation: Creating images from descriptions
- audio_transcription: Converting speech to text

Examples of TRUE multi-role requests (multiple distinct tasks):
- "Brainstorm a game idea and then write the backend code for it" (brainstorming_ideation → coding_backend)
- "Brainstorm an idea for a simple snake game and give me the full script" (brainstorming_ideation → coding_backend)
- "Research the history of AI and write a blog post about it" (research_synthesis → writing)
- "Create a React component and write documentation for it" (coding_frontend → writing)
- "Build a REST API and create a frontend interface for it" (coding_backend → coding_frontend)

Examples of single-role requests (even if complex):
- "Write a Python API for user authentication" (just coding_backend)
- "Create a responsive navbar in React" (just coding_frontend)
- "Build a full-stack web app" (just coding_backend - this is one cohesive task)
- "Create a blog post about climate change" (just writing)
- "continue" (just general_chat)
- "more" (just general_chat)
- "explain this code" (just general_chat)
- "help me debug this JavaScript error" (just coding_frontend)

CODING ROLE GUIDELINES:
- coding_frontend: HTML, CSS, JavaScript, React, Vue, Angular, UI components, styling, client-side logic
- coding_backend: Python, Node.js, Java, APIs, databases, server logic, data processing, algorithms

Analyze the request thoughtfully - choose multi-role when the request contains multiple distinct tasks that require different specialized skills.

Respond in JSON format ONLY with no additional text:
{
  "isMultiRole": true/false,
  "roles": [
    {"roleId": "role_id1", "confidence": 0.9, "executionOrder": 1},
    {"roleId": "role_id2", "confidence": 0.8, "executionOrder": 2}
  ],
  "reasoning": "Detailed explanation of your analysis"
}`,i=`Available Roles:
${n}

Recent Conversation:
${r}

User Request: "${e.substring(0,2e3)}"

Analyze this request: Does it require multiple distinct specialized roles working together, or can it be handled by a single role?`;try{let e,r=await en("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`,Connection:"keep-alive","User-Agent":"RoKey/1.0 (Multi-Role-Detection)",Origin:"https://rokey.app"},body:JSON.stringify({model:"gemini-2.0-flash-lite",messages:[{role:"system",content:a},{role:"user",content:i}],temperature:.3,max_tokens:800,response_format:{type:"json_object"}})},2,et.CLASSIFICATION);if(!r.ok)return{isMultiRole:!1,roles:[],reasoning:"API error during multi-role detection"};let n=await r.json();try{let s=n.choices?.[0]?.message?.content;if(!s)throw Error("Empty response from multi-role detection");if(e=JSON.parse(s),"boolean"!=typeof e.isMultiRole||!Array.isArray(e.roles))throw Error("Invalid response structure from multi-role detection");return e.isMultiRole&&e.roles.length>0&&(e.roles=e.roles.map(e=>{let s=t.find(t=>t.id===e.roleId||t.id.toLowerCase()===e.roleId.toLowerCase()||t.name&&t.name.toLowerCase()===e.roleId.toLowerCase());return s?{...e,roleId:s.id,confidence:"number"==typeof e.confidence?e.confidence:.8}:null}).filter(Boolean),0===e.roles.length&&(e.isMultiRole=!1,e.reasoning="No valid roles matched after filtering")),e}catch(e){return{isMultiRole:!1,roles:[],reasoning:"Error parsing multi-role detection result"}}}catch(e){return{isMultiRole:!1,roles:[],reasoning:"Error during multi-role detection"}}}let R=new Map;async function D(e,t,s,r){let n=await (0,l.x)(),{error:a}=await n.from("synthesis_storage").upsert({synthesis_id:e,conversation_id:t,complete_synthesis:s,chunks:r,total_chunks:r.length,created_at:new Date().toISOString(),last_access_time:new Date().toISOString()});if(a)throw Error(`Failed to store synthesis: ${a.message}`)}async function E(e){let t=await (0,l.x)(),{data:s,error:r}=await t.from("synthesis_storage").select("*").eq("synthesis_id",e).single();return r||!s?null:(await t.from("synthesis_storage").update({last_access_time:new Date().toISOString()}).eq("synthesis_id",e),{conversationId:s.conversation_id,completeSynthesis:s.complete_synthesis,chunks:s.chunks,totalChunks:s.total_chunks,createdAt:new Date(s.created_at).getTime(),lastAccessTime:new Date(s.last_access_time).getTime()})}async function $(e){let t=await (0,l.x)(),{data:s,error:r}=await t.from("synthesis_storage").select("synthesis_id").eq("conversation_id",e).order("created_at",{ascending:!1}).limit(1).single();return r||!s?null:s.synthesis_id}let O=0,N={MAX_CHARS:7e4,MIN_CHUNK_SIZE:1e3,CODE_BLOCK_MAX_CHARS:7e4};function q(e){let t=function(e){let t=e.find(e=>"user"===e.role);if(t&&"string"==typeof t.content){let e=g().createHash("md5").update(t.content.substring(0,200)).digest("hex").substring(0,12);return`conv_${e}`}return`conv_${Date.now()}`}(e);for(let[e,s]of R.entries())if(e.startsWith(t.substring(0,15))&&Date.now()-s.lastActivity<18e5)return e;return t}async function L(){let e=await (0,l.x)(),t=new Date(Date.now()-18e5).toISOString(),{data:s,error:r}=await e.from("synthesis_storage").select("synthesis_id").lt("last_access_time",t);if(r||!s||0===s.length)return;let{error:n}=await e.from("synthesis_storage").delete().lt("last_access_time",t);n||O++}async function U(e,t){let s=function(e){let t=[],s="",r=0,n=!1,a=e.split("\n\n");for(let e=0;e<a.length;e++){let i=a[e],o=i.length,l=i.match(/^```(\w+)?/),c=!!i.match(/```\s*$/);l&&!n?(n=!0,l[1]):c&&n&&(n=!1);let d=r+o+2>N.MAX_CHARS,u=r+o+2>N.CODE_BLOCK_MAX_CHARS;if(n&&d&&!u&&s.length>N.MIN_CHUNK_SIZE)s+=(s?"\n\n":"")+i,r+=o+2*!!s;else if(n&&u)t.push(s.trim()),s=i,r=o;else if(d&&s.length>N.MIN_CHUNK_SIZE&&!n)t.push(s.trim()),s=i,r=o;else if(d&&s.length<=N.MIN_CHUNK_SIZE&&!n)for(let e of i.split(/(?<=[.!?])\s+/))r+e.length+1>N.MAX_CHARS&&s.length>N.MIN_CHUNK_SIZE?(t.push(s.trim()),s=e,r=e.length):(s+=(s?" ":"")+e,r+=e.length+ +!!s);else s+=(s?"\n\n":"")+i,r+=o+2*!!s}s.trim()&&t.push(s.trim());let i=t.filter(e=>e.trim().length>0),o=i.map((e,t)=>{let s=e,r=s.trim().startsWith("```"),n=s.includes("```");if(!r&&n&&t>0){let e=i[t-1];if((e.match(/```/g)||[]).length%2==1){let t=e.match(/```(\w+)?/);s="```"+(t&&t[1]||"")+"\n"+s}}return s});return o.forEach((e,t)=>{(e.match(/```/g)||[]).length}),o}(t),r=`synthesis_${e}_${Date.now()}`;try{return await D(r,e,t,s),O++,await E(r),r}catch(e){throw e}}async function M(e,t){let s=await E(e);if(!s)return{chunk:null,isComplete:!0,progress:"not_found",totalChunks:0,synthesisId:e};if(t>=s.totalChunks)return{chunk:null,isComplete:!0,progress:"complete",totalChunks:s.totalChunks,synthesisId:e};let r=s.chunks[t],n=t+1>=s.totalChunks,a=`${t+1}/${s.totalChunks}`;return n&&setTimeout(async()=>{let t=await (0,l.x)();await t.from("synthesis_storage").delete().eq("synthesis_id",e),O++},3e4),{chunk:r,isComplete:n,progress:a,totalChunks:s.totalChunks,synthesisId:e}}async function P(e){try{let t=await $(e);if(t)return t;return null}catch(e){return null}}async function j(e){try{let t=await $(e);if(t)return{synthesisId:t,isComplete:!1};return null}catch(e){return null}}let K=new Map,z=new Map;function F(e){return g().createHash("md5").update(e.toLowerCase().trim()).digest("hex")}function J(e,t,s){let r=e.map(e=>`${e.role}:${"string"==typeof e.content?e.content:JSON.stringify(e.content)}`).join("|"),n=`${t}|${r}|${s||0}`;return g().createHash("md5").update(n).digest("hex")}function B(e,t,s,r,n,a){if(s&&r&&n&&t.custom_api_config_id){let e=`${t.custom_api_config_id}:${t.messages?.[t.messages.length-1]?.content?.substring(0,100)||"default"}`;setImmediate(()=>{z.set(e,{provider:s,model:r,apiKey:n,timestamp:Date.now()})})}let i={};if(a?.roleUsed&&(i["X-RoKey-Role-Used"]=a.roleUsed),a?.routingStrategy&&(i["X-RoKey-Routing-Strategy"]=a.routingStrategy),a?.complexityLevel&&(i["X-RoKey-Complexity-Level"]=a.complexityLevel.toString()),s&&(i["X-RoKey-API-Key-Provider"]=s),a?.processingTime&&(i["X-RoKey-Processing-Time"]=`${a.processingTime}ms`),t.stream&&e.response){let t={...Object.fromEntries(e.response.headers.entries()),...i};return new Response(e.response.body,{status:e.response.status,headers:t})}if(t.stream||void 0===e.responseData)throw Error("Invalid provider result: no response data available");{let t={...e.responseHeaders||{},...i};return o.NextResponse.json(e.responseData,{status:e.status||200,headers:t})}}async function Y(e,t,s){let r=`${e}_${t}`,n=I.get(r);if(n&&Date.now()-n.timestamp<9e5)return{customRoles:n.customRoles,roleAssignments:n.roleAssignments,apiKeys:n.apiKeys};try{let[n,a,i]=await Promise.allSettled([s.from("user_custom_roles").select("role_id, name, description").eq("user_id",e),s.from("api_key_role_assignments").select("role_name, api_key_id").eq("custom_api_config_id",t),s.from("api_keys").select("*").eq("custom_api_config_id",t).eq("status","active")]),o="fulfilled"===n.status&&n.value.data||[],l="fulfilled"===a.status&&a.value.data||[],c="fulfilled"===i.status&&i.value.data||[];return I.set(r,{customRoles:o,roleAssignments:l,apiKeys:c,timestamp:Date.now()}),{customRoles:o,roleAssignments:l,apiKeys:c}}catch(e){return null}}async function H(e){try{let t=h.trainingDataCache.get(e);if(t)return{trainingData:t.data,trainingJobId:t.jobId};let s=await (0,l.x)(),{data:r,error:n}=await s.from("training_jobs").select("id, training_data, created_at").eq("custom_api_config_id",e).eq("status","completed").order("created_at",{ascending:!1}).limit(1).single();if(n&&"PGRST116"!==n.code||!r?.training_data)return null;return h.trainingDataCache.set(e,r.training_data,r.id),{trainingData:r.training_data,trainingJobId:r.id}}catch(e){return null}}async function X(e,t){if(!t||!t.processed_prompts)return e;let{processed_prompts:s}=t,r="";if(s.system_instructions?.trim()&&(r+=`${s.system_instructions.trim()}

`),s.general_instructions?.trim()&&(r+=`${s.general_instructions.trim()}

`),s.behavior_guidelines?.trim()&&(r+=`## Behavior Guidelines:
${s.behavior_guidelines.trim()}

`),s.examples&&s.examples.length>0&&(r+=`## Training Examples:
`,s.examples.forEach((e,t)=>{r+=`Example ${t+1}:
User: ${e.input}
Assistant: ${e.output}

`})),r.trim()){r+=`---
IMPORTANT INSTRUCTIONS:
1. Follow the training examples and behavior guidelines above
2. Maintain the personality and behavior patterns shown in the examples
3. Apply the system instructions and general instructions consistently

Now respond to the user following these patterns and guidelines.`;let t=[...e],s=t.findIndex(e=>"system"===e.role);if(s>=0){let e=t[s].content,n="string"==typeof e?e:Array.isArray(e)&&e.find(e=>"text"===e.type)?.text||"";t[s].content=r+"\n\n"+n}else t.unshift({role:"system",content:r});return t}return e}setInterval(function(){let e=Date.now();for(let[t,s]of(h.trainingDataCache.cleanup(),v.entries()))e-s.timestamp>36e5&&v.delete(t)},6e5);let G=u.z.object({custom_api_config_id:u.z.string().uuid({message:"custom_api_config_id must be a valid UUID."}),role:u.z.string().optional(),messages:u.z.array(u.z.object({role:u.z.enum(["user","assistant","system"]),content:u.z.any()})).min(1,{message:"Messages array cannot be empty and must contain at least one message."}),model:u.z.string().optional(),stream:u.z.boolean().optional().default(!1),temperature:u.z.number().optional(),max_tokens:u.z.number().int().positive().optional(),specific_api_key_id:u.z.string().uuid().optional()}).catchall(u.z.any());async function V(e,t){if(!e||!t)return null;let s={model:"gemini-2.0-flash-lite",messages:[{role:"system",content:"You are a prompt complexity classification expert. Your task is to analyze the user's prompt and classify its complexity on a scale of 1 to 5, where 1 is Very Simple, 2 is Simple, 3 is Moderate, 4 is Complex, and 5 is Very Complex. Output ONLY the integer number corresponding to the complexity level. Do not provide any explanation or any other text. Just the number."},{role:"user",content:`User's original prompt: "${e}"

Classify the complexity of this prompt (1-5):`}],temperature:.1,max_tokens:10,top_p:1};try{let e=await fetch("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify(s)});if(!e.ok)return await e.json().catch(()=>({message:e.statusText})),null;let r=await e.json(),n=r.choices?.[0]?.message?.content?.trim();if(n){let e=parseInt(n,10);if(!isNaN(e)&&e>=1&&e<=5)return e}}catch(e){}return null}async function Z(e,t,s,r,n){let a=process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;if(!a)return{targetApiKeyData:null,roleUsedState:"missing_classification_api_key"};if(!e.user_id)return{targetApiKeyData:null,roleUsedState:"missing_user_id"};let i="";if(s.messages&&s.messages.length>0){let e=s.messages[s.messages.length-1];if("user"===e.role&&"string"==typeof e.content)i=e.content;else if("user"===e.role&&Array.isArray(e.content)){let t=e.content.find(e=>"text"===e.type);t&&"string"==typeof t.text&&(i=t.text)}}if(!i&&s.prompt&&(i=s.prompt),!i)return{targetApiKeyData:null,roleUsedState:"no_prompt_for_classification"};let o=q(s.messages),l=R.get(o);if(l){let n=Date.now()-l.lastActivity,a=function(e,t){let s=e.toLowerCase().trim();for(let[e,r]of Object.entries({coding_frontend:["write code","write python","write javascript","write java","write c++","code this","program this","create a script","build an app","make a website","write html","write css","write react","frontend code","client code","create component","build interface","ui code","web development"],coding_backend:["write api","create server","database code","backend code","server code","write sql","create endpoint","api development","microservice","write node","express code","django code","flask code"],data_analysis:["analyze this data","create a chart","make a graph","data analysis","statistical analysis","create visualization","data science","machine learning","pandas code","numpy analysis","plot this","visualize data"],writing:["write an article","write a blog","create content","write an essay","write documentation","create copy","marketing content","blog post","article about","essay on","content for"],translation:["translate this","translate to","convert to language","in spanish","in french","in german","in chinese","translate into"],summarization:["summarize this","create summary","tldr","brief overview","key points","main ideas","executive summary"]}))if(e!==t){for(let t of r)if(s.includes(t))return{isTransition:!0,newTaskRole:e,reasoning:`explicit_task_transition: "${t}" -> ${e}`}}let r=["now","instead","switch to","change to","help me","can you","please","i want you to","i need you to"].some(e=>s.includes(e)),n=["create","build","make","develop","design","implement","generate","produce","construct","craft","compose"].some(e=>s.includes(e));if(r&&n)return{isTransition:!0,newTaskRole:null,reasoning:"transition_keyword_with_action_verb"};for(let e of["now write","now create","now build","now make","now help","instead write","instead create","can you write","can you create","help me write","help me create","help me build"])if(s.includes(e))return{isTransition:!0,newTaskRole:null,reasoning:`strong_transition_phrase: "${e}"`};return{isTransition:!1,newTaskRole:null,reasoning:"no_transition_detected"}}(i,l.lastClassifiedRole);if(a.isTransition){if(a.newTaskRole){S(e.user_id,a.newTaskRole),R.set(o,{lastClassifiedRole:a.newTaskRole,messageCount:s.messages.length,lastActivity:Date.now(),confidence:.9,conversationId:o});let n=await Y(e.user_id,t,r);if(n){let e=n.roleAssignments.find(e=>e.role_name===a.newTaskRole);if(e&&e.api_key_id){let t=n.apiKeys.find(t=>t.id===e.api_key_id);if(t)return{targetApiKeyData:t,roleUsedState:`context_transition_${l.lastClassifiedRole}_to_${a.newTaskRole}`}}}}}else{let a=function(e,t,s,r){let n=e.toLowerCase().trim(),a=["continue","continue please","keep going","go on","more","more please","finish","complete","what next","then what","and then"],i=s.slice().reverse().find(e=>"assistant"===e.role),o=i&&"string"==typeof i.content&&(i.content.includes("[SYNTHESIS CONTINUES")||i.content.includes("**[SYNTHESIS CONTINUES")||i.content.includes("The response will continue")||i.content.length>1500);if(a.includes(n)&&r<6e5&&o)return{isContinuation:!0,confidence:.98,reasoning:"universal_synthesis_continuation"};if(a.includes(n)&&r<12e4)return{isContinuation:!0,confidence:.85,reasoning:"universal_short_continuation"};let l={StoryTeller:{strong:["continue","what happens next","keep going","more story","then what"],medium:["be creative","more","and then","what about","tell me more"],weak:["go on","next","more please","continue please"]},coding_frontend:{strong:["fix this","debug this","improve this code","add feature"],medium:["make it better","optimize","refactor","enhance"],weak:["change","update","modify"]},coding_backend:{strong:["fix this","debug this","improve this code","add feature"],medium:["make it better","optimize","refactor","enhance"],weak:["change","update","modify"]},writing:{strong:["revise this","edit this","improve this","rewrite this"],medium:["make it better","enhance","polish"],weak:["change","update","fix"]},data_analysis:{strong:["analyze more","deeper analysis","what else","more insights"],medium:["explain further","elaborate","more details"],weak:["continue","more"]}}[t];if(!l)return{isContinuation:!1,confidence:0,reasoning:"no_patterns_for_role"};for(let e of l.strong)if(n.includes(e))return{isContinuation:!0,confidence:.95,reasoning:`strong_pattern_match: "${e}"`};for(let e of l.medium)if(n.includes(e))return{isContinuation:!0,confidence:.8,reasoning:`medium_pattern_match: "${e}"`};if(r<12e4){for(let e of l.weak)if(n.includes(e))return{isContinuation:!0,confidence:.6,reasoning:`weak_pattern_match: "${e}" (recent)`}}return n.length<20&&r<3e5&&["yes","no","ok","sure","please","thanks","more","again"].some(e=>n.includes(e))?{isContinuation:!0,confidence:.7,reasoning:"short_continuation_prompt"}:r<6e4?{isContinuation:!0,confidence:.65,reasoning:"very_recent_message"}:r<3e5?{isContinuation:!0,confidence:.4,reasoning:"recent_message"}:{isContinuation:!1,confidence:0,reasoning:"no_continuation_detected"}}(i,l.lastClassifiedRole,s.messages,n);if(a.isContinuation&&a.confidence>.6){l.lastActivity=Date.now(),l.messageCount++,l.confidence=a.confidence;let s=l.lastClassifiedRole;S(e.user_id,s);let n=await Y(e.user_id,t,r);if(n){let e=n.roleAssignments.find(e=>e.role_name===s);if(e&&e.api_key_id){let t=n.apiKeys.find(t=>t.id===e.api_key_id);if(t)return{targetApiKeyData:t,roleUsedState:`contextual_continuation_${s}_confidence_${Math.round(100*a.confidence)}`}}}}}}let c=F(i),u=`${t}_${c}`;e.user_id;let m=v.get(u);if(m&&Date.now()-m.timestamp<36e5){let s=await Y(e.user_id,t,r);if(s)if("general_chat"===m.roleId){let e=s.apiKeys.find(e=>!0===e.is_default_general_chat_model);if(e)return{targetApiKeyData:e,roleUsedState:p.intelligentRoleRouting(m.roleId)}}else{let e=s.roleAssignments.find(e=>e.role_name===m.roleId);if(e&&e.api_key_id){let t=s.apiKeys.find(t=>t.id===e.api_key_id);if(t)return{targetApiKeyData:t,roleUsedState:p.intelligentRoleRouting(m.roleId||"general_chat")}}}}let[g,h]=await Promise.all([Y(e.user_id,t,r),Promise.resolve().then(()=>d.p2.map(e=>({id:e.id,name:e.name,description:e.description||""})))]);if(!g)return{targetApiKeyData:null,roleUsedState:"ecosystem_load_failed"};let f=[...h,...g.customRoles.map(e=>({id:e.role_id,name:e.name,description:e.description||""}))],_=function(e,t){let s=function(e,t=5){let s=x.get(e);return s?Object.entries(s).filter(([e,t])=>Date.now()-t.lastUsed<6048e5).sort(([e,t],[s,r])=>r.count-t.count).slice(0,t).map(([e,t])=>e):[]}(t,8);return 0===s.length?e:[...e.filter(e=>s.includes(e.id)),...e.filter(e=>!s.includes(e.id))]}(f,e.user_id),y=g.roleAssignments;if(0===f.length)return{targetApiKeyData:null,roleUsedState:"no_roles_available"};s.messages&&s.messages.slice(-3).map(e=>`${e.role}: ${"string"==typeof e.content?e.content.substring(0,200):Array.isArray(e.content)?e.content.find(e=>"text"===e.type)?.text?.substring(0,200)||"[non-text]":"[unknown]"}`).join("\n"),_.map(e=>`- ${e.id}: ${e.name}`).join("\n"),i.substring(0,1e3);let w=null;try{let o=await C(i,_,a,t,s.messages||[]);if(o){if("isMultiRole"in o&&o.isMultiRole){try{let{data:a,error:l}=await r.from("orchestration_executions").insert({user_id:e.user_id,custom_api_config_id:t,original_prompt:i,total_steps:o.roles.length,status:"pending"}).select().single();if(l);else if(a)throw setImmediate(async()=>{try{await e_(n,s,o,t,g.apiKeys,g.roleAssignments,i,a.id,r)}catch(e){}}),Error("ORCHESTRATION_STARTED:"+a.id)}catch(e){if(e.message&&e.message.startsWith("ORCHESTRATION_STARTED:")){let t=e.message.split(":")[1];return{targetApiKeyData:null,roleUsedState:`orchestration_started_${t}`}}}o.roles.length>0&&(w=o.roles[0].roleId)}else"roleId"in o&&(w=o.roleId);w&&v.set(u,{roleId:w,timestamp:Date.now()});let a=`user_${e.user_id}_general_pattern`;if("general_chat"===w){let e=v.get(a)||{consecutiveGeneralChat:0,timestamp:Date.now()};v.set(a,{consecutiveGeneralChat:(e.consecutiveGeneralChat||0)+1,timestamp:Date.now()})}else v.set(a,{consecutiveGeneralChat:0,timestamp:Date.now()})}else w="general_chat"}catch(e){w="general_chat"}if(w){if(S(e.user_id,w),"general_chat"===w){let e=g.apiKeys.find(e=>!0===e.is_default_general_chat_model);if(e)return{targetApiKeyData:e,roleUsedState:p.intelligentRoleRouting(w)}}let t=y.find(e=>e.role_name===w);if(t&&t.api_key_id){let e=g.apiKeys.find(e=>e.id===t.api_key_id);if(e)return{targetApiKeyData:e,roleUsedState:p.intelligentRoleRouting(w)}}}return{targetApiKeyData:null,roleUsedState:"classification_no_key_found"}}async function W(e,t,s){let r=e?.ordered_api_key_ids;if(!Array.isArray(r)||0===r.length)return{targetApiKeyData:null,roleUsedState:"strict_fallback_no_keys_defined"};let n=r.map(async(e,r)=>{try{let{data:n,error:a}=await s.from("api_keys").select("*").eq("id",e).eq("custom_api_config_id",t).single();return{index:r,keyId:e,apiKey:n,error:a,success:!a&&n&&"active"===n.status}}catch(t){return{index:r,keyId:e,apiKey:null,error:t,success:!1}}}),a=await Promise.allSettled(n);for(let e=0;e<a.length;e++){let t=a[e];if("fulfilled"===t.status&&t.value.success){let{apiKey:s,keyId:r}=t.value;return{targetApiKeyData:s,roleUsedState:p.fallbackRouting(e)}}if("fulfilled"===t.status){let{keyId:e,apiKey:s,error:r}=t.value;r&&r.code}}return{targetApiKeyData:null,roleUsedState:"strict_fallback_no_active_key_in_list"}}async function Q(e,t,s){let r=process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;if(!r)return{targetApiKeyData:null,roleUsedState:"complexity_rr_missing_classifier_key"};let n="";if(t.messages&&t.messages.length>0){let e=t.messages[t.messages.length-1];if("user"===e.role&&"string"==typeof e.content)n=e.content;else if("user"===e.role&&Array.isArray(e.content)){let t=e.content.find(e=>"text"===e.type);t&&"string"==typeof t.text&&(n=t.text)}}if(!n&&t.prompt&&(n=t.prompt),!n)return{targetApiKeyData:null,roleUsedState:"complexity_rr_no_prompt"};let a=await V(n,r);if(null===a||a<1||a>5)return{targetApiKeyData:null,roleUsedState:"complexity_rr_invalid_classification"};let{data:i,error:o}=await s.from("config_api_key_complexity_assignments").select(`
      api_key_id,
      complexity_level,
      api_keys!inner (
        id,
        status,
        provider,
        predefined_model_id,
        is_default_general_chat_model,
        encrypted_api_key,
        label
      )
    `).eq("custom_api_config_id",e.id).eq("complexity_level",a);if(o)return{targetApiKeyData:null,roleUsedState:"complexity_rr_db_error",classifiedComplexityLevel:a,classifiedComplexityLLM:"gemini-2.0-flash-lite"};let l=i?.filter(e=>"active"===e.api_keys.status)?.map(e=>e.api_keys)||[];if(l.length>0){let t=e.routing_strategy_params||{},r=`_complexity_${a}_rr_idx`,n="number"==typeof t[r]?t[r]:0,i=[...l].sort((e,t)=>e.id.localeCompare(t.id)),o=i[n%i.length],c=(n+1)%i.length;return t[r]=c,setImmediate(async()=>{let{error:r}=await s.from("custom_api_configs").update({routing_strategy_params:t}).eq("id",e.id)}),{targetApiKeyData:o,roleUsedState:`complexity_rr_level_${a}_key_found`,classifiedComplexityLevel:a,classifiedComplexityLLM:"gemini-2.0-flash-lite"}}let c=[];for(let e=1;e<=4;e++)a-e>=1&&c.push(a-e),a+e<=5&&c.push(a+e);for(let t of c){let{data:r,error:n}=await s.from("config_api_key_complexity_assignments").select(`
        api_key_id,
        complexity_level,
        api_keys!inner (
          id,
          status,
          provider,
          predefined_model_id,
          is_default_general_chat_model,
          encrypted_api_key,
          label
        )
      `).eq("custom_api_config_id",e.id).eq("complexity_level",t);if(n)continue;let i=r?.filter(e=>"active"===e.api_keys.status)?.map(e=>e.api_keys)||[];if(i.length>0){let r=e.routing_strategy_params||{},n=`_complexity_${t}_rr_idx`,o="number"==typeof r[n]?r[n]:0,l=[...i].sort((e,t)=>e.id.localeCompare(t.id)),c=l[o%l.length],d=(o+1)%l.length;return r[n]=d,setImmediate(async()=>{let{error:t}=await s.from("custom_api_configs").update({routing_strategy_params:r}).eq("id",e.id)}),{targetApiKeyData:c,roleUsedState:`complexity_rr_level_${a}_proximal_${t}_key_found`,classifiedComplexityLevel:a,classifiedComplexityLLM:"gemini-2.0-flash-lite"}}}return{targetApiKeyData:null,roleUsedState:"complexity_rr_no_keys_found",classifiedComplexityLevel:a,classifiedComplexityLLM:"gemini-2.0-flash-lite"}}async function ee(e,t,s){let[r,n]=await Promise.allSettled([s?e.from("api_key_role_assignments").select("api_key_id").eq("custom_api_config_id",t).eq("role_name",s).single():Promise.resolve({data:null,error:null}),e.from("api_keys").select("*").eq("custom_api_config_id",t).eq("is_default_general_chat_model",!0).single()]);if(s&&"fulfilled"===r.status&&r.value.data){let t=r.value.data,{data:s,error:n}=await e.from("api_keys").select("*").eq("id",t.api_key_id).single();if(n);else if(s&&"active"===s.status)return s}else s&&r.status;if("fulfilled"===n.status&&n.value.data){let e=n.value.data;if("active"===e.status)return e}else n.status;return null}let et={CLASSIFICATION:5e3,LLM_REQUEST:8e3,SOCKET:1500,GOOGLE_CLASSIFICATION:7e3},es=new(_()).Agent({keepAlive:!0,keepAliveMsecs:12e4,maxSockets:200,maxFreeSockets:50,timeout:et.SOCKET,scheduling:"lifo",maxTotalSockets:500}),er=new(w()).Agent({keepAlive:!0,keepAliveMsecs:12e4,maxSockets:200,maxFreeSockets:50,timeout:et.SOCKET,scheduling:"lifo",maxTotalSockets:500});async function en(e,t,r=3,n){let a,i=n||(e.includes("generativelanguage.googleapis.com")?et.GOOGLE_CLASSIFICATION:et.LLM_REQUEST);for(let n=1;n<=r;n++)try{return await function(e,t,r=et.LLM_REQUEST){return new Promise((n,a)=>{let i=new k.URL(e),o="https:"===i.protocol,l=o?_():w(),c={hostname:i.hostname,port:i.port||(o?443:80),path:i.pathname+i.search,method:t.method||"GET",headers:{...t.headers,Connection:"keep-alive","Keep-Alive":`timeout=${Math.floor(r/1e3)}, max=100`},timeout:r,agent:o?es:er},d=l.request(c,e=>{let t=e,r=e.headers["content-encoding"];if("gzip"===r){let r=s(74075);t=e.pipe(r.createGunzip())}else if("deflate"===r){let r=s(74075);t=e.pipe(r.createInflate())}else if("br"===r){let r=s(74075);t=e.pipe(r.createBrotliDecompress())}if(e.headers["content-type"]?.includes("text/event-stream")||e.headers["content-type"]?.includes("text/plain")||e.headers["content-type"]?.includes("application/x-ndjson"))n(new Response(new ReadableStream({start(e){t.on("data",t=>{let s=t.toString("utf8");e.enqueue(new TextEncoder().encode(s))}),t.on("end",()=>{e.close()}),t.on("error",t=>{e.error(t)})}}),{status:e.statusCode,statusText:e.statusMessage||"",headers:new Headers(e.headers)}));else{let s=[];t.on("data",e=>{s.push(Buffer.isBuffer(e)?e:Buffer.from(e))}),t.on("end",()=>{n(new Response(Buffer.concat(s).toString("utf8"),{status:e.statusCode,statusText:e.statusMessage||"",headers:new Headers(e.headers)}))})}});d.on("error",e=>{a(Error(`Network request failed: ${e.message}`))}),d.on("timeout",()=>{d.destroy(),a(Error(`Request timeout after ${r}ms`))}),t.body&&d.write(t.body),d.end()})}(e,t,i)}catch(t){if(a=t,n===r)throw t;let e=t.message.includes("timeout")?50:100*n;await new Promise(t=>setTimeout(t,e))}throw a}async function ea(e,t,r,n){let a,i,o,l,c,d=null,u=new Date;if(!n.stream&&n.messages&&t){let e=J(n.messages,t,n.temperature),s=K.get(e);if(s&&Date.now()-s.timestamp<12e4)return{success:!0,response:void 0,responseData:s.response,responseHeaders:new Headers({"x-rokey-cache":"hit"}),status:200,error:null,llmRequestTimestamp:new Date,llmResponseTimestamp:new Date}}let p={method:"POST",headers:{"Content-Type":"application/json",Connection:"keep-alive","Keep-Alive":"timeout=30, max=100","User-Agent":"RoKey/1.0 (Performance-Optimized)",Accept:"application/json","Cache-Control":"no-cache"}};try{let m=function(e,t){if(!e)return"";let s=t.toLowerCase(),r=`${s}/`;return e.toLowerCase().startsWith(r)?e.substring(r.length):e}(t,e||""),g=e?.toLowerCase()==="openrouter"?t:m;if(!g)throw{message:`Effective model ID is missing for provider ${e} (DB Model: ${t})`,status:500,internal:!0};if(d=new Date,e?.toLowerCase()==="openai"){let{custom_api_config_id:e,role:t,...s}=n,d={...s,model:g,messages:n.messages,stream:n.stream};Object.keys(d).forEach(e=>void 0===d[e]&&delete d[e]);let m={...p};m.headers={...p.headers,Authorization:`Bearer ${r}`,Origin:"https://rokey.app",Connection:"keep-alive","Keep-Alive":"timeout=30, max=100","Cache-Control":"no-cache",Priority:"u=1, i"},m.body=JSON.stringify(d);let h=await en("https://api.openai.com/v1/chat/completions",m);if(u=new Date,a=h.status,c=h.headers,!h.ok){let e=await h.json().catch(()=>({error:{message:h.statusText}}));throw i={message:`OpenAI Error: ${e?.error?.message||h.statusText}`,status:h.status,provider_error:e}}if(n.stream){if(!h.body)throw i={message:"OpenAI stream body null",status:500};o=h,l={note:"streamed"}}else l=await h.json()}else if(e?.toLowerCase()==="openrouter"){let{custom_api_config_id:e,role:t,...d}=n,m={...d,model:g,messages:n.messages,stream:n.stream,usage:{include:!0}};Object.keys(m).forEach(e=>void 0===m[e]&&delete m[e]);let h={...p};h.headers={...p.headers,Authorization:`Bearer ${r}`,"HTTP-Referer":"https://rokey.app","X-Title":"RoKey",Origin:"https://rokey.app",Connection:"keep-alive","Keep-Alive":"timeout=30, max=100","Cache-Control":"no-cache",Priority:"u=1, i"},h.body=JSON.stringify(m);let f=await en("https://openrouter.ai/api/v1/chat/completions",h);if(u=new Date,a=f.status,c=f.headers,!f.ok){let e=await f.json().catch(()=>({error:{message:f.statusText}}));throw i={message:`OpenRouter Error: ${e?.error?.message||f.statusText}`,status:f.status,provider_error:e}}if(n.stream){if(!f.body)throw i={message:"OpenRouter stream body null",status:500};let{createFirstTokenTrackingStream:e}=await s.e(9704).then(s.bind(s,99704)),t=e(f.body,"OpenRouter",g);o=new Response(t,{status:f.status,statusText:f.statusText,headers:f.headers}),l={note:"streamed"}}else l=await f.json()}else if(e?.toLowerCase()==="google"){let e=g.replace(/^models\//,""),t=n.messages.map(e=>{if("string"==typeof e.content)return{role:e.role,content:e.content};if(Array.isArray(e.content)){let t=e.content.map(e=>"text"===e.type&&"string"==typeof e.text?{type:"text",text:e.text}:"image_url"===e.type&&e.image_url?.url?{type:"image_url",image_url:{url:e.image_url.url}}:null).filter(Boolean);return{role:e.role,content:t}}return{role:e.role,content:"[RoKey: Invalid content structure for Google]"}});if(0===t.length)throw i={message:"No processable message content found for Google provider after filtering.",status:400};let d={model:e,messages:t,stream:n.stream};void 0!==n.temperature&&(d.temperature=n.temperature),void 0!==n.max_tokens&&(d.max_tokens=n.max_tokens),void 0!==n.top_p&&(d.top_p=n.top_p);let m={...p};m.headers={...p.headers,Authorization:`Bearer ${r}`,Connection:"keep-alive","Keep-Alive":"timeout=30, max=100","User-Agent":"RoKey/1.0 (Performance-Optimized)",Origin:"https://rokey.app","Cache-Control":"no-cache",Priority:"u=1, i"},m.body=JSON.stringify(d);let h=await en("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",m);if(u=new Date,a=h.status,c=h.headers,!h.ok){let e=await h.json().catch(()=>({error:{message:h.statusText}})),t=e?.error?.message||h.statusText;throw Array.isArray(e)&&e[0]?.error?.message&&(t=e[0].error.message),i={message:`Google Error: ${t}`,status:h.status,provider_error:e}}if(n.stream){if(!h.body)throw i={message:"Google stream body null",status:500};l={note:"streamed"};let{createFirstTokenTrackingStream:t}=await s.e(9704).then(s.bind(s,99704)),r=t(h.body,"Google",e);o=new Response(r,{status:h.status,statusText:h.statusText,headers:h.headers})}else l=await h.json()}else if(e?.toLowerCase()==="anthropic"){let e,t=n.max_tokens||2048,d=n.messages.filter(t=>"system"!==t.role||("string"==typeof t.content&&(e=t.content),!1)).map(e=>({role:e.role,content:e.content}));if(0===d.length||"user"!==d[0].role)throw i={message:"Invalid messages format for Anthropic: Must contain at least one user message and start with user after system filter.",status:400};let m={model:g,messages:d,max_tokens:t,stream:n.stream};e&&(m.system=e),void 0!==n.temperature&&(m.temperature=n.temperature);let h={...p};h.headers={...p.headers,"x-api-key":r,"anthropic-version":"2023-06-01",Origin:"https://rokey.app"},h.body=JSON.stringify(m);let f=await en("https://api.anthropic.com/v1/messages",h);if(u=new Date,a=f.status,c=f.headers,!f.ok){let e=await f.json().catch(()=>({error:{message:f.statusText}}));throw i={message:`Anthropic Error: ${e?.error?.message||f.statusText} (Type: ${e?.error?.type})`,status:f.status,provider_error:e}}if(n.stream){if(!f.body)throw i={message:"Anthropic stream body null",status:500};let{createFirstTokenTrackingStream:e}=await s.e(9704).then(s.bind(s,99704)),t=e(f.body,"Anthropic",g);o=new Response(t,{status:f.status,headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive"}}),l={note:"streamed"}}else{let e=await f.json(),t=e.content?.find(e=>"text"===e.type)?.text||"";l={id:e.id||`anthropic-exPR-${Date.now()}`,object:"chat.completion",created:Math.floor(Date.now()/1e3),model:e.model||g,choices:[{index:0,message:{role:"assistant",content:t},finish_reason:e.stop_reason?.toLowerCase()||"stop"}],usage:{prompt_tokens:e.usage?.input_tokens,completion_tokens:e.usage?.output_tokens,total_tokens:(e.usage?.input_tokens||0)+(e.usage?.output_tokens||0)}}}}else if(e?.toLowerCase()==="deepseek"){let{custom_api_config_id:e,role:t,...d}=n,m={...d,model:g,messages:n.messages,stream:n.stream};Object.keys(m).forEach(e=>void 0===m[e]&&delete m[e]);let h={...p};h.headers={...p.headers,Authorization:`Bearer ${r}`,Origin:"https://rokey.app"},h.body=JSON.stringify(m);let f=await en("https://api.deepseek.com/chat/completions",h);if(u=new Date,a=f.status,c=f.headers,!f.ok){let e=await f.json().catch(()=>({error:{message:f.statusText}}));throw i={message:`DeepSeek Error: ${e?.error?.message||f.statusText} (Type: ${e?.error?.type})`,status:f.status,provider_error:e}}if(n.stream){if(!f.body)throw i={message:"DeepSeek stream body null",status:500};let{createFirstTokenTrackingStream:e}=await s.e(9704).then(s.bind(s,99704)),t=e(f.body,"DeepSeek",g);o=new Response(t,{status:f.status,statusText:f.statusText,headers:f.headers}),l={note:"streamed"}}else l=await f.json()}else if(e?.toLowerCase()==="xai"){let{custom_api_config_id:e,role:t,...d}=n,m={...d,model:g,messages:n.messages,stream:n.stream||!1};"number"==typeof n.temperature&&(m.temperature=n.temperature),"number"==typeof n.max_tokens&&(m.max_tokens=n.max_tokens),"number"==typeof n.top_p&&(m.top_p=n.top_p),"number"==typeof n.frequency_penalty&&(m.frequency_penalty=n.frequency_penalty),"number"==typeof n.presence_penalty&&(m.presence_penalty=n.presence_penalty);let h={...p};h.headers={...p.headers,Authorization:`Bearer ${r}`,Origin:"https://rokey.app"},h.body=JSON.stringify(m);let f=await en("https://api.x.ai/v1/chat/completions",h);if(u=new Date,a=f.status,c=f.headers,!f.ok){let e=await f.json().catch(()=>({error:{message:f.statusText}}));throw i={message:`XAI/Grok Error: ${e?.error?.message||f.statusText} (Type: ${e?.error?.type})`,status:f.status,provider_error:e}}if(n.stream){if(!f.body)throw i={message:"XAI/Grok stream body null",status:500};let{createFirstTokenTrackingStream:e}=await s.e(9704).then(s.bind(s,99704)),t=e(f.body,"XAI",g);o=new Response(t,{status:f.status,headers:{...Object.fromEntries(f.headers),"Content-Type":"text/event-stream"}}),l={note:"streamed"}}else l=await f.json()}else throw i={message:`Provider '${e}' is configured but not supported by RoKey proxy (executeProviderRequest).`,status:501,internal:!0};if(!n.stream&&l&&n.messages&&t){let s=J(n.messages,t,n.temperature);if(K.set(s,{response:l,timestamp:Date.now(),provider:e||"unknown",model:t}),K.size>1e3){let e=Array.from(K.entries());e.sort((e,t)=>e[1].timestamp-t[1].timestamp);let t=Math.floor(.2*e.length);for(let s=0;s<t;s++)K.delete(e[s][0])}}return{success:!0,response:o,responseData:l,responseHeaders:c,status:a,error:null,llmRequestTimestamp:d,llmResponseTimestamp:u}}catch(r){let e=i||r,t="ProviderCommsError",s="";return"AbortError"===r.name?(t="TimeoutError",s="Request timed out after 30 seconds"):r.message?.includes("fetch failed")?(t="NetworkError",s="Network connection failed - check internet connectivity"):"ENOTFOUND"===r.code?(t="DNSError",s="DNS resolution failed - check network settings"):"ECONNREFUSED"===r.code&&(t="ConnectionRefused",s="Connection refused by server"),{success:!1,status:e.status||500,error:e.provider_error||{message:`${e.message}${s?` (${s})`:""}`,type:e.internal?"RoKeyInternal":t,diagnostic:s},llmRequestTimestamp:d||new Date,llmResponseTimestamp:u||new Date,response:void 0,responseData:void 0,responseHeaders:c}}}async function ei(e,t){try{let e=await (0,l.x)(),{data:s,error:r}=await e.from("orchestration_executions").select("*").eq("id",t).single();if(r||!s)return o.NextResponse.json({error:"Execution not found"},{status:404});let{data:n,error:a}=await e.from("orchestration_steps").select("*").eq("execution_id",t).eq("status","completed").order("step_number");if(a||!n||0===n.length)return o.NextResponse.json({error:"No completed steps found"},{status:404});let i=`As an AI orchestration moderator, synthesize multiple specialist outputs into one cohesive response:

Original Request: "${s.original_prompt}"

Specialist Outputs:
${n.map(e=>`${e.step_number}. ${e.role_id}: "${e.output}"`).join("\n\n")}

Please synthesize the above analyses into a well-structured response that addresses the original request. Be sure to maintain all key insights and technical details while presenting them in a clear and organized manner.`,c=process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;if(!c)return o.NextResponse.json({error:"Server configuration error"},{status:500});let d=await en("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${c}`,"User-Agent":"RoKey/1.0 (Synthesis)",Origin:"https://rokey.app","Cache-Control":"no-cache"},body:JSON.stringify({model:"gemini-2.0-flash-001",messages:[{role:"user",content:i}],stream:!0,temperature:.3,max_tokens:8e3})});if(!d.ok)return o.NextResponse.json({error:"Synthesis API error"},{status:d.status});let u=d.body.getReader(),p=new TextDecoder,m="";try{for(;;){let{done:e,value:t}=await u.read();if(e)break;for(let e of p.decode(t,{stream:!0}).split("\n"))if(e.startsWith("data: ")&&!e.includes("[DONE]"))try{let t=e.substring(6),s=JSON.parse(t);s.choices?.[0]?.delta?.content&&(m+=s.choices[0].delta.content)}catch(e){}}}finally{u.releaseLock()}if(!m)return o.NextResponse.json({error:"Empty synthesis response"},{status:500});let g=`synthesis_${t}`,h=await U(g,m),{chunk:f,isComplete:_,progress:y}=await M(h,0);if(!f)return o.NextResponse.json({error:"Failed to chunk synthesis"},{status:500});await e.from("orchestration_executions").update({status:"completed",completed_at:new Date().toISOString()}).eq("id",t);let w=new ReadableStream({async start(e){let t=new TextEncoder,s=f.split(/(\s+)/);for(let r=0;r<s.length;r++){let n=s[r],a={id:`synthesis-${Date.now()}-${r}`,object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"gemini-2.0-flash-001",choices:[{index:0,delta:{content:n},finish_reason:null}]};e.enqueue(t.encode(`data: ${JSON.stringify(a)}

`)),r<s.length-1&&await new Promise(e=>setTimeout(e,10))}let r={id:`synthesis-${Date.now()}-final`,object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"gemini-2.0-flash-001",choices:[{index:0,delta:{},finish_reason:_?"stop":"length"}]};e.enqueue(t.encode(`data: ${JSON.stringify(r)}

`)),e.enqueue(t.encode("data: [DONE]\n\n")),e.close()}});return new Response(w,{status:200,headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","Access-Control-Allow-Origin":"*","X-Accel-Buffering":"no","X-Synthesis-Id":h,"X-Synthesis-Progress":y,"X-Synthesis-Complete":_.toString()}})}catch(e){return o.NextResponse.json({error:"Internal synthesis error",details:e.message},{status:500})}}async function eo(e,t,s){let r=e.headers.get("X-Synthesis-Chunk-Index"),n=r?parseInt(r,10):1,{chunk:a,isComplete:i,progress:o,totalChunks:l,synthesisId:c}=await M(s,n);if(!a)if(i||n>=l)return new Response(JSON.stringify({error:"synthesis_complete",message:"The synthesis is already complete. Your message will be processed as a new conversation."}),{status:200,headers:{"Content-Type":"application/json"}});else return new Response(JSON.stringify({error:"synthesis_not_found",message:"Synthesis data not found. Please start a new conversation."}),{status:404,headers:{"Content-Type":"application/json"}});return new Response(new ReadableStream({async start(e){let t=new TextEncoder,s=a.split(/(\s+)/);for(let r=0;r<s.length;r++){let a=s[r],o={id:g().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"synthesis-continuation",choices:[{index:0,delta:{content:a},finish_reason:null}],synthesis_metadata:{synthesis_id:c,current_chunk:n,total_chunks:l,has_more:!i}};e.enqueue(t.encode(`data: ${JSON.stringify(o)}

`)),r<s.length-1&&await new Promise(e=>setTimeout(e,10))}if(!i){let s={id:g().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"synthesis-continuation",choices:[{index:0,delta:{content:`

**[SYNTHESIS CONTINUES - Please type 'continue' to see the rest (${o})]**`},finish_reason:null}],synthesis_metadata:{synthesis_id:c,current_chunk:n,total_chunks:l,has_more:!0}};e.enqueue(t.encode(`data: ${JSON.stringify(s)}

`))}let r={id:g().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"synthesis-continuation",choices:[{index:0,delta:{},finish_reason:i?"stop":"length"}],synthesis_metadata:{synthesis_id:c,current_chunk:n,total_chunks:l,has_more:!i}};e.enqueue(t.encode(`data: ${JSON.stringify(r)}

`)),e.enqueue(t.encode("data: [DONE]\n\n")),e.close()}}),{headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","X-Synthesis-Progress":o,"X-Synthesis-Complete":i.toString(),"X-Synthesis-ID":c,"X-Synthesis-Chunk-Index":n.toString(),"X-Synthesis-Total-Chunks":l.toString()}})}async function el(e,t,s){let r=new TextEncoder;return new Response(new ReadableStream({async start(e){try{let n={id:g().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-orchestration-enhanced",choices:[{index:0,delta:{content:"\uD83C\uDFAC **Multi-Role AI Orchestration Started!**\n\nYour request is being processed by multiple specialized AI models working together.\n\n"},finish_reason:null}]};e.enqueue(r.encode(`data: ${JSON.stringify(n)}

`)),await ec(t,e,r,s)}catch(s){let t={id:g().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-orchestration-enhanced",choices:[{index:0,delta:{content:`

❌ **Error:** ${s instanceof Error?s.message:"Unknown error occurred"}

`},finish_reason:"stop"}]};e.enqueue(r.encode(`data: ${JSON.stringify(t)}

`)),e.enqueue(r.encode(`data: [DONE]

`)),e.close()}},cancel(){}}),{headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","Access-Control-Allow-Origin":"*","X-Accel-Buffering":"no","X-RoKey-Orchestration-ID":t,"X-RoKey-Orchestration-Active":"true"}})}async function ec(e,t,s,r){let n=await (0,l.x)(),a=Date.now(),i=0,o=!1,c={};for(;Date.now()-a<3e5;)try{let{data:l,error:d}=await n.from("orchestration_executions").select("*").eq("id",e).single();if(d)throw Error(`Failed to fetch execution: ${d.message}`);if(!l)throw Error(`Orchestration execution not found: ${e}`);let{data:u}=await n.from("orchestration_steps").select("*").eq("execution_id",e).order("step_number");if(u&&u.length>0){let n=u.filter(e=>"completed"===e.status),a=u.length;for(let e of(u.find(e=>"in_progress"===e.status),u.find(e=>"pending"===e.status),!o&&a>0&&(o=!0,await ed(u,t,s)),u)){let r=e.step_number,n=e.status,a=c[r];n!==a&&(c[r]=n,await eu(e,n,a,t,s))}if(n.length>i){i=n.length;let e=n[n.length-1];e&&await ep(e,n.length,a,t,s)}if(n.length===a&&a>0){await em(n,t,s),await eg(e,n,t,s,r);return}}else if(Date.now()-a>3e4)throw Error("No orchestration steps found after 30 seconds");await new Promise(e=>setTimeout(e,2e3))}catch(e){throw e}throw Error("Orchestration timed out after 5 minutes")}async function ed(e,t,s){let r={brainstorming_ideation:"\uD83E\uDDE0",coding_backend:"\uD83D\uDCBB",coding_frontend:"\uD83C\uDFA8",education_tutoring:"\uD83C\uDF93",research_analysis:"\uD83D\uDD2C",creative_writing:"✍️",business_strategy:"\uD83D\uDCCA",technical_documentation:"\uD83D\uDCDA"},n={brainstorming_ideation:"Generate creative concepts and innovative ideas",coding_backend:"Implement technical solutions and backend logic",coding_frontend:"Create user interfaces and frontend experiences",education_tutoring:"Add educational elements and learning components",research_analysis:"Conduct research and analyze information",creative_writing:"Craft engaging content and narratives",business_strategy:"Develop strategic approaches and solutions",technical_documentation:"Create comprehensive technical documentation"},a="\uD83D\uDCCB **Orchestration Plan:**\n\n";e.forEach((e,t)=>{let s=r[e.role_id]||"\uD83E\uDD16",i=n[e.role_id]||"Specialized AI processing";a+=`**Step ${e.step_number}:** ${s} ${e.role_id.replace("_"," ").toUpperCase()} Specialist
└─ ${i}

`}),a+=`🤖 **Moderator:** "I've analyzed your request and assembled a team of ${e.length} specialists. Each will contribute their expertise in sequence, and I'll coordinate their work to deliver a comprehensive response."

---

`;let i={id:g().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-orchestration-enhanced",choices:[{index:0,delta:{content:a},finish_reason:null}]};t.enqueue(s.encode(`data: ${JSON.stringify(i)}

`))}async function eu(e,t,s,r,n){let a={brainstorming_ideation:"\uD83E\uDDE0",coding_backend:"\uD83D\uDCBB",coding_frontend:"\uD83C\uDFA8",education_tutoring:"\uD83C\uDF93",research_analysis:"\uD83D\uDD2C",creative_writing:"✍️",business_strategy:"\uD83D\uDCCA",technical_documentation:"\uD83D\uDCDA"}[e.role_id]||"\uD83E\uDD16",i="";if("pending"===t&&"pending"!==s?i=`🤖 **Moderator** → ${a} **${e.role_id.replace("_"," ").toUpperCase()} Specialist:**
"You're up next! Please analyze the request and provide your specialized expertise..."

`:"in_progress"===t&&"in_progress"!==s&&(i=`${a} **${e.role_id.replace("_"," ").toUpperCase()} Specialist:** Working... ⏳
🔄 *Analyzing and processing your request with specialized expertise*

`),i){let e={id:g().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-orchestration-enhanced",choices:[{index:0,delta:{content:i},finish_reason:null}]};r.enqueue(n.encode(`data: ${JSON.stringify(e)}

`))}}async function ep(e,t,s,r,n){let a={brainstorming_ideation:"\uD83E\uDDE0",coding_backend:"\uD83D\uDCBB",coding_frontend:"\uD83C\uDFA8",education_tutoring:"\uD83C\uDF93",research_analysis:"\uD83D\uDD2C",creative_writing:"✍️",business_strategy:"\uD83D\uDCCA",technical_documentation:"\uD83D\uDCDA"}[e.role_id]||"\uD83E\uDD16",i=`${a} **${e.role_id.replace("_"," ").toUpperCase()} Specialist:** ✅ **Complete!**

`,o=Math.floor(85+10*Math.random());i+=`🤖 **Moderator Review:** "Excellent work! Quality: ${o}%. `,t<s?i+=`Passing results to Step ${t+1} specialist..."

📤 *Preparing handoff with context and requirements*

`:i+=`All specialists have completed their work. Ready for synthesis!"

`;let l={id:g().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-orchestration-enhanced",choices:[{index:0,delta:{content:i},finish_reason:null}]};r.enqueue(n.encode(`data: ${JSON.stringify(l)}

`))}async function em(e,t,s){let r="\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**\n\n";r+=`🤖 **Moderator:** "All ${e.length} specialists have completed their work! Now I'll weave their expertise together into a comprehensive response."

---

🧩 **Synthesis Process:**

`;let n={id:g().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-orchestration-enhanced",choices:[{index:0,delta:{content:r},finish_reason:null}]};t.enqueue(s.encode(`data: ${JSON.stringify(n)}

`));let a=["\uD83D\uDD0D Analyzing all specialist contributions...","\uD83E\uDDE0 Identifying key insights and patterns...","\uD83D\uDD17 Finding connections between different perspectives...","\uD83D\uDCDD Structuring the comprehensive response...","✨ Adding final polish and coherence..."];for(let e=0;e<a.length;e++){await new Promise(e=>setTimeout(e,800));let r=`${a[e]} ✅

`,n={id:g().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-orchestration-enhanced",choices:[{index:0,delta:{content:r},finish_reason:null}]};t.enqueue(s.encode(`data: ${JSON.stringify(n)}

`))}await new Promise(e=>setTimeout(e,500));let i={id:g().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-orchestration-enhanced",choices:[{index:0,delta:{content:"\uD83C\uDF89 **Synthesis Complete!** Here's your comprehensive response:\n\n---\n\n"},finish_reason:null}]};t.enqueue(s.encode(`data: ${JSON.stringify(i)}

`))}async function eg(e,t,s,r,n){try{let e=t.filter(e=>e.response&&e.response.trim().length>0);if(0===e.length)throw Error("No valid specialist responses found for synthesis");let n=`As an AI orchestration moderator, synthesize multiple specialist outputs into one cohesive response.

CRITICAL INSTRUCTIONS:
- PRESERVE ALL CODE BLOCKS, TECHNICAL DETAILS, AND IMPLEMENTATION SPECIFICS
- DO NOT SUMMARIZE OR TRUNCATE CODE - INCLUDE EVERY LINE OF CODE PROVIDED
- MAINTAIN ALL TECHNICAL SPECIFICATIONS, FILE STRUCTURES, AND DETAILED EXPLANATIONS
- COMBINE THE OUTPUTS SEAMLESSLY BUT KEEP ALL TECHNICAL CONTENT INTACT
- If specialists provided complete code implementations, include them in full

Original Request: Please provide a comprehensive response based on the following specialist analyses.

Specialist Outputs:
${e.map(e=>`${e.step_number}. ${e.role_id} Specialist Analysis:
${e.response}`).join("\n\n---\n\n")}

SYNTHESIS INSTRUCTIONS:
1. PRESERVE ALL CODE BLOCKS IN THEIR ENTIRETY - Do not truncate, summarize, or abbreviate any code
2. INCLUDE ALL TECHNICAL SPECIFICATIONS, FILE STRUCTURES, AND IMPLEMENTATION DETAILS
3. MAINTAIN ALL FUNCTION DEFINITIONS, CLASS STRUCTURES, AND VARIABLE DECLARATIONS
4. KEEP ALL COMMENTS, DOCUMENTATION, AND EXPLANATORY TEXT FROM THE SPECIALISTS
5. If multiple specialists provided code, include ALL code from ALL specialists
6. Combine the outputs seamlessly while preserving every technical detail
7. Focus on practical implementation with complete, runnable code examples
8. Provide a complete, comprehensive response that fully addresses the original request

Please synthesize the above specialist analyses into a well-structured, comprehensive response that addresses the original request. Include all technical details and provide complete, runnable code examples.`,a=process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;if(!a)throw Error("Classification API key not found");let i=await en("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${a}`,"User-Agent":"RoKey/1.0 (Synthesis)",Origin:"https://rokey.app","Cache-Control":"no-cache"},body:JSON.stringify({model:"gemini-2.0-flash-001",messages:[{role:"user",content:n}],stream:!0,temperature:.3,max_tokens:8e3})});if(!i.ok)throw Error(`Synthesis API error: ${i.status}`);if(!i.body)throw Error("No response body for synthesis stream");let o=i.body.getReader(),l=new TextDecoder;for(;;){let{done:e,value:t}=await o.read();if(e){let e={id:g().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-orchestration-enhanced",choices:[{index:0,delta:{},finish_reason:"stop"}]};s.enqueue(r.encode(`data: ${JSON.stringify(e)}

`)),s.enqueue(r.encode(`data: [DONE]

`)),s.close();break}for(let e of l.decode(t,{stream:!0}).split("\n"))if(e.startsWith("data: ")){let t=e.slice(6);if("[DONE]"===t){let e={id:g().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-orchestration-enhanced",choices:[{index:0,delta:{},finish_reason:"stop"}]};s.enqueue(r.encode(`data: ${JSON.stringify(e)}

`)),s.enqueue(r.encode(`data: [DONE]

`)),s.close();return}try{let e=JSON.parse(t);if(e.choices?.[0]?.delta?.content){let t=e.choices[0].delta.content;t.length;let n={id:g().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-orchestration-enhanced",choices:[{index:0,delta:{content:t},finish_reason:null}]};s.enqueue(r.encode(`data: ${JSON.stringify(n)}

`))}e.choices?.[0]?.finish_reason}catch(e){}}}}catch(n){let e={id:g().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-orchestration-enhanced",choices:[{index:0,delta:{content:`

❌ **Synthesis Error:** ${n instanceof Error?n.message:"Unknown error occurred during synthesis"}

`},finish_reason:null}]};s.enqueue(r.encode(`data: ${JSON.stringify(e)}

`));let t={id:g().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-orchestration-enhanced",choices:[{index:0,delta:{},finish_reason:"stop"}]};s.enqueue(r.encode(`data: ${JSON.stringify(t)}

`)),s.enqueue(r.encode(`data: [DONE]

`)),s.close()}}async function eh(e){let t,s,r=new Date,n=await (0,l.x)();performance.now();let a=e.headers.get("Authorization"),i=process.env.ROKEY_API_ACCESS_TOKEN;if(!i)return o.NextResponse.json({error:"Server configuration error: API access token not configured."},{status:500});if(!a||!a.startsWith("Bearer "))return o.NextResponse.json({error:"Unauthorized: Missing or invalid Authorization header format."},{status:401});if(a.substring(7)!==i)return o.NextResponse.json({error:"Unauthorized: Invalid API token."},{status:401});let d=e.headers.get("X-Synthesis-Execution-Id");if(d)return await ei(e,d);let u=null,m="unknown",g=null,h=null,f=null,_=null,y=null,w=null,k=null,v=null,x=null,I=null,S=null,b=null,C=null,A=null,T=!1,D=null;try{let a=await e.json(),i=G.safeParse(a);if(!i.success)throw _={message:"Invalid request body",issues:i.error.flatten().fieldErrors,status:400};g=(t=i.data).custom_api_config_id;let l=t.messages?.[t.messages.length-1];if(l?.role==="user"&&"string"==typeof l.content){let s=l.content.toLowerCase().trim();if(["continue","continue please","keep going","go on","more","more please","finish","complete","what next","then what","and then"].includes(s)){let s=q(t.messages),r=await P(s);if(r)return await eo(e,t,r);let n=await j(s);if(n&&n.isComplete)return new Response(JSON.stringify({error:"synthesis_complete",message:"The synthesis is already complete. Your message will be processed as a new conversation."}),{status:200,headers:{"Content-Type":"application/json"}})}}let d=Date.now();for(let[e,t]of R.entries())d-t.lastActivity>18e5&&R.delete(e);if(await L(),t.specific_api_key_id)try{let{data:e,error:s}=await n.from("api_keys").select("*").eq("id",t.specific_api_key_id).eq("custom_api_config_id",g).eq("status","active").single();if(s||!e)throw _={message:`Specific API key ${t.specific_api_key_id} not found or not active in this configuration.`,status:404};let r=(0,c.Y)(e.encrypted_api_key),a=await ea(e.provider,e.predefined_model_id,r,t);if(u=e.id,h=e.predefined_model_id,f=e.provider,k=a.llmRequestTimestamp,v=a.llmResponseTimestamp,y=a.status??null,S=a.responseHeaders??null,m="specific_key_retry",a.success){if(w=a.responseData||{note:"streamed via specific key routing"},t.stream&&a.response)return a.response;if(!t.stream&&void 0!==a.responseData)return o.NextResponse.json(w,{status:y||200,headers:a.responseHeaders})}else throw _={message:`Specific API key ${e.id} failed: ${a.error?.message||"Unknown error"}`,status:a.status||500,provider_error:a.error},w=a.error,_}catch(e){_||(_={message:`Error using specific API key: ${e.message}`,status:500})}let b=`${g}:${t.messages?.[t.messages.length-1]?.content?.substring(0,100)||"default"}`,C=z.get(b);if(C&&Date.now()-C.timestamp<18e5){let e=await ea(C.provider,C.model,C.apiKey,t);if(e.success)return B(e,t);z.delete(b)}let[A,T]=await Promise.allSettled([H(g),n.from("custom_api_configs").select("id, name, user_id, routing_strategy, routing_strategy_params").eq("id",g).single()]);if("rejected"===T.status||!T.value.data){let e="rejected"===T.status?T.reason:T.value.error;throw _={message:"Custom API Configuration not found or error fetching it.",status:404,provider_error:e}}let E=T.value.data;D=E.user_id;let $=E.routing_strategy,O=E.routing_strategy_params,N=$&&"none"!==$&&"auto"!==$,U=performance.now(),[M,K]=await Promise.allSettled([(async()=>{if("fulfilled"===A.status&&A.value){let e=A.value.trainingData;return{enhancedMessages:await X(t.messages,e),trainingData:e}}return{enhancedMessages:t.messages,trainingData:null}})(),(async()=>"intelligent_role"===$?await Z(E,g,t,n,e):"strict_fallback"===$?await W(O,g,n):"complexity_round_robin"===$?await Q(E,t,n):{targetApiKeyData:null,roleUsedState:"no_strategy"})()]),F=performance.now(),J=null;if("fulfilled"===M.status&&(t.messages=M.value.enhancedMessages,t.messages.find(e=>"system"===e.role)),"fulfilled"===K.status){if(J=K.value.targetApiKeyData,(m=K.value.roleUsedState)&&m.startsWith("orchestration_started_")){let s=m.split("_")[2];return el(e,s,t)}"classifiedComplexityLevel"in K.value&&void 0!==K.value.classifiedComplexityLevel&&(x=K.value.classifiedComplexityLevel),"classifiedComplexityLLM"in K.value&&void 0!==K.value.classifiedComplexityLLM&&(I=K.value.classifiedComplexityLLM)}else m="routing_failed";if(!J)if(N)if(J=await ee(n,g,t.role))if(t.role){let{data:e}=await n.from("api_key_role_assignments").select("api_key_id").eq("custom_api_config_id",g).eq("api_key_id",J.id).eq("role_name",t.role).maybeSingle();m=e?p.roleRouting(t.role):p.defaultKeySuccess()}else m=p.defaultKeySuccess();else m=`${m}_then_fb_failed_completely`;else{let{data:e,error:s}=await n.from("api_keys").select("*").eq("custom_api_config_id",g).eq("status","active");if(s)_={message:"Database error fetching keys for default routing.",status:500,provider_error:s},m="default_db_error_fetching_keys";else if(e&&0!==e.length){let s=E.routing_strategy_params||{};"number"==typeof s._default_rr_idx&&s._default_rr_idx;let r=[...e].sort((e,t)=>e.id.localeCompare(t.id)),a=r.map(async(e,s)=>{let r=s+1;try{let s=(0,c.Y)(e.encrypted_api_key),n=await ea(e.provider,e.predefined_model_id,s,t);if(n.success)return{success:!0,key:e,result:n,attemptNumber:r};return{success:!1,key:e,error:n.error,status:n.status,attemptNumber:r}}catch(t){return{success:!1,key:e,error:t,status:t.status||500,attemptNumber:r}}});try{let e=await Promise.allSettled(a),i=null,l=null,c=e.length;for(let t of e)if("fulfilled"===t.status&&t.value.success){i=t.value;break}else"fulfilled"===t.status&&(l=t.value);if(i){let e=i.key,a=i.result,l=i.attemptNumber;if(s._default_rr_idx=(r.findIndex(t=>t.id===e.id)+1)%r.length,E.routing_strategy_params=s,setImmediate(async()=>{let{error:e}=await n.from("custom_api_configs").update({routing_strategy_params:s}).eq("id",E.id)}),u=e.id,h=e.predefined_model_id,f=e.provider,k=a?.llmRequestTimestamp||null,v=a?.llmResponseTimestamp||null,y=a?.status??null,S=a?.responseHeaders??null,m=p.defaultKeySuccess(l),_=null,t.stream&&a?.response)return w=a.responseData||{note:"streamed via parallel default routing"},a.response;if(!t.stream&&a?.responseData!==void 0)return w=a.responseData,o.NextResponse.json(w,{status:y||200,headers:a.responseHeaders});w={error:(_={message:`Internal error: Key ${e.id} success but no response data/stream.`,status:500}).message},y=500}else l?(w=l.error,y=l.status??null,_={message:`All ${c} key(s) for parallel default routing failed. Last error from key ${l.key.id}: ${l.error?.message||"Unknown"}`,status:l.status||500,provider_error:l.error},m=p.allKeysFailed(c)):(_={message:`All ${c} key(s) for parallel default routing failed with unknown errors.`,status:500},m=`default_all_parallel_attempts_failed_${c}`)}catch(e){_={message:`Parallel default routing failed: ${e.message}`,status:500},m="default_parallel_execution_error"}!_&&!u&&r.length>0&&(_={message:`All ${r.length} key(s) for default routing were attempted but failed. Status: ${y||"N/A"}. An internal error may have occurred.`,status:y||500,provider_error:null},r.length>0&&(m=p.allKeysFailed(r.length))),_&&!u&&(m=`default_all_attempts_failed_final_err_summary_${_?.message?.substring(0,70)}`)}else _={message:`No active keys configured for RoKey Config ID ${g} to use with default routing.`,status:404},m="default_no_active_keys_for_config"}if(J&&!_)if(u=J.id,h=J.predefined_model_id,f=J.provider){if(!s)try{s=(0,c.Y)(J.encrypted_api_key)}catch(e){_={message:`API Key decryption failed for selected key ${J.id}.`,status:500}}}else _={message:`Selected API key '${u}' does not have a provider configured. Please check the API key settings.`,status:500};else J||_||(_={message:`RoKey could not resolve an API key for this request. Last routing state: ${m||"unknown"}.`,status:404});if(J&&s&&!_&&f&&N){let a=await ea(f,h,s,t);if(k=a.llmRequestTimestamp,v=a.llmResponseTimestamp,y=a.status??null,S=a.responseHeaders??null,a.success)return w=a.responseData||{note:"streamed via explicit strategy"},B(a,t,f,h||void 0,s,{roleUsed:m,routingStrategy:$,complexityLevel:x||void 0,processingTime:F-U});{let s={keyId:u,provider:f,status:a.status,error:a.error,strategy:$};try{let s={custom_api_config_id:g,api_key_id:u,user_id:D||null,predefined_model_id:h,role_requested:t?.role||null,role_used:`${m}_FAILED`,ip_address:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||null,request_timestamp:r.toISOString(),response_timestamp:new Date().toISOString(),status_code:a.status||500,request_payload_summary:t?{messages_count:t.messages?.length,model_requested_passthrough:t.model,stream:t.stream,temp:t.temperature,max_tok:t.max_tokens}:{note:"Request body was not available."},response_payload_summary:{error_type:a.error?.type||a.error?.error?.type||"provider_error",error_message_summary:a.error?.message||a.error?.error?.message||a.error?.details?.message||"Unknown error",provider_status:a.error?.status||a.status,full_error_details:a.error,fallback_initiated:!0},error_message:`ORIGINAL FAILURE: ${a.error?.message||a.error?.error?.message||a.error?.details?.message||JSON.stringify(a.error)||"Unknown error"}. Fallback will be attempted.`,error_source:f,error_details_zod:null,llm_provider_name:f,llm_model_name:h,llm_provider_status_code:a.status,llm_provider_latency_ms:a.llmResponseTimestamp&&a.llmRequestTimestamp?a.llmResponseTimestamp.getTime()-a.llmRequestTimestamp.getTime():null,processing_duration_ms:new Date().getTime()-r.getTime(),classified_role_llm:null,classified_complexity_level:x,classified_complexity_llm:I,cost:null,input_tokens:null,output_tokens:null,is_multimodal:!!t?.messages&&t.messages.some(e=>Array.isArray(e.content)&&e.content.some(e=>"image_url"===e.type))},{error:i}=await n.from("request_logs").insert(s)}catch(e){}let i=!1,l=await ee(n,g,void 0);if(l&&l.id!==u){let e;try{e=(0,c.Y)(l.encrypted_api_key)}catch(t){e=""}if(e){let s=await ea(l.provider,l.predefined_model_id,e,t);if(s.success){if(m=`${m}_FALLBACK_SUCCESS_default`,i=!0,k=s.llmRequestTimestamp,v=s.llmResponseTimestamp,y=s.status??null,S=s.responseHeaders??null,u=l.id,h=l.predefined_model_id,f=l.provider,t.stream&&s.response)return w=s.responseData||{note:"streamed via intelligent fallback to default"},s.response;if(!t.stream&&void 0!==s.responseData)return w=s.responseData,o.NextResponse.json(w,{status:y||200,headers:s.responseHeaders})}}}if(!i){let{data:e,error:s}=await n.from("api_keys").select("*").eq("custom_api_config_id",g).eq("status","active");if(s);else if(e&&e.length>0){let s=new Set([u]);l&&s.add(l.id);let r=e.filter(e=>!s.has(e.id));if(r.length>0){let e=r.map(async e=>{try{let s=(0,c.Y)(e.encrypted_api_key),r=await ea(e.provider,e.predefined_model_id,s,t);if(r.success)return{success:!0,key:e,result:r};return{success:!1,key:e,error:r.error}}catch(t){return{success:!1,key:e,error:t}}});try{for(let s of(await Promise.allSettled(e)))if("fulfilled"===s.status&&s.value.success){let e=s.value,r=e.key,n=e.result;if(m=`${m}_PARALLEL_FALLBACK_SUCCESS_${r.id}`,i=!0,k=n?.llmRequestTimestamp||null,v=n?.llmResponseTimestamp||null,y=n?.status??null,S=n?.responseHeaders??null,u=r.id,h=r.predefined_model_id,f=r.provider,t.stream&&n?.response)return w=n.responseData||{note:`streamed via parallel fallback to ${r.id}`},n.response;if(!t.stream&&n?.responseData!==void 0)return w=n.responseData,o.NextResponse.json(w,{status:y||200,headers:n.responseHeaders});break}}catch(e){}}}}i||(m=`${m}_all_fallbacks_failed`,_={message:`Intelligent routing (${s.strategy}) failed for key ${s.keyId} (${s.provider}). All fallback attempts also failed. Original error: ${s.error?.message||"Unknown"}`,status:s.status||500,provider_error:s.error,fallback_attempted:!0},w=s.error,y=s.status??null)}}else J&&!s&&!_&&f&&N?_||(_={message:`API key ${u} selected but decryption failed (safeguard).`,status:500}):J&&s&&!_&&!f&&N&&!_&&(_={message:`API key ${u} selected but has no provider configured (safeguard).`,status:500});if(_){let e=_.status||500,t=_.message||"An unexpected internal server error occurred.",s=_.issues,r=_.provider_error;return!y&&_.status&&_.provider_error&&(y=_.status),!w&&t&&(w={error:{message:t,...r&&{details:r}}}),o.NextResponse.json({error:t,...s&&{issues:s},...r&&{provider_error_details:r}},{status:e})}if(!_&&!u)return _={message:"Critical internal error: No API key processed and no explicit error state.",status:500},o.NextResponse.json({error:_.message},{status:_.status});if(w&&!t?.stream)return o.NextResponse.json(w,{status:y||200});return o.NextResponse.json({error:"An unexpected critical server error occurred."},{status:500})}finally{let s=new Date,a=s.getTime()-r.getTime(),i=null;k&&v&&(i=v.getTime()-k.getTime());let o=a-(i||0);performance.now(),t?.messages&&(T=t.messages.some(e=>Array.isArray(e.content)&&e.content.some(e=>"image_url"===e.type))),w?.usage?(C=w.usage.prompt_tokens||w.usage.input_tokens||null,A=w.usage.completion_tokens||w.usage.output_tokens||null):f?.toLowerCase()==="google"&&w?.promptFeedback?.tokenCount!==void 0&&(C=w.promptFeedback.tokenCount,A=w.candidates?.[0]?.tokenCount||null),function(e,t){if(!e)return!1;let s=e.toLowerCase();if("deepseek"===s)return!0;if(("google"===s||"gemini"===s)&&t)for(let e of["x-ratelimit-limit","x-ratelimit-requests-limit","x-goog-quota-limit","quota-limit"]){let s=t.get(e);if(s){let e=parseInt(s);if(!isNaN(e))return e<=60}}return!1}(f,S||void 0)?b=0:w?.usage?.cost&&f?.toLowerCase()==="openrouter"?b=1e-6*w.usage.cost:null!==C&&null!==A&&h&&(setImmediate(async()=>{try{let{data:e,error:t}=await n.from("models").select("input_token_price, output_token_price").eq("id",h).single();if(!t&&e?.input_token_price&&e?.output_token_price&&null!==C&&null!==A){let t=C*e.input_token_price,s=A*e.output_token_price;l&&await n.from("request_logs").update({cost:t+s}).eq("custom_api_config_id",l).eq("request_timestamp",r.toISOString())}}catch(e){}}),b=null);let l=g||t?.custom_api_config_id;l?setImmediate(async()=>{try{let a={custom_api_config_id:l,api_key_id:u,user_id:D||null,predefined_model_id:h,role_requested:t?.role||null,role_used:m,ip_address:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||null,request_timestamp:r.toISOString(),response_timestamp:s.toISOString(),status_code:_?_.status||500:y||200,request_payload_summary:t?{messages_count:t.messages?.length,model_requested_passthrough:t.model,stream:t.stream,temp:t.temperature,max_tok:t.max_tokens}:{note:"Request body was not available or Zod validation failed."},response_payload_summary:{usage:w?.usage,finish_reason:w?.choices?.[0]?.finish_reason,error_type:w?.error?.type,error_message_summary:w?.error?.message,full_error_details:w?.error,is_fallback_success:m?.includes("FALLBACK_SUCCESS")||!1,original_failure_summary:m?.includes("FAILED")?"See previous log entry for original failure details":null},error_message:m?.includes("FALLBACK_SUCCESS")?"FALLBACK SUCCESS: Original model failed, successfully used fallback model. Check previous log entry for failure details.":_?.message?_.message:w?.error?.message?w.error.message:null,error_source:_?_.provider_error&&f?f:"RoKey":w?.error?f:null,error_details_zod:_?.issues?JSON.stringify(_.issues):null,llm_provider_name:f,llm_model_name:h,llm_provider_status_code:y,llm_provider_latency_ms:i,processing_duration_ms:o,classified_role_llm:null,classified_complexity_level:x,classified_complexity_llm:I,cost:b,input_tokens:C,output_tokens:A,is_multimodal:T},{error:c}=await n.from("request_logs").insert(a)}catch(e){}}):_&&setImmediate(async()=>{try{let i={custom_api_config_id:null,api_key_id:null,user_id:null,predefined_model_id:null,role_requested:t?.role||null,role_used:null,ip_address:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||null,request_timestamp:r.toISOString(),response_timestamp:s.toISOString(),status_code:_.status||500,request_payload_summary:{note:"Early error, request body may be malformed.",custom_api_config_id_attempted:g},response_payload_summary:{error_message_summary:_.message?.substring(0,100)},error_message:_.message,error_source:"RoKey",error_details_zod:_.issues?JSON.stringify(_.issues):null,llm_provider_name:null,llm_model_name:null,llm_provider_status_code:null,llm_provider_latency_ms:null,processing_duration_ms:a,classified_role_llm:null,classified_complexity_level:null,classified_complexity_llm:null,cost:null,input_tokens:null,output_tokens:null,is_multimodal:!1},{error:o}=await n.from("request_logs").insert(i)}catch(e){}})}}async function ef(e){return o.NextResponse.json({},{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}async function e_(e,t,s,r,n,a,i,o,l){let c=await ey(i,s.roles,t.messages||[]),d=[];for(let e=0;e<c.length;e++){let t=c[e],i=s.roles.find(e=>e.roleId===t.roleId),u=await ew(r,t.roleId,n,a,l),p=u.apiKey,m=u.assignmentType;d.push({execution_id:o,step_number:e+1,role_id:t.roleId,api_key_id:p?.id||null,model_name:p?.predefined_model_id||"unknown",prompt:t.prompt,status:0===e?"pending":"waiting",assignment_type:m,confidence:i?.confidence||.8})}try{let{error:e}=await l.from("orchestration_steps").insert(d);if(e)throw Error(`Failed to create orchestration steps: ${e.message}`)}catch(e){throw e}try{await l.from("orchestration_executions").update({status:"in_progress"}).eq("id",o)}catch(e){}let u=e.nextUrl.origin,p=`${u}/api/orchestration/stream/${o}`,m=`${u}/api/orchestration/status/${o}`;setTimeout(async()=>{try{let e=await fetch(`${u}/api/orchestration/start`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({executionId:o})});e.ok||await e.text()}catch(e){}},1e3);let h=new TextEncoder;return new Response(new ReadableStream({async start(e){let t={id:g().randomUUID(),type:"orchestration_started",data:{execution_id:o,stream_url:p,status_url:m,total_steps:c.length,current_step:1,status:"in_progress",message:"Orchestration started. Connecting to stream..."}};e.enqueue(h.encode(`data: ${JSON.stringify(t)}

`)),await new Promise(e=>setTimeout(e,1e3)),e.close()},cancel(){}}),{headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","Access-Control-Allow-Origin":"*","X-Accel-Buffering":"no"}})}async function ey(e,t,s=[]){let r=[...t].sort((e,t)=>e.executionOrder-t.executionOrder),n=[];if(1===r.length)n.push({roleId:r[0].roleId,prompt:e,moderatorInstructions:`This is a single-role task for ${r[0].roleId}. Ensure the response fully addresses the user's request.`});else if(r.length>1){let t=r[0],s=function(e,t,s){let r={brainstorming:`Focus on brainstorming creative ideas for this request: "${e}"

Provide detailed concepts, possibilities, and innovative approaches. Don't implement or code anything yet, just focus on generating ideas.`,coding_frontend:`${s?`Develop frontend code for this request: "${e}"

Provide complete, well-commented code that addresses the requirements.`:"Based on the previous work, implement the frontend code needed. Make sure your code is complete and well-commented."}`,coding_backend:`${s?`Develop backend code for this request: "${e}"

Provide complete, well-commented code that addresses the requirements.`:"Based on the previous work, implement the backend code needed. Make sure your code is complete and well-commented."}`,content_writing:`${s?`Write content for this request: "${e}"

Create high-quality, engaging content that addresses the requirements.`:"Based on the previous work, create polished content that effectively communicates the ideas."}`,research:`${s?`Research information for this request: "${e}"

Provide comprehensive, accurate information with proper citations where possible.`:"Based on the previous work, conduct thorough research to expand and validate the information."}`,logic_reasoning:`${s?`Apply logical reasoning to this request: "${e}"

Analyze the problem systematically and provide a structured solution approach.`:"Based on the previous work, apply logical reasoning to refine and improve the solution."}`,default:`${s?`Handle the ${t} aspect of this request: "${e}"

Focus specifically on your role's expertise.`:`Based on the previous work, address the ${t} aspect of this request.`}`};return r[t]||r.default}(e,t.roleId,!0);n.push({roleId:t.roleId,prompt:`${s}

IMPORTANT: Your output will be passed to a ${r[1].roleId} specialist next, so focus on providing information that will be useful for that role.`,outputFormat:"Detailed output for the next step",moderatorInstructions:`This is the first step in a multi-role task. The ${t.roleId} specialist should provide output that will be useful for the ${r[1].roleId} specialist in the next step.`});for(let t=1;t<r.length-1;t++){let s=r[t],a=r[t+1];n.push({roleId:s.roleId,prompt:`You are working as part of an AI team to address this request: "${e}"

A ${r[t-1].roleId} specialist has already worked on this and produced the following output:

{{previousOutput}}

Your role is to handle the ${s.roleId} aspect of this request. Your output will be passed to a ${a.roleId} specialist next, so focus on providing information that will be useful for that role.`,outputFormat:"Detailed output for the next step",moderatorInstructions:`This is step ${t+1} in a multi-role task. The ${s.roleId} specialist should build on the previous output and prepare content for the ${a.roleId} specialist.`})}let a=r[r.length-1];n.push({roleId:a.roleId,prompt:`You are the final specialist in an AI team addressing this request: "${e}"

Previous specialists have already worked on this and produced the following output:

{{previousOutput}}

As the ${a.roleId} specialist, your role is to finalize the solution and provide a complete, polished response to the user's original request.`,outputFormat:"Final complete solution",moderatorInstructions:`This is the final step in a multi-role task. The ${a.roleId} specialist should provide a complete solution that incorporates all previous work.`})}return n}async function ew(e,t,s,r,n){try{let e=r.find(e=>e.role_name===t);if(e&&e.api_key_id){let t=s.find(t=>t.id===e.api_key_id&&"active"===t.status);if(t)return{apiKey:t,assignmentType:"assigned"}}let n=s.find(e=>!0===e.is_default_general_chat_model&&"active"===e.status);if(n)return{apiKey:n,assignmentType:"general_chat_fallback"};let a=s.find(e=>"active"===e.status);if(a)return{apiKey:a,assignmentType:"first_available_fallback"};return{apiKey:null,assignmentType:"none"}}catch(e){return{apiKey:null,assignmentType:"none"}}}let ek=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/v1/chat/completions/route",pathname:"/api/v1/chat/completions",filename:"route",bundlePath:"app/api/v1/chat/completions/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\v1\\chat\\completions\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:ev,workUnitAsyncStorage:ex,serverHooks:eI}=ek;function eS(){return(0,i.patchFetch)({workAsyncStorage:ev,workUnitAsyncStorage:ex})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,9398,3410,5697],()=>s(98811));module.exports=r})();
(()=>{var e={};e.id=1087,e.ids=[1087],e.modules={2507:(e,t,n)=>{"use strict";n.d(t,{x:()=>i});var s=n(34386),o=n(44999);async function i(){let e=await (0,o.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,n,s){try{e.set({name:t,value:n,...s})}catch(e){}},remove(t,n){try{e.set({name:t,value:"",...n})}catch(e){}}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19902:(e,t,n)=>{"use strict";n.r(t),n.d(t,{patchFetch:()=>ed,routeModule:()=>er,serverHooks:()=>el,workAsyncStorage:()=>ea,workUnitAsyncStorage:()=>ec});var s,o,i,r,a,c,l,d,u,h,f,p,g={};n.r(g),n.d(g,{GET:()=>ei});var E=n(96559),m=n(48088),C=n(37719),y=n(32190),O=n(2507);!function(e){e.STRING="string",e.NUMBER="number",e.INTEGER="integer",e.BOOLEAN="boolean",e.ARRAY="array",e.OBJECT="object"}(s||(s={})),function(e){e.LANGUAGE_UNSPECIFIED="language_unspecified",e.PYTHON="python"}(o||(o={})),function(e){e.OUTCOME_UNSPECIFIED="outcome_unspecified",e.OUTCOME_OK="outcome_ok",e.OUTCOME_FAILED="outcome_failed",e.OUTCOME_DEADLINE_EXCEEDED="outcome_deadline_exceeded"}(i||(i={}));let I=["user","model","function","system"];!function(e){e.HARM_CATEGORY_UNSPECIFIED="HARM_CATEGORY_UNSPECIFIED",e.HARM_CATEGORY_HATE_SPEECH="HARM_CATEGORY_HATE_SPEECH",e.HARM_CATEGORY_SEXUALLY_EXPLICIT="HARM_CATEGORY_SEXUALLY_EXPLICIT",e.HARM_CATEGORY_HARASSMENT="HARM_CATEGORY_HARASSMENT",e.HARM_CATEGORY_DANGEROUS_CONTENT="HARM_CATEGORY_DANGEROUS_CONTENT",e.HARM_CATEGORY_CIVIC_INTEGRITY="HARM_CATEGORY_CIVIC_INTEGRITY"}(r||(r={})),function(e){e.HARM_BLOCK_THRESHOLD_UNSPECIFIED="HARM_BLOCK_THRESHOLD_UNSPECIFIED",e.BLOCK_LOW_AND_ABOVE="BLOCK_LOW_AND_ABOVE",e.BLOCK_MEDIUM_AND_ABOVE="BLOCK_MEDIUM_AND_ABOVE",e.BLOCK_ONLY_HIGH="BLOCK_ONLY_HIGH",e.BLOCK_NONE="BLOCK_NONE"}(a||(a={})),function(e){e.HARM_PROBABILITY_UNSPECIFIED="HARM_PROBABILITY_UNSPECIFIED",e.NEGLIGIBLE="NEGLIGIBLE",e.LOW="LOW",e.MEDIUM="MEDIUM",e.HIGH="HIGH"}(c||(c={})),function(e){e.BLOCKED_REASON_UNSPECIFIED="BLOCKED_REASON_UNSPECIFIED",e.SAFETY="SAFETY",e.OTHER="OTHER"}(l||(l={})),function(e){e.FINISH_REASON_UNSPECIFIED="FINISH_REASON_UNSPECIFIED",e.STOP="STOP",e.MAX_TOKENS="MAX_TOKENS",e.SAFETY="SAFETY",e.RECITATION="RECITATION",e.LANGUAGE="LANGUAGE",e.BLOCKLIST="BLOCKLIST",e.PROHIBITED_CONTENT="PROHIBITED_CONTENT",e.SPII="SPII",e.MALFORMED_FUNCTION_CALL="MALFORMED_FUNCTION_CALL",e.OTHER="OTHER"}(d||(d={})),function(e){e.TASK_TYPE_UNSPECIFIED="TASK_TYPE_UNSPECIFIED",e.RETRIEVAL_QUERY="RETRIEVAL_QUERY",e.RETRIEVAL_DOCUMENT="RETRIEVAL_DOCUMENT",e.SEMANTIC_SIMILARITY="SEMANTIC_SIMILARITY",e.CLASSIFICATION="CLASSIFICATION",e.CLUSTERING="CLUSTERING"}(u||(u={})),function(e){e.MODE_UNSPECIFIED="MODE_UNSPECIFIED",e.AUTO="AUTO",e.ANY="ANY",e.NONE="NONE"}(h||(h={})),function(e){e.MODE_UNSPECIFIED="MODE_UNSPECIFIED",e.MODE_DYNAMIC="MODE_DYNAMIC"}(f||(f={}));class _ extends Error{constructor(e){super(`[GoogleGenerativeAI Error]: ${e}`)}}class T extends _{constructor(e,t){super(e),this.response=t}}class S extends _{constructor(e,t,n,s){super(e),this.status=t,this.statusText=n,this.errorDetails=s}}class N extends _{}class w extends _{}!function(e){e.GENERATE_CONTENT="generateContent",e.STREAM_GENERATE_CONTENT="streamGenerateContent",e.COUNT_TOKENS="countTokens",e.EMBED_CONTENT="embedContent",e.BATCH_EMBED_CONTENTS="batchEmbedContents"}(p||(p={}));class R{constructor(e,t,n,s,o){this.model=e,this.task=t,this.apiKey=n,this.stream=s,this.requestOptions=o}toString(){var e,t;let n=(null==(e=this.requestOptions)?void 0:e.apiVersion)||"v1beta",s=(null==(t=this.requestOptions)?void 0:t.baseUrl)||"https://generativelanguage.googleapis.com",o=`${s}/${n}/${this.model}:${this.task}`;return this.stream&&(o+="?alt=sse"),o}}async function A(e){var t;let n=new Headers;n.append("Content-Type","application/json"),n.append("x-goog-api-client",function(e){let t=[];return(null==e?void 0:e.apiClient)&&t.push(e.apiClient),t.push("genai-js/0.24.1"),t.join(" ")}(e.requestOptions)),n.append("x-goog-api-key",e.apiKey);let s=null==(t=e.requestOptions)?void 0:t.customHeaders;if(s){if(!(s instanceof Headers))try{s=new Headers(s)}catch(e){throw new N(`unable to convert customHeaders value ${JSON.stringify(s)} to Headers: ${e.message}`)}for(let[e,t]of s.entries()){if("x-goog-api-key"===e)throw new N(`Cannot set reserved header name ${e}`);if("x-goog-api-client"===e)throw new N(`Header name ${e} can only be set using the apiClient field`);n.append(e,t)}}return n}async function v(e,t,n,s,o,i){let r=new R(e,t,n,s,i);return{url:r.toString(),fetchOptions:Object.assign(Object.assign({},function(e){let t={};if((null==e?void 0:e.signal)!==void 0||(null==e?void 0:e.timeout)>=0){let n=new AbortController;(null==e?void 0:e.timeout)>=0&&setTimeout(()=>n.abort(),e.timeout),(null==e?void 0:e.signal)&&e.signal.addEventListener("abort",()=>{n.abort()}),t.signal=n.signal}return t}(i)),{method:"POST",headers:await A(r),body:o})}}async function x(e,t,n,s,o,i={},r=fetch){let{url:a,fetchOptions:c}=await v(e,t,n,s,o,i);return b(a,c,r)}async function b(e,t,n=fetch){let s;try{s=await n(e,t)}catch(n){var o=n,i=e;let t=o;throw"AbortError"===t.name?(t=new w(`Request aborted when fetching ${i.toString()}: ${o.message}`)).stack=o.stack:o instanceof S||o instanceof N||((t=new _(`Error fetching from ${i.toString()}: ${o.message}`)).stack=o.stack),t}return s.ok||await M(s,e),s}async function M(e,t){let n,s="";try{let t=await e.json();s=t.error.message,t.error.details&&(s+=` ${JSON.stringify(t.error.details)}`,n=t.error.details)}catch(e){}throw new S(`Error fetching from ${t.toString()}: [${e.status} ${e.statusText}] ${s}`,e.status,e.statusText,n)}function D(e){return e.text=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&console.warn(`This response had ${e.candidates.length} candidates. Returning text from the first candidate only. Access response.candidates directly to use the other candidates.`),P(e.candidates[0]))throw new T(`${j(e)}`,e);return function(e){var t,n,s,o;let i=[];if(null==(n=null==(t=e.candidates)?void 0:t[0].content)?void 0:n.parts)for(let t of null==(o=null==(s=e.candidates)?void 0:s[0].content)?void 0:o.parts)t.text&&i.push(t.text),t.executableCode&&i.push("\n```"+t.executableCode.language+"\n"+t.executableCode.code+"\n```\n"),t.codeExecutionResult&&i.push("\n```\n"+t.codeExecutionResult.output+"\n```\n");return i.length>0?i.join(""):""}(e)}if(e.promptFeedback)throw new T(`Text not available. ${j(e)}`,e);return""},e.functionCall=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&console.warn(`This response had ${e.candidates.length} candidates. Returning function calls from the first candidate only. Access response.candidates directly to use the other candidates.`),P(e.candidates[0]))throw new T(`${j(e)}`,e);return console.warn("response.functionCall() is deprecated. Use response.functionCalls() instead."),L(e)[0]}if(e.promptFeedback)throw new T(`Function call not available. ${j(e)}`,e)},e.functionCalls=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&console.warn(`This response had ${e.candidates.length} candidates. Returning function calls from the first candidate only. Access response.candidates directly to use the other candidates.`),P(e.candidates[0]))throw new T(`${j(e)}`,e);return L(e)}if(e.promptFeedback)throw new T(`Function call not available. ${j(e)}`,e)},e}function L(e){var t,n,s,o;let i=[];if(null==(n=null==(t=e.candidates)?void 0:t[0].content)?void 0:n.parts)for(let t of null==(o=null==(s=e.candidates)?void 0:s[0].content)?void 0:o.parts)t.functionCall&&i.push(t.functionCall);return i.length>0?i:void 0}let U=[d.RECITATION,d.SAFETY,d.LANGUAGE];function P(e){return!!e.finishReason&&U.includes(e.finishReason)}function j(e){var t,n,s;let o="";if((!e.candidates||0===e.candidates.length)&&e.promptFeedback)o+="Response was blocked",(null==(t=e.promptFeedback)?void 0:t.blockReason)&&(o+=` due to ${e.promptFeedback.blockReason}`),(null==(n=e.promptFeedback)?void 0:n.blockReasonMessage)&&(o+=`: ${e.promptFeedback.blockReasonMessage}`);else if(null==(s=e.candidates)?void 0:s[0]){let t=e.candidates[0];P(t)&&(o+=`Candidate was blocked due to ${t.finishReason}`,t.finishMessage&&(o+=`: ${t.finishMessage}`))}return o}function H(e){return this instanceof H?(this.v=e,this):new H(e)}"function"==typeof SuppressedError&&SuppressedError;let $=/^data\: (.*)(?:\n\n|\r\r|\r\n\r\n)/;async function q(e){let t=[],n=e.getReader();for(;;){let{done:e,value:s}=await n.read();if(e)return D(function(e){let t=e[e.length-1],n={promptFeedback:null==t?void 0:t.promptFeedback};for(let t of e){if(t.candidates){let e=0;for(let s of t.candidates)if(n.candidates||(n.candidates=[]),n.candidates[e]||(n.candidates[e]={index:e}),n.candidates[e].citationMetadata=s.citationMetadata,n.candidates[e].groundingMetadata=s.groundingMetadata,n.candidates[e].finishReason=s.finishReason,n.candidates[e].finishMessage=s.finishMessage,n.candidates[e].safetyRatings=s.safetyRatings,s.content&&s.content.parts){n.candidates[e].content||(n.candidates[e].content={role:s.content.role||"user",parts:[]});let t={};for(let o of s.content.parts)o.text&&(t.text=o.text),o.functionCall&&(t.functionCall=o.functionCall),o.executableCode&&(t.executableCode=o.executableCode),o.codeExecutionResult&&(t.codeExecutionResult=o.codeExecutionResult),0===Object.keys(t).length&&(t.text=""),n.candidates[e].content.parts.push(t)}e++}t.usageMetadata&&(n.usageMetadata=t.usageMetadata)}return n}(t));t.push(s)}}async function F(e,t,n,s){let[o,i]=(function(e){let t=e.getReader();return new ReadableStream({start(e){let n="";return function s(){return t.read().then(({value:t,done:o})=>{let i;if(o)return n.trim()?void e.error(new _("Failed to parse stream")):void e.close();let r=(n+=t).match($);for(;r;){try{i=JSON.parse(r[1])}catch(t){e.error(new _(`Error parsing JSON response: "${r[1]}"`));return}e.enqueue(i),r=(n=n.substring(r[0].length)).match($)}return s()}).catch(e=>{let t=e;throw t.stack=e.stack,t="AbortError"===t.name?new w("Request aborted when reading from the stream"):new _("Error reading from the stream")})}()}})})((await x(t,p.STREAM_GENERATE_CONTENT,e,!0,JSON.stringify(n),s)).body.pipeThrough(new TextDecoderStream("utf8",{fatal:!0}))).tee();return{stream:function(e){return function(e,t,n){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var s,o=n.apply(e,t||[]),i=[];return s={},r("next"),r("throw"),r("return"),s[Symbol.asyncIterator]=function(){return this},s;function r(e){o[e]&&(s[e]=function(t){return new Promise(function(n,s){i.push([e,t,n,s])>1||a(e,t)})})}function a(e,t){try{var n;(n=o[e](t)).value instanceof H?Promise.resolve(n.value.v).then(c,l):d(i[0][2],n)}catch(e){d(i[0][3],e)}}function c(e){a("next",e)}function l(e){a("throw",e)}function d(e,t){e(t),i.shift(),i.length&&a(i[0][0],i[0][1])}}(this,arguments,function*(){let t=e.getReader();for(;;){let{value:e,done:n}=yield H(t.read());if(n)break;yield yield H(D(e))}})}(o),response:q(i)}}async function k(e,t,n,s){let o=await x(t,p.GENERATE_CONTENT,e,!1,JSON.stringify(n),s);return{response:D(await o.json())}}function G(e){if(null!=e){if("string"==typeof e)return{role:"system",parts:[{text:e}]};if(e.text)return{role:"system",parts:[e]};if(e.parts)if(!e.role)return{role:"system",parts:e.parts};else return e}}function Y(e){let t=[];if("string"==typeof e)t=[{text:e}];else for(let n of e)"string"==typeof n?t.push({text:n}):t.push(n);var n=t;let s={role:"user",parts:[]},o={role:"function",parts:[]},i=!1,r=!1;for(let e of n)"functionResponse"in e?(o.parts.push(e),r=!0):(s.parts.push(e),i=!0);if(i&&r)throw new _("Within a single message, FunctionResponse cannot be mixed with other type of part in the request for sending chat message.");if(!i&&!r)throw new _("No content is provided for sending chat message.");return i?s:o}function K(e){let t;return t=e.contents?e:{contents:[Y(e)]},e.systemInstruction&&(t.systemInstruction=G(e.systemInstruction)),t}let B=["text","inlineData","functionCall","functionResponse","executableCode","codeExecutionResult"],J={user:["text","inlineData"],function:["functionResponse"],model:["text","functionCall","executableCode","codeExecutionResult"],system:["text"]};function V(e){var t;if(void 0===e.candidates||0===e.candidates.length)return!1;let n=null==(t=e.candidates[0])?void 0:t.content;if(void 0===n||void 0===n.parts||0===n.parts.length)return!1;for(let e of n.parts)if(void 0===e||0===Object.keys(e).length||void 0!==e.text&&""===e.text)return!1;return!0}let X="SILENT_ERROR";class z{constructor(e,t,n,s={}){this.model=t,this.params=n,this._requestOptions=s,this._history=[],this._sendPromise=Promise.resolve(),this._apiKey=e,(null==n?void 0:n.history)&&(!function(e){let t=!1;for(let n of e){let{role:e,parts:s}=n;if(!t&&"user"!==e)throw new _(`First content should be with role 'user', got ${e}`);if(!I.includes(e))throw new _(`Each item should include role field. Got ${e} but valid roles are: ${JSON.stringify(I)}`);if(!Array.isArray(s))throw new _("Content should have 'parts' property with an array of Parts");if(0===s.length)throw new _("Each Content should have at least one part");let o={text:0,inlineData:0,functionCall:0,functionResponse:0,fileData:0,executableCode:0,codeExecutionResult:0};for(let e of s)for(let t of B)t in e&&(o[t]+=1);let i=J[e];for(let t of B)if(!i.includes(t)&&o[t]>0)throw new _(`Content with role '${e}' can't contain '${t}' part`);t=!0}}(n.history),this._history=n.history)}async getHistory(){return await this._sendPromise,this._history}async sendMessage(e,t={}){var n,s,o,i,r,a;let c;await this._sendPromise;let l=Y(e),d={safetySettings:null==(n=this.params)?void 0:n.safetySettings,generationConfig:null==(s=this.params)?void 0:s.generationConfig,tools:null==(o=this.params)?void 0:o.tools,toolConfig:null==(i=this.params)?void 0:i.toolConfig,systemInstruction:null==(r=this.params)?void 0:r.systemInstruction,cachedContent:null==(a=this.params)?void 0:a.cachedContent,contents:[...this._history,l]},u=Object.assign(Object.assign({},this._requestOptions),t);return this._sendPromise=this._sendPromise.then(()=>k(this._apiKey,this.model,d,u)).then(e=>{var t;if(V(e.response)){this._history.push(l);let n=Object.assign({parts:[],role:"model"},null==(t=e.response.candidates)?void 0:t[0].content);this._history.push(n)}else{let t=j(e.response);t&&console.warn(`sendMessage() was unsuccessful. ${t}. Inspect response object for details.`)}c=e}).catch(e=>{throw this._sendPromise=Promise.resolve(),e}),await this._sendPromise,c}async sendMessageStream(e,t={}){var n,s,o,i,r,a;await this._sendPromise;let c=Y(e),l={safetySettings:null==(n=this.params)?void 0:n.safetySettings,generationConfig:null==(s=this.params)?void 0:s.generationConfig,tools:null==(o=this.params)?void 0:o.tools,toolConfig:null==(i=this.params)?void 0:i.toolConfig,systemInstruction:null==(r=this.params)?void 0:r.systemInstruction,cachedContent:null==(a=this.params)?void 0:a.cachedContent,contents:[...this._history,c]},d=Object.assign(Object.assign({},this._requestOptions),t),u=F(this._apiKey,this.model,l,d);return this._sendPromise=this._sendPromise.then(()=>u).catch(e=>{throw Error(X)}).then(e=>e.response).then(e=>{if(V(e)){this._history.push(c);let t=Object.assign({},e.candidates[0].content);t.role||(t.role="model"),this._history.push(t)}else{let t=j(e);t&&console.warn(`sendMessageStream() was unsuccessful. ${t}. Inspect response object for details.`)}}).catch(e=>{e.message!==X&&console.error(e)}),u}}async function W(e,t,n,s){return(await x(t,p.COUNT_TOKENS,e,!1,JSON.stringify(n),s)).json()}async function Z(e,t,n,s){return(await x(t,p.EMBED_CONTENT,e,!1,JSON.stringify(n),s)).json()}async function Q(e,t,n,s){let o=n.requests.map(e=>Object.assign(Object.assign({},e),{model:t}));return(await x(t,p.BATCH_EMBED_CONTENTS,e,!1,JSON.stringify({requests:o}),s)).json()}class ee{constructor(e,t,n={}){this.apiKey=e,this._requestOptions=n,t.model.includes("/")?this.model=t.model:this.model=`models/${t.model}`,this.generationConfig=t.generationConfig||{},this.safetySettings=t.safetySettings||[],this.tools=t.tools,this.toolConfig=t.toolConfig,this.systemInstruction=G(t.systemInstruction),this.cachedContent=t.cachedContent}async generateContent(e,t={}){var n;let s=K(e),o=Object.assign(Object.assign({},this._requestOptions),t);return k(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null==(n=this.cachedContent)?void 0:n.name},s),o)}async generateContentStream(e,t={}){var n;let s=K(e),o=Object.assign(Object.assign({},this._requestOptions),t);return F(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null==(n=this.cachedContent)?void 0:n.name},s),o)}startChat(e){var t;return new z(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null==(t=this.cachedContent)?void 0:t.name},e),this._requestOptions)}async countTokens(e,t={}){let n=function(e,t){var n;let s={model:null==t?void 0:t.model,generationConfig:null==t?void 0:t.generationConfig,safetySettings:null==t?void 0:t.safetySettings,tools:null==t?void 0:t.tools,toolConfig:null==t?void 0:t.toolConfig,systemInstruction:null==t?void 0:t.systemInstruction,cachedContent:null==(n=null==t?void 0:t.cachedContent)?void 0:n.name,contents:[]},o=null!=e.generateContentRequest;if(e.contents){if(o)throw new N("CountTokensRequest must have one of contents or generateContentRequest, not both.");s.contents=e.contents}else if(o)s=Object.assign(Object.assign({},s),e.generateContentRequest);else{let t=Y(e);s.contents=[t]}return{generateContentRequest:s}}(e,{model:this.model,generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:this.cachedContent}),s=Object.assign(Object.assign({},this._requestOptions),t);return W(this.apiKey,this.model,n,s)}async embedContent(e,t={}){let n="string"==typeof e||Array.isArray(e)?{content:Y(e)}:e,s=Object.assign(Object.assign({},this._requestOptions),t);return Z(this.apiKey,this.model,n,s)}async batchEmbedContents(e,t={}){let n=Object.assign(Object.assign({},this._requestOptions),t);return Q(this.apiKey,this.model,e,n)}}class et{constructor(e){this.apiKey=e}getGenerativeModel(e,t){if(!e.model)throw new _("Must provide a model name. Example: genai.getGenerativeModel({ model: 'my-model-name' })");return new ee(this.apiKey,e,t)}getGenerativeModelFromCachedContent(e,t,n){if(!e.name)throw new N("Cached content must contain a `name` field.");if(!e.model)throw new N("Cached content must contain a `model` field.");for(let n of["model","systemInstruction"])if((null==t?void 0:t[n])&&e[n]&&(null==t?void 0:t[n])!==e[n]){if("model"===n&&(t.model.startsWith("models/")?t.model.replace("models/",""):t.model)===(e.model.startsWith("models/")?e.model.replace("models/",""):e.model))continue;throw new N(`Different value for "${n}" specified in modelParams (${t[n]}) and cachedContent (${e[n]})`)}let s=Object.assign(Object.assign({},t),{model:e.model,tools:e.tools,toolConfig:e.toolConfig,systemInstruction:e.systemInstruction,cachedContent:e});return new ee(this.apiKey,s,n)}}var en=n(55511),es=n.n(en);new et(process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY||"");let eo=new Map;async function ei(e,{params:t}){let{executionId:n}=await t;if(!n)return y.NextResponse.json({error:"Execution ID is required"},{status:400});let s=await (0,O.x)(),o=new ReadableStream({async start(t){let o=Math.random().toString(36).substring(7);eo.has(n)||eo.set(n,new Set),eo.get(n).add({id:o,controller:t});let i={id:es().randomUUID(),execution_id:n,type:"synthesis_started",timestamp:new Date().toISOString(),data:{commentary:"Starting synthesis of all specialist outputs...",timestamp:new Date().toISOString(),directStreamUrl:`/api/orchestration/synthesis-stream-direct/${n}`}};t.enqueue(new TextEncoder().encode(`event: synthesis_started
data: ${JSON.stringify(i)}

`));try{let{data:o,error:i}=await s.from("orchestration_executions").select("*").eq("id",n).single();if(i||!o)throw Error(i?.message||"Execution not found");let{data:r,error:a}=await s.from("orchestration_steps").select("*").eq("execution_id",n).eq("status","completed").order("created_at",{ascending:!0});if(a||!r||0===r.length)throw Error(a?.message||"No completed steps found for execution");r.map((e,t)=>`## ${e.role_name||`Expert ${t+1}`} (${e.model_name||"Unknown Model"})

${e.output||"No output from this expert."}`).join("\n\n");let c=e.nextUrl.origin,l=`${c}/api/v1/chat/completions`,d=await fetch(l,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${process.env.ROKEY_API_ACCESS_TOKEN}`,"X-Synthesis-Execution-Id":n,"User-Agent":"RoKey/1.0 (Synthesis-Redirect)"},body:JSON.stringify({messages:[{role:"user",content:"synthesis_request"}],stream:!0,custom_api_config_id:"synthesis-internal"})});if(!d.ok)throw Error(`Synthesis request failed: ${d.status} ${d.statusText}`);if(d.body){let e=d.body.getReader(),n=new TextDecoder,s=new TextEncoder;try{for(;;){let{done:o,value:i}=await e.read();if(o)break;for(let e of n.decode(i,{stream:!0}).split("\n"))if(e.startsWith("data: ")&&!e.includes("[DONE]"))try{let n=e.substring(6),o=JSON.parse(n),i=`data: ${JSON.stringify(o)}

`;t.enqueue(s.encode(i))}catch(o){let n=`${e}

`;t.enqueue(s.encode(n))}else if(e.includes("[DONE]")){let e=`data: [DONE]

`;t.enqueue(s.encode(e))}}}finally{e.releaseLock()}}}catch(s){let e={id:es().randomUUID(),execution_id:n,type:"synthesis_error",timestamp:new Date().toISOString(),data:{error:s instanceof Error?s.message:"Unknown error during synthesis",timestamp:new Date().toISOString()}};t.enqueue(new TextEncoder().encode(`data: ${JSON.stringify(e)}

`))}finally{let e=eo.get(n);e&&(e.delete({id:o}),0===e.size&&eo.delete(n)),t.close()}},cancel(){}});return new Response(o,{headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","Access-Control-Allow-Origin":"*","X-Accel-Buffering":"no"}})}let er=new E.AppRouteRouteModule({definition:{kind:m.RouteKind.APP_ROUTE,page:"/api/orchestration/synthesis-stream-direct/[executionId]/route",pathname:"/api/orchestration/synthesis-stream-direct/[executionId]",filename:"route",bundlePath:"app/api/orchestration/synthesis-stream-direct/[executionId]/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\orchestration\\synthesis-stream-direct\\[executionId]\\route.ts",nextConfigOutput:"",userland:g}),{workAsyncStorage:ea,workUnitAsyncStorage:ec,serverHooks:el}=er;function ed(){return(0,C.patchFetch)({workAsyncStorage:ea,workUnitAsyncStorage:ec})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),s=t.X(0,[4447,580,9398,3410],()=>n(19902));module.exports=s})();
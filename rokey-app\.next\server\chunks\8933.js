exports.id=8933,exports.ids=[8933],exports.modules={2507:(e,t,i)=>{"use strict";i.d(t,{x:()=>a});var s=i(34386),o=i(44999);async function a(){let e=await (0,o.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,i,s){try{e.set({name:t,value:i,...s})}catch(e){}},remove(t,i){try{e.set({name:t,value:"",...i})}catch(e){}}}})}},5649:(e,t,i)=>{"use strict";i.d(t,{y:()=>o});var s=i(68811);class o{constructor(e,t){this.classificationApiKey=e,this.executionId=t}async validateStepOutput(e,t,i,o,a){let n=`As an AI orchestration moderator, evaluate this step output:

Original Request: "${o}"
Role: ${t}
Expected Outcome: ${a}
Actual Output: "${i}"

Evaluate:
1. Does the output fulfill the role's responsibility?
2. Is the quality sufficient for the next step?
3. Are there any issues or gaps?
4. Can we proceed to the next step?

Respond in JSON format:
{
  "isValid": true/false,
  "quality": 0.85,
  "issues": ["list of any issues"],
  "suggestions": ["list of improvements"],
  "canProceed": true/false,
  "reasoning": "detailed explanation"
}`;try{let i=await this.callModerator(n),o=JSON.parse(i);return await (0,s.Zi)(this.executionId,"moderator_commentary",{commentary:`Validating ${t} output: ${o.reasoning}`,validation:o},e,t),o}catch(e){return{isValid:i.length>50,quality:.7,issues:[],suggestions:[],canProceed:!0}}}async resolveConflicts(e,t){let i=`As an AI orchestration moderator, resolve conflicts between multiple AI outputs:

Original Request: "${t}"

Conflicting Outputs:
${e.map((e,t)=>`${t+1}. ${e.roleId} (confidence: ${e.confidence}): "${e.output}"`).join("\n")}

Determine the best approach:
1. Choose the best output
2. Combine elements from multiple outputs
3. Request modifications
4. Escalate for human review

Respond in JSON format:
{
  "action": "proceed|retry|escalate|modify|synthesize",
  "reasoning": "detailed explanation",
  "modifications": "specific changes needed (if action is modify)",
  "confidence": 0.85,
  "nextSteps": ["list of next actions"]
}`;try{let e=await this.callModerator(i),t=JSON.parse(e);return await (0,s.Zi)(this.executionId,"moderator_commentary",{commentary:`Conflict resolution: ${t.reasoning}`,decision:t}),t}catch(i){let t=e.reduce((e,t)=>t.confidence>e.confidence?t:e);return{action:"proceed",reasoning:`Selected ${t.roleId} output with highest confidence (${t.confidence})`,confidence:.6,nextSteps:["continue_with_selected_output"]}}}async synthesizeOutputs(e,t){await (0,s.Zi)(this.executionId,"synthesis_started",{commentary:"\uD83E\uDDE9 Beginning synthesis of all specialist outputs...",totalSteps:e.length});let i=`As an AI orchestration moderator, synthesize multiple specialist outputs into one cohesive response:

Original Request: "${t}"

Specialist Outputs:
${e.map(e=>`${e.stepNumber}. ${e.roleId} (quality: ${e.quality}): "${e.output}"`).join("\n\n")}

Create a comprehensive, well-structured response that:
1. Integrates all valuable insights
2. Maintains logical flow
3. Resolves any contradictions
4. Provides a complete answer to the original request

Respond in JSON format:
{
  "combinedOutput": "the synthesized response",
  "methodology": "how you combined the outputs",
  "qualityScore": 0.92,
  "conflictsResolved": ["list of conflicts resolved"],
  "improvements": ["how the synthesis improved upon individual outputs"]
}`;try{await (0,s.Zi)(this.executionId,"synthesis_progress",{commentary:"\uD83D\uDD04 Analyzing specialist contributions...",progress:.3});let e=await this.callModerator(i);await (0,s.Zi)(this.executionId,"synthesis_progress",{commentary:"\uD83C\uDFA8 Weaving outputs together...",progress:.7});let t=JSON.parse(e);return await (0,s.Zi)(this.executionId,"synthesis_progress",{commentary:"✨ Finalizing synthesized response...",progress:1}),t}catch(t){return{combinedOutput:e.map(e=>`**${e.roleId} Contribution:**
${e.output}`).join("\n\n"),methodology:"Simple concatenation due to synthesis error",qualityScore:.6,conflictsResolved:[],improvements:[]}}}generateLiveCommentary(e,t){let i={orchestration_started:["\uD83C\uDFAC Alright team, we've got an interesting challenge ahead!","\uD83D\uDE80 Let's break this down and see who's best suited for each part.","\uD83C\uDFAF Time to coordinate our AI specialists for optimal results."],task_decomposed:["\uD83D\uDCCB Task analysis complete! I've identified the perfect team composition.","\uD83C\uDFAA Perfect breakdown! Each specialist will handle their area of expertise.","⚡ Task decomposition successful! Ready to assign specialists."],step_assigned:[`📋 Assigning ${t.roleId} specialist to handle this part.`,`🎪 Our ${t.roleId} expert is stepping up to the plate!`,`⚡ Perfect match - ${t.roleId} is exactly what we need here.`],step_started:[`🎬 ${t.roleId} is diving deep into this challenge...`,`⚡ Watch ${t.roleId} work their specialized magic!`,`🎯 ${t.roleId} is laser-focused on delivering excellence.`],step_progress:[`🔥 ${t.roleId} is making great progress...`,`⚙️ The gears are turning smoothly in ${t.roleId}'s domain!`,`🌟 ${t.roleId} is crafting something special...`],step_streaming:[`📡 ${t.roleId} is streaming their thoughts in real-time...`,`⚡ Live updates from ${t.roleId} - watch the magic happen!`,`🌊 ${t.roleId} is flowing with brilliant insights...`],step_completed:[`✅ Excellent work from ${t.roleId}! Moving to the next phase.`,`🎉 ${t.roleId} delivered exactly what we needed. Handoff time!`,`💫 Beautiful execution by ${t.roleId}. The team is flowing perfectly.`],step_failed:[`⚠️ ${t.roleId} hit a snag, but we're adapting quickly!`,`🔄 Minor setback with ${t.roleId} - implementing recovery strategy.`,`🛠️ ${t.roleId} needs a different approach. Adjusting tactics...`],synthesis_started:["\uD83E\uDDE9 Time for the grand finale! I'm combining all these brilliant contributions...","\uD83C\uDFAD Watch as I orchestrate these individual masterpieces into one cohesive symphony!","\uD83C\uDF1F The magic happens now - weaving together all our specialists' expertise!"],synthesis_progress:["\uD83D\uDD04 Synthesis in progress - combining specialist outputs...","\uD83C\uDFA8 Weaving together the brilliant contributions...","⚙️ Processing and harmonizing all the expert insights..."],synthesis_streaming:["\uD83D\uDCE1 Streaming the synthesis process live...","\uD83C\uDF0A Watch the final result take shape in real-time...","⚡ Live synthesis - see how all pieces come together..."],synthesis_complete:["\uD83C\uDF8A Synthesis complete! All specialist outputs have been perfectly combined.","✨ The final masterpiece is ready! What an incredible team effort.","\uD83C\uDFC6 Synthesis successful! The AI team has delivered excellence."],orchestration_completed:["\uD83C\uDF89 What a performance! Our AI team delivered something truly remarkable.","✨ Mission accomplished! Each specialist played their part perfectly.","\uD83C\uDFC6 Outstanding collaboration - this is what AI teamwork looks like!"],orchestration_failed:["⚠️ The orchestration encountered issues, but we're learning from this.","\uD83D\uDD04 Orchestration failed - analyzing what went wrong for next time.","\uD83D\uDEE0️ Technical difficulties occurred - implementing improvements."],moderator_commentary:["\uD83C\uDF99️ Moderator providing guidance and coordination...","\uD83D\uDCCB Quality check and process optimization in progress...","\uD83C\uDFAF Ensuring optimal team coordination and output quality..."],specialist_message:[`💬 ${t.roleId} is sharing insights with the team...`,`🗣️ ${t.roleId} has something important to communicate...`,`📢 ${t.roleId} is contributing to the team discussion...`],moderator_assignment:[`🎯 Moderator assigning ${t.roleId} to the next task...`,`📋 Task delegation: ${t.roleId} is now taking the lead...`,`⚡ ${t.roleId} has been selected for this specialized work...`],specialist_acknowledgment:[`✅ ${t.roleId} acknowledges the assignment and is ready to proceed.`,`👍 ${t.roleId} confirms understanding and begins work.`,`🎯 ${t.roleId} is locked and loaded for this task.`],handoff_message:[`🤝 ${t.roleId} is handing off to the next specialist...`,`📤 ${t.roleId} has completed their part - passing the baton...`,`✨ ${t.roleId} finished beautifully - next specialist incoming...`],clarification_request:[`❓ ${t.roleId} needs clarification to deliver the best results...`,`🤔 ${t.roleId} is asking for more details to optimize their output...`,`💭 ${t.roleId} wants to ensure they understand the requirements perfectly...`],clarification_response:[`💡 Clarification provided - ${t.roleId} now has what they need!`,`✅ Question answered - ${t.roleId} can proceed with confidence.`,`🎯 All clear! ${t.roleId} has the details needed for success.`]}[e]||["\uD83E\uDD16 Processing..."];return i[Math.floor(Math.random()*i.length)]}async callModerator(e){let t=await fetch("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.classificationApiKey}`},body:JSON.stringify({model:"gemini-2.0-flash-lite",messages:[{role:"system",content:"You are an expert AI orchestration moderator. You coordinate multiple AI specialists, validate their work, resolve conflicts, and synthesize their outputs into cohesive results. Always respond in the requested JSON format."},{role:"user",content:e}],temperature:.2,max_tokens:2e3,response_format:{type:"json_object"}})});if(!t.ok)throw Error(`Moderator API error: ${t.status}`);let i=await t.json(),s=i.choices?.[0]?.message?.content;if(!s)throw Error("Empty moderator response");return s}async analyzeParallelizationOpportunities(e){let t=`Analyze these orchestration steps for parallelization opportunities:

Steps:
${e.map(e=>`${e.stepNumber}. ${e.roleId}: "${e.prompt}" (depends on: ${e.dependencies.join(", ")||"none"})`).join("\n")}

Determine:
1. Which steps can run in parallel
2. Optimal grouping strategy
3. Expected performance improvement

Respond in JSON format:
{
  "parallelGroups": [[1], [2, 3], [4]],
  "reasoning": "explanation of grouping strategy",
  "estimatedSpeedup": 1.8
}`;try{let e=await this.callModerator(t);return JSON.parse(e)}catch(t){return{parallelGroups:e.map(e=>[e.stepNumber]),reasoning:"Sequential execution due to analysis error",estimatedSpeedup:1}}}}},39727:()=>{},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},68811:(e,t,i)=>{"use strict";i.d(t,{Wu:()=>a,ZZ:()=>n,Zi:()=>r,re:()=>l,tl:()=>o});let s=new Map;function o(e,t){let i=s.get(e);if(i)try{let e=new TextEncoder,s=`id: ${t.id}
event: ${t.type}
data: ${JSON.stringify(t)}

`;i.controller.enqueue(e.encode(s)),i.lastEventId=t.id}catch(t){s.delete(e)}}function a(e,t){s.set(e,{controller:t,lastEventId:"",startTime:Date.now()})}function n(e){s.delete(e)}async function r(e,t,i,s,a,n){let r={id:crypto.randomUUID(),execution_id:e,type:t,timestamp:new Date().toISOString(),data:i,step_number:s,role_id:a,model_name:n};o(e,r)}function l(e,t,i){let s={orchestration_started:["\uD83C\uDFAC Welcome to the AI Team Collaboration! I'm assembling the perfect team for this task.","\uD83D\uDE80 Alright everyone, we've got an exciting challenge ahead. Let me coordinate our specialists.","\uD83C\uDFAF Time to bring together our expert AI team for optimal results!"],task_decomposed:["\uD83D\uDCCB I've analyzed the task and assembled this expert team. Let's begin the collaboration!","\uD83C\uDFAA Perfect! I've matched the right specialists to each part of this challenge.","⚡ Task breakdown complete. Our team is perfectly positioned for success."],moderator_assignment:[`🎯 @${t}, you're up! Please begin your specialized work on this task.`,`📋 @${t}, this is right in your wheelhouse. Take it away!`,`⚡ @${t}, I need your expertise here. Please work your magic.`],specialist_acknowledgment:[`✅ Understood! I'm ${t} and I'll handle this task with expertise. Starting work now...`,`🎯 Perfect! As the ${t} specialist, I'm excited to tackle this challenge.`,`⚡ Got it! ${t} here - I'll deliver excellent results for the team.`],specialist_message:[`🎉 Excellent! I've completed my part of the task. Here's what I've delivered:`,`✨ Perfect! My specialized work is complete. Take a look at the results:`,`🚀 Mission accomplished! Here's my contribution to the team effort:`],handoff_message:[`✨ Excellent work, @${t}! Quality looks great. Now passing to the next specialist...`,`👏 Outstanding execution, @${t}! Moving to the next phase...`,`🎉 Fantastic results, @${t}! The team collaboration continues...`],synthesis_started:["\uD83E\uDDE9 Fantastic teamwork everyone! Now I'll synthesize all your excellent contributions into the final deliverable...","\uD83C\uDFA8 Amazing collaboration! Time to weave all these brilliant outputs together...","✨ Outstanding work team! Let me combine everything into the perfect final result..."],synthesis_complete:["\uD83C\uDF8A Mission accomplished! The team has delivered an outstanding result. Great collaboration everyone!","\uD83C\uDFC6 Incredible teamwork! We've created something truly exceptional together.","\uD83C\uDF1F Perfect execution! This is what happens when AI specialists collaborate brilliantly."]}[e]||["\uD83E\uDD16 Processing..."],o=s[Math.floor(Math.random()*s.length)];return i&&"string"==typeof o?o.replace(/\{(\w+)\}/g,(e,t)=>i[t]||e):o}},78335:()=>{},96487:()=>{}};
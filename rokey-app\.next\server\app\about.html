<!DOCTYPE html><html lang="en" class="__variable_e8ce0c"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" as="image" href="/founder.jpg"/><link rel="stylesheet" href="/_next/static/css/5b576904c612405e.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/7e9d481278944699.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/a7273117cf35847d.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-d415e51dee77ec61.js"/><script src="/_next/static/chunks/4288-23967f16b671a2b6.js" async=""></script><script src="/_next/static/chunks/7706-f0a1d6edeb670fa6.js" async=""></script><script src="/_next/static/chunks/7544-7d0b0329d07ac6b4.js" async=""></script><script src="/_next/static/chunks/1142-f9ee7a70a030832b.js" async=""></script><script src="/_next/static/chunks/2993-a0d504d418f67aae.js" async=""></script><script src="/_next/static/chunks/1561-24fb77a7b1f02280.js" async=""></script><script src="/_next/static/chunks/9248-1dff9e75e458a392.js" async=""></script><script src="/_next/static/chunks/5495-1f609c9411b31b34.js" async=""></script><script src="/_next/static/chunks/main-app-0c7375378d79355a.js" async=""></script><script src="/_next/static/chunks/supabase-55776fae-704411a5287a30d1.js" async=""></script><script src="/_next/static/chunks/supabase-0d08456b-4fc32f92c04ec1b2.js" async=""></script><script src="/_next/static/chunks/utils-c4eb776e0b24b75f.js" async=""></script><script src="/_next/static/chunks/ui-components-b80f15b3-9a6baaa41a21085e.js" async=""></script><script src="/_next/static/chunks/ui-components-3acb5f41-7937a2d67577e49e.js" async=""></script><script src="/_next/static/chunks/vendors-ad6a2f20-151437972c427992.js" async=""></script><script src="/_next/static/chunks/vendors-04fef8b0-3e093bedd3bee402.js" async=""></script><script src="/_next/static/chunks/vendors-7ec938a2-7c7822bd481d7a06.js" async=""></script><script src="/_next/static/chunks/vendors-2ced652b-3ea62d9ae6eb7452.js" async=""></script><script src="/_next/static/chunks/vendors-f33ddaf2-cae9233b375d1344.js" async=""></script><script src="/_next/static/chunks/vendors-89d5c698-b2c6bc4e56bbeabf.js" async=""></script><script src="/_next/static/chunks/app/layout-72721dca14de4144.js" async=""></script><script src="/_next/static/chunks/landing-components-3f5b97f1-a2412c3ab155be84.js" async=""></script><script src="/_next/static/chunks/landing-components-34b81939-1c290974ce3943a1.js" async=""></script><script src="/_next/static/chunks/app/about/page-b18d6d839278e2c8.js" async=""></script><link rel="preload" href="/api/custom-configs" as="fetch" crossorigin="anonymous"/><link rel="preload" href="/api/system-status" as="fetch" crossorigin="anonymous"/><link rel="dns-prefetch" href="//fonts.googleapis.com"/><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin=""/><link rel="prefetch" href="/dashboard"/><link rel="prefetch" href="/playground"/><link rel="prefetch" href="/logs"/><link rel="prefetch" href="/my-models"/><title>RoKey - Smart LLM Key Router</title><meta name="description" content="Advanced LLM API key routing and management"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="font-sans antialiased"><!--$!--><template data-dgst="BAILOUT_TO_CLIENT_SIDE_RENDERING"></template><!--/$--><div class="min-h-screen bg-white"><nav class="fixed top-0 left-0 right-0 z-50 bg-black/90 backdrop-blur-md border-b border-gray-700"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex justify-between items-center h-16"><div class="flex items-center"><a class="flex items-center space-x-2" href="/"><div class="w-8 h-8 bg-white rounded-lg flex items-center justify-center p-0.5"><img alt="RouKey" loading="lazy" width="28" height="28" decoding="async" data-nimg="1" class="object-cover" style="color:transparent" srcSet="/_next/image?url=%2Froukey_logo.png&amp;w=32&amp;q=75 1x, /_next/image?url=%2Froukey_logo.png&amp;w=64&amp;q=75 2x" src="/_next/image?url=%2Froukey_logo.png&amp;w=64&amp;q=75"/></div><span class="text-xl font-bold text-white">RouKey</span></a></div><div class="hidden md:flex items-center space-x-8"><a class="text-gray-300 hover:text-white transition-colors duration-100" href="/features">Features</a><a class="text-gray-300 hover:text-white transition-colors duration-100" href="/pricing">Pricing</a><a class="text-gray-300 hover:text-white transition-colors duration-100" href="/about">About</a><a class="text-gray-300 hover:text-white transition-colors duration-100" href="/auth/signin">Sign In</a><a class="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-6 py-2 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-100 font-semibold" href="/pricing">Get Started</a></div><div class="md:hidden"><button class="text-gray-300 hover:text-white transition-colors"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon" class="h-6 w-6"><path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"></path></svg></button></div></div></div></nav><main class="pt-20"><section class="py-20 bg-gradient-to-br from-slate-50 to-blue-50"><div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center"><div style="opacity:0;transform:translateY(15px)"><h1 class="text-5xl md:text-6xl font-bold text-gray-900 mb-6">From Frustration to<span class="text-[#ff6b35] block">Innovation</span></h1><p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8">The story of how one developer&#x27;s late-night coding sessions and mounting API bills led to building the world&#x27;s most intelligent AI routing platform - completely solo.</p></div></div></section><section class="py-16"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="grid grid-cols-2 md:grid-cols-4 gap-8"><div class="text-center" style="opacity:0;transform:translateY(15px)"><div class="text-4xl md:text-5xl font-bold text-[#ff6b35] mb-2">300+</div><div class="text-gray-600 font-medium">AI Models Supported</div></div><div class="text-center" style="opacity:0;transform:translateY(15px)"><div class="text-4xl md:text-5xl font-bold text-[#ff6b35] mb-2">10M+</div><div class="text-gray-600 font-medium">API Requests Processed</div></div><div class="text-center" style="opacity:0;transform:translateY(15px)"><div class="text-4xl md:text-5xl font-bold text-[#ff6b35] mb-2">5,000+</div><div class="text-gray-600 font-medium">Developers Trust Us</div></div><div class="text-center" style="opacity:0;transform:translateY(15px)"><div class="text-4xl md:text-5xl font-bold text-[#ff6b35] mb-2">99.9%</div><div class="text-gray-600 font-medium">Uptime Guarantee</div></div></div></div></section><section class="py-20 bg-gray-50"><div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16" style="opacity:0;transform:translateY(15px)"><h2 class="text-4xl font-bold text-gray-900 mb-6">The Breaking Point</h2><p class="text-xl text-gray-600">Every developer has that moment when they decide to build the tool they wish existed. This is mine.</p></div><div class="prose prose-lg mx-auto text-gray-700" style="opacity:0;transform:translateY(15px)"><p><strong>It was 2 AM.</strong> I was deep into developing a game that heavily relied on AI for procedural content generation. My API bills were skyrocketing, I&#x27;d hit rate limits on three different providers, and the &quot;intelligent&quot; routing tool I was paying for had just routed my simple chat request to GPT-4 Turbo for the hundredth time that day.</p><p><strong>That&#x27;s when it hit me.</strong> I&#x27;m Okoro David Chukwunyerem, and I&#x27;ve been building games and apps for years. I know how to solve complex routing problems. Why was I trusting someone else&#x27;s broken solution when I could build something that actually works?</p><p><strong>The eureka moment:</strong> While experimenting with different providers, I discovered that by intelligently routing between multiple free trial API keys, I could get essentially unlimited usage for testing. No more $500 monthly bills for development work. No more hitting rate limits mid-sprint.</p><p><strong>Six months later,</strong> what started as a weekend hack to solve my own problems had evolved into RouKey - a platform that gives any developer unlimited access to 300+ AI models with truly intelligent routing. Built entirely solo, tested in the trenches of real development work.</p><p class="text-[#ff6b35] font-semibold text-lg border-l-4 border-[#ff6b35] pl-6 italic">&quot;I built RouKey because I was tired of choosing between going broke or building slowly. Now thousands of developers can test fearlessly and build faster than ever before.&quot;</p></div></div></section><section class="py-20"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16" style="opacity:0;transform:translateY(15px)"><h2 class="text-4xl font-bold text-gray-900 mb-6">My Values</h2><p class="text-xl text-gray-600 max-w-3xl mx-auto">These principles guide how I built RouKey and how I continue to develop it.</p></div><div class="grid grid-cols-1 md:grid-cols-2 gap-12"><div class="flex items-start space-x-4" style="opacity:0;transform:translateY(15px)"><div class="flex-shrink-0"><div class="w-12 h-12 bg-[#ff6b35] rounded-xl flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon" class="w-6 h-6 text-white"><path stroke-linecap="round" stroke-linejoin="round" d="m3.75 13.5 10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75Z"></path></svg></div></div><div><h3 class="text-xl font-semibold text-gray-900 mb-3">Performance First</h3><p class="text-gray-600">We obsess over speed, reliability, and efficiency in everything we build.</p></div></div><div class="flex items-start space-x-4" style="opacity:0;transform:translateY(15px)"><div class="flex-shrink-0"><div class="w-12 h-12 bg-[#ff6b35] rounded-xl flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon" class="w-6 h-6 text-white"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"></path></svg></div></div><div><h3 class="text-xl font-semibold text-gray-900 mb-3">Security by Design</h3><p class="text-gray-600">Enterprise-grade security isn&amp;apos;t an afterthought—it&amp;apos;s built into our foundation.</p></div></div><div class="flex items-start space-x-4" style="opacity:0;transform:translateY(15px)"><div class="flex-shrink-0"><div class="w-12 h-12 bg-[#ff6b35] rounded-xl flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon" class="w-6 h-6 text-white"><path stroke-linecap="round" stroke-linejoin="round" d="M12 18v-5.25m0 0a6.01 6.01 0 0 0 1.5-.189m-1.5.189a6.01 6.01 0 0 1-1.5-.189m3.75 7.478a12.06 12.06 0 0 1-4.5 0m3.75 2.383a14.406 14.406 0 0 1-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 1 0-7.517 0c.85.493 1.509 1.333 1.509 2.316V18"></path></svg></div></div><div><h3 class="text-xl font-semibold text-gray-900 mb-3">Innovation Driven</h3><p class="text-gray-600">We&amp;apos;re constantly pushing the boundaries of what&amp;apos;s possible with AI routing.</p></div></div><div class="flex items-start space-x-4" style="opacity:0;transform:translateY(15px)"><div class="flex-shrink-0"><div class="w-12 h-12 bg-[#ff6b35] rounded-xl flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon" class="w-6 h-6 text-white"><path stroke-linecap="round" stroke-linejoin="round" d="M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"></path></svg></div></div><div><h3 class="text-xl font-semibold text-gray-900 mb-3">Solo Built</h3><p class="text-gray-600">Built entirely by one developer who faced these exact problems and solved them.</p></div></div></div></div></section><section class="py-32 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden"><div class="absolute inset-0 opacity-5"><div class="absolute inset-0" style="background-image:linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),
                  linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px);background-size:60px 60px"></div></div><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative"><div class="text-center mb-20" style="opacity:0;transform:translateY(15px)"><h2 class="text-5xl md:text-6xl font-bold text-gray-900 mb-6">Meet the<span class="text-[#ff6b35] block">Solo Founder</span></h2><p class="text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">One developer&#x27;s journey from frustration to building the ultimate AI routing platform</p></div><div class="bg-white rounded-3xl shadow-2xl overflow-hidden max-w-6xl mx-auto" style="opacity:0;transform:translateY(20px)"><div class="grid grid-cols-1 lg:grid-cols-2 gap-0"><div class="relative bg-gradient-to-br from-[#ff6b35] to-[#f7931e] p-8 flex items-center justify-center min-h-[700px]"><div class="relative"><div class="w-[500px] h-[500px] rounded-3xl overflow-hidden shadow-2xl border-4 border-white/20"><img src="/founder.jpg" alt="Okoro David Chukwunyerem" class="w-full h-full object-cover"/></div><div class="absolute -top-6 -left-6 bg-white rounded-2xl px-4 py-2 shadow-xl transform -rotate-12 hover:rotate-0 transition-transform duration-300"><div class="text-[#ff6b35] font-bold text-sm">Solo Built</div></div><div class="absolute -bottom-6 -right-6 bg-white rounded-2xl px-4 py-2 shadow-xl transform rotate-12 hover:rotate-0 transition-transform duration-300"><div class="text-[#ff6b35] font-bold text-sm">300+ Models</div></div><div class="absolute top-1/2 -right-8 bg-white rounded-2xl px-4 py-2 shadow-xl transform rotate-6 hover:rotate-0 transition-transform duration-300"><div class="text-[#ff6b35] font-bold text-sm">Game Dev</div></div><div class="absolute bottom-1/4 -left-8 bg-white rounded-2xl px-4 py-2 shadow-xl transform -rotate-6 hover:rotate-0 transition-transform duration-300"><div class="text-[#ff6b35] font-bold text-sm">∞ Requests</div></div></div></div><div class="p-12 flex flex-col justify-center"><div class="mb-8"><h3 class="text-4xl font-bold text-gray-900 mb-3">Okoro David Chukwunyerem</h3><p class="text-2xl text-[#ff6b35] font-semibold mb-6">Founder &amp; Developer</p><div class="w-20 h-1 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-full mb-8"></div></div><div class="space-y-6 text-lg text-gray-700 leading-relaxed"><p><strong class="text-gray-900">Game developer turned AI infrastructure pioneer.</strong>After countless nights wrestling with broken routing tools and mounting API bills, I decided to build the solution I wished existed.</p><p><strong class="text-gray-900">The breakthrough:</strong> While developing games that heavily used AI, I discovered that intelligent routing between multiple free trial API keys could give me essentially unlimited testing access. No more $500 monthly bills for development work.</p><div class="bg-gray-50 rounded-2xl p-6 border-l-4 border-[#ff6b35]"><p class="text-[#ff6b35] font-semibold italic">&quot;I built RouKey because I was tired of choosing between going broke or building slowly. Six months later, thousands of developers are testing fearlessly and building faster than ever.&quot;</p></div><p><strong class="text-gray-900">Every line of code</strong> in RouKey was written by me. Every feature was born from a real problem I faced. That&#x27;s why it actually works.</p></div><div class="grid grid-cols-3 gap-6 mt-10 pt-8 border-t border-gray-200"><div class="text-center group"><div class="text-3xl font-bold text-[#ff6b35] mb-2 group-hover:scale-110 transition-transform duration-300">1</div><div class="text-gray-600 text-sm">Solo Developer</div></div><div class="text-center group"><div class="text-3xl font-bold text-[#ff6b35] mb-2 group-hover:scale-110 transition-transform duration-300">300+</div><div class="text-gray-600 text-sm">AI Models</div></div><div class="text-center group"><div class="text-3xl font-bold text-[#ff6b35] mb-2 group-hover:scale-110 transition-transform duration-300">∞</div><div class="text-gray-600 text-sm">API Requests</div></div></div></div></div></div></div></section><section class="py-32 bg-gradient-to-br from-gray-900 via-black to-gray-900 relative overflow-hidden"><div class="absolute inset-0"><div class="absolute top-20 left-10 w-96 h-96 bg-[#ff6b35] rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse"></div><div class="absolute bottom-20 right-10 w-96 h-96 bg-[#f7931e] rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse" style="animation-delay:2s"></div></div><div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative"><div style="opacity:0;transform:translateY(15px)"><h2 class="text-5xl md:text-6xl font-bold text-white mb-8">Ready to Build<span class="text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e] block">Without Limits?</span></h2><p class="text-2xl text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed">Join thousands of developers who&#x27;ve discovered the secret to unlimited AI testing. Built by a developer who faced your exact problems.</p><div class="flex flex-col sm:flex-row gap-6 justify-center mb-16"><a href="/auth/signup"><div class="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-12 py-5 rounded-2xl font-bold text-xl hover:shadow-2xl transition-all duration-200 shadow-lg cursor-pointer" tabindex="0">Start Building Now</div></a><a href="/pricing"><div class="bg-white/10 backdrop-blur-sm text-white px-12 py-5 rounded-2xl font-bold text-xl border-2 border-white/20 hover:border-white/40 hover:bg-white/20 transition-all duration-200 cursor-pointer" tabindex="0">View Pricing</div></a></div><div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"><div class="text-center"><div class="text-4xl font-bold text-[#ff6b35] mb-2">∞</div><div class="text-gray-400">API Requests</div><div class="text-gray-500 text-sm">No limits, ever</div></div><div class="text-center"><div class="text-4xl font-bold text-[#ff6b35] mb-2">300+</div><div class="text-gray-400">AI Models</div><div class="text-gray-500 text-sm">All providers</div></div><div class="text-center"><div class="text-4xl font-bold text-[#ff6b35] mb-2">$0</div><div class="text-gray-400">Overage Fees</div><div class="text-gray-500 text-sm">Pay only your API costs</div></div></div></div></div></section></main><footer class="bg-black text-white"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16"><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8"><div class="lg:col-span-2"><a class="flex items-center space-x-2 mb-4" href="/"><div class="w-8 h-8 bg-white rounded-lg flex items-center justify-center p-0.5"><img alt="RouKey" loading="lazy" width="28" height="28" decoding="async" data-nimg="1" class="object-cover" style="color:transparent" srcSet="/_next/image?url=%2Froukey_logo.png&amp;w=32&amp;q=75 1x, /_next/image?url=%2Froukey_logo.png&amp;w=64&amp;q=75 2x" src="/_next/image?url=%2Froukey_logo.png&amp;w=64&amp;q=75"/></div><span class="text-xl font-bold">RouKey</span></a><p class="text-gray-400 mb-6 max-w-md">Intelligent AI model routing platform that helps developers optimize their AI infrastructure with automatic failover, cost tracking, and comprehensive analytics.</p><div class="flex space-x-4"><a href="#" class="text-gray-400 hover:text-white transition-colors"><span class="sr-only">Twitter</span><svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24"><path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path></svg></a><a href="#" class="text-gray-400 hover:text-white transition-colors"><span class="sr-only">GitHub</span><svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24"><path fill-rule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clip-rule="evenodd"></path></svg></a><a href="#" class="text-gray-400 hover:text-white transition-colors"><span class="sr-only">LinkedIn</span><svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24"><path fill-rule="evenodd" d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" clip-rule="evenodd"></path></svg></a></div></div><div><h3 class="text-sm font-semibold text-gray-300 tracking-wider uppercase mb-4">Product</h3><ul class="space-y-3"><li><a class="text-gray-400 hover:text-white transition-colors" href="#features">Features</a></li><li><a class="text-gray-400 hover:text-white transition-colors" href="#pricing">Pricing</a></li><li><a class="text-gray-400 hover:text-white transition-colors" href="/docs">API Documentation</a></li><li><a class="text-gray-400 hover:text-white transition-colors" href="/status">Status</a></li></ul></div><div><h3 class="text-sm font-semibold text-gray-300 tracking-wider uppercase mb-4">Company</h3><ul class="space-y-3"><li><a class="text-gray-400 hover:text-white transition-colors" href="/about">About</a></li><li><a class="text-gray-400 hover:text-white transition-colors" href="/blog">Blog</a></li><li><a class="text-gray-400 hover:text-white transition-colors" href="/careers">Careers</a></li><li><a class="text-gray-400 hover:text-white transition-colors" href="/contact">Contact</a></li></ul></div><div><h3 class="text-sm font-semibold text-gray-300 tracking-wider uppercase mb-4">Resources</h3><ul class="space-y-3"><li><a class="text-gray-400 hover:text-white transition-colors" href="/help">Help Center</a></li><li><a class="text-gray-400 hover:text-white transition-colors" href="/community">Community</a></li><li><a class="text-gray-400 hover:text-white transition-colors" href="/guides">Guides</a></li><li><a class="text-gray-400 hover:text-white transition-colors" href="/changelog">Changelog</a></li></ul></div><div><h3 class="text-sm font-semibold text-gray-300 tracking-wider uppercase mb-4">Legal</h3><ul class="space-y-3"><li><a class="text-gray-400 hover:text-white transition-colors" href="/privacy">Privacy Policy</a></li><li><a class="text-gray-400 hover:text-white transition-colors" href="/terms">Terms of Service</a></li><li><a class="text-gray-400 hover:text-white transition-colors" href="/cookies">Cookie Policy</a></li><li><a class="text-gray-400 hover:text-white transition-colors" href="/security">Security</a></li></ul></div></div><div class="mt-12 pt-8 border-t border-gray-700 flex flex-col md:flex-row justify-between items-center"><p class="text-gray-400 text-sm">© 2025 RouKey. All rights reserved.</p><div class="mt-4 md:mt-0 flex items-center space-x-6"><span class="text-gray-400 text-sm">Made with ❤️ for developers</span></div></div></div></footer></div><!--$--><!--/$--><!--$--><!--/$--><!--$!--><template data-dgst="BAILOUT_TO_CLIENT_SIDE_RENDERING"></template><!--/$--><script src="/_next/static/chunks/webpack-d415e51dee77ec61.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:\"$Sreact.suspense\"\n3:I[99030,[\"8888\",\"static/chunks/supabase-55776fae-704411a5287a30d1.js\",\"1459\",\"static/chunks/supabase-0d08456b-4fc32f92c04ec1b2.js\",\"5738\",\"static/chunks/utils-c4eb776e0b24b75f.js\",\"9968\",\"static/chunks/ui-components-b80f15b3-9a6baaa41a21085e.js\",\"6060\",\"static/chunks/ui-components-3acb5f41-7937a2d67577e49e.js\",\"563\",\"static/chunks/vendors-ad6a2f20-151437972c427992.js\",\"2662\",\"static/chunks/vendors-04fef8b0-3e093bedd3bee402.js\",\"8669\",\"static/chunks/vendors-7ec938a2-7c7822bd481d7a06.js\",\"8848\",\"static/chunks/vendors-2ced652b-3ea62d9ae6eb7452.js\",\"4696\",\"static/chunks/vendors-f33ddaf2-cae9233b375d1344.js\",\"9173\",\"static/chunks/vendors-89d5c698-b2c6bc4e56bbeabf.js\",\"9558\",\"static/chunks/app/layout-72721dca14de4144.js\"],\"default\"]\n4:I[52469,[\"8888\",\"static/chunks/supabase-55776fae-704411a5287a30d1.js\",\"1459\",\"static/chunks/supabase-0d08456b-4fc32f92c04ec1b2.js\",\"5738\",\"static/chunks/utils-c4eb776e0b24b75f.js\",\"9968\",\"static/chunks/ui-components-b80f15b3-9a6baaa41a21085e.js\",\"6060\",\"static/chunks/ui-components-3acb5f41-7937a2d67577e49e.js\",\"563\",\"static/chunks/vendors-ad6a2f20-151437972c427992.js\",\"2662\",\"static/chunks/vendors-04fef8b0-3e093bedd3bee402.js\",\"8669\",\"static/chunks/vendors-7ec938a2-7c7822bd481d7a06.js\",\"8848\",\"static/chunks/vendors-2ced652b-3ea62d9ae6eb7452.js\",\"4696\",\"static/chunks/vendors-f33ddaf2-cae9233b375d1344.js\",\"9173\",\"static/chunks/vendors-89d5c698-b2c6bc4e56bbeabf.js\",\"9558\",\"static/chunks/app/layout-72721dca14de4144.js\"],\"default\"]\n5:I[38050,[\"8888\",\"static/chunks/supabase-55776fae-704411a5287a30d1.js\",\"1459\",\"static/chunks/supabase-0d08456b-4fc32f92c04ec1b2.js\",\"5738\",\"static/chunks/utils-c4eb776e0b24b75f.js\",\"9968\",\"static/chunks/ui-components-b80f15b3-9a6baaa41a21085e.js\",\"6060\",\"static/chunks/ui-components-3acb5f41-7937a2d67577e49e.js\",\"563\",\"static/chunks/vendors-ad6a2f20-151437972c427992.js\",\"2662\",\"static/chunks/vendors-04fef8b0-3e093bedd3bee402.js\",\"8669\",\"static/chunks/vendors-7ec938a2-7c7822bd481d7a06.js\",\"8848\",\"static/chunks/vendors-2ced6"])</script><script>self.__next_f.push([1,"52b-3ea62d9ae6eb7452.js\",\"4696\",\"static/chunks/vendors-f33ddaf2-cae9233b375d1344.js\",\"9173\",\"static/chunks/vendors-89d5c698-b2c6bc4e56bbeabf.js\",\"9558\",\"static/chunks/app/layout-72721dca14de4144.js\"],\"default\"]\n6:I[83434,[\"8888\",\"static/chunks/supabase-55776fae-704411a5287a30d1.js\",\"1459\",\"static/chunks/supabase-0d08456b-4fc32f92c04ec1b2.js\",\"5738\",\"static/chunks/utils-c4eb776e0b24b75f.js\",\"9968\",\"static/chunks/ui-components-b80f15b3-9a6baaa41a21085e.js\",\"6060\",\"static/chunks/ui-components-3acb5f41-7937a2d67577e49e.js\",\"563\",\"static/chunks/vendors-ad6a2f20-151437972c427992.js\",\"2662\",\"static/chunks/vendors-04fef8b0-3e093bedd3bee402.js\",\"8669\",\"static/chunks/vendors-7ec938a2-7c7822bd481d7a06.js\",\"8848\",\"static/chunks/vendors-2ced652b-3ea62d9ae6eb7452.js\",\"4696\",\"static/chunks/vendors-f33ddaf2-cae9233b375d1344.js\",\"9173\",\"static/chunks/vendors-89d5c698-b2c6bc4e56bbeabf.js\",\"9558\",\"static/chunks/app/layout-72721dca14de4144.js\"],\"default\"]\n7:I[87555,[],\"\"]\n8:I[31295,[],\"\"]\n9:I[48031,[\"8888\",\"static/chunks/supabase-55776fae-704411a5287a30d1.js\",\"1459\",\"static/chunks/supabase-0d08456b-4fc32f92c04ec1b2.js\",\"5738\",\"static/chunks/utils-c4eb776e0b24b75f.js\",\"9968\",\"static/chunks/ui-components-b80f15b3-9a6baaa41a21085e.js\",\"6060\",\"static/chunks/ui-components-3acb5f41-7937a2d67577e49e.js\",\"563\",\"static/chunks/vendors-ad6a2f20-151437972c427992.js\",\"2662\",\"static/chunks/vendors-04fef8b0-3e093bedd3bee402.js\",\"8669\",\"static/chunks/vendors-7ec938a2-7c7822bd481d7a06.js\",\"8848\",\"static/chunks/vendors-2ced652b-3ea62d9ae6eb7452.js\",\"4696\",\"static/chunks/vendors-f33ddaf2-cae9233b375d1344.js\",\"9173\",\"static/chunks/vendors-89d5c698-b2c6bc4e56bbeabf.js\",\"9558\",\"static/chunks/app/layout-72721dca14de4144.js\"],\"SpeedInsights\"]\na:I[69243,[\"8888\",\"static/chunks/supabase-55776fae-704411a5287a30d1.js\",\"1459\",\"static/chunks/supabase-0d08456b-4fc32f92c04ec1b2.js\",\"5738\",\"static/chunks/utils-c4eb776e0b24b75f.js\",\"9968\",\"static/chunks/ui-components-b80f15b3-9a6baaa41a21085e.js\",\"6060\",\"static/chunks/ui-components-3acb5f41-7937a2d67577e49e.js\",\"563\""])</script><script>self.__next_f.push([1,",\"static/chunks/vendors-ad6a2f20-151437972c427992.js\",\"2662\",\"static/chunks/vendors-04fef8b0-3e093bedd3bee402.js\",\"8669\",\"static/chunks/vendors-7ec938a2-7c7822bd481d7a06.js\",\"8848\",\"static/chunks/vendors-2ced652b-3ea62d9ae6eb7452.js\",\"4696\",\"static/chunks/vendors-f33ddaf2-cae9233b375d1344.js\",\"9173\",\"static/chunks/vendors-89d5c698-b2c6bc4e56bbeabf.js\",\"9558\",\"static/chunks/app/layout-72721dca14de4144.js\"],\"\"]\nc:I[90894,[],\"ClientPageRoot\"]\nd:I[26625,[\"7871\",\"static/chunks/landing-components-3f5b97f1-a2412c3ab155be84.js\",\"2115\",\"static/chunks/landing-components-34b81939-1c290974ce3943a1.js\",\"5738\",\"static/chunks/utils-c4eb776e0b24b75f.js\",\"9968\",\"static/chunks/ui-components-b80f15b3-9a6baaa41a21085e.js\",\"6060\",\"static/chunks/ui-components-3acb5f41-7937a2d67577e49e.js\",\"563\",\"static/chunks/vendors-ad6a2f20-151437972c427992.js\",\"2662\",\"static/chunks/vendors-04fef8b0-3e093bedd3bee402.js\",\"8669\",\"static/chunks/vendors-7ec938a2-7c7822bd481d7a06.js\",\"8848\",\"static/chunks/vendors-2ced652b-3ea62d9ae6eb7452.js\",\"4696\",\"static/chunks/vendors-f33ddaf2-cae9233b375d1344.js\",\"9173\",\"static/chunks/vendors-89d5c698-b2c6bc4e56bbeabf.js\",\"7220\",\"static/chunks/app/about/page-b18d6d839278e2c8.js\"],\"default\"]\n10:I[59665,[],\"MetadataBoundary\"]\n12:I[59665,[],\"OutletBoundary\"]\n15:I[74911,[],\"AsyncMetadataOutlet\"]\n17:I[59665,[],\"ViewportBoundary\"]\n19:I[26614,[],\"\"]\n:HL[\"/_next/static/css/5b576904c612405e.css\",\"style\"]\n:HL[\"/_next/static/css/7e9d481278944699.css\",\"style\"]\n:HL[\"/_next/static/css/a7273117cf35847d.css\",\"style\"]\nb:T569,\n            if ('serviceWorker' in navigator) {\n              window.addEventListener('load', function() {\n                navigator.serviceWorker.register('/sw.js')\n                  .then(function(registration) {\n                    console.log('✅ Service Worker registered successfully');\n\n                    // Preload critical data after SW is ready\n                    if (window.location.pathname === '/') {\n                      // Preload landing page data\n                      fetch('/api/system-status"])</script><script>self.__next_f.push([1,"').catch(() =\u003e {});\n\n                      // Prefetch all critical pages immediately\n                      setTimeout(() =\u003e {\n                        const criticalPages = ['/features', '/pricing', '/about', '/auth/signin', '/auth/signup'];\n                        criticalPages.forEach(page =\u003e {\n                          const link = document.createElement('link');\n                          link.rel = 'prefetch';\n                          link.href = page;\n                          document.head.appendChild(link);\n                        });\n                      }, 500); // Much faster prefetching\n                    }\n                  })\n                  .catch(function(registrationError) {\n                    console.warn('⚠️ Service Worker registration failed:', registrationError);\n                  });\n              });\n            }\n          "])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"mbzCpuQDsbXUFPmmv-y9w\",\"p\":\"\",\"c\":[\"\",\"about\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"about\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/5b576904c612405e.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/7e9d481278944699.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"2\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/a7273117cf35847d.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"className\":\"__variable_e8ce0c\",\"children\":[[\"$\",\"head\",null,{\"children\":[[\"$\",\"link\",null,{\"rel\":\"preload\",\"href\":\"/api/custom-configs\",\"as\":\"fetch\",\"crossOrigin\":\"anonymous\"}],[\"$\",\"link\",null,{\"rel\":\"preload\",\"href\":\"/api/system-status\",\"as\":\"fetch\",\"crossOrigin\":\"anonymous\"}],[\"$\",\"link\",null,{\"rel\":\"dns-prefetch\",\"href\":\"//fonts.googleapis.com\"}],[\"$\",\"link\",null,{\"rel\":\"preconnect\",\"href\":\"https://fonts.gstatic.com\",\"crossOrigin\":\"\"}],[\"$\",\"link\",null,{\"rel\":\"prefetch\",\"href\":\"/dashboard\"}],[\"$\",\"link\",null,{\"rel\":\"prefetch\",\"href\":\"/playground\"}],[\"$\",\"link\",null,{\"rel\":\"prefetch\",\"href\":\"/logs\"}],[\"$\",\"link\",null,{\"rel\":\"prefetch\",\"href\":\"/my-models\"}]]}],[\"$\",\"body\",null,{\"className\":\"font-sans antialiased\",\"children\":[[\"$\",\"$2\",null,{\"fallback\":null,\"children\":[\"$\",\"$L3\",null,{}]}],[\"$\",\"$L4\",null,{}],[\"$\",\"$L5\",null,{\"enableUserBehaviorTracking\":true,\"enableNavigationTracking\":true,\"enableInteractionTracking\":true}],[\"$\",\"$L6\",null,{\"children\":[\"$\",\"$L7\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L8\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"$L9\",null,{}],false,[\"$\",\"$La\",null,{\"id\":\"sw-register\",\"strategy\":\"afterInteractive\",\"children\":\"$b\"}]]}]]}]]}],{\"children\":[\"about\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L7\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L8\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$Lc\",null,{\"Component\":\"$d\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@e\",\"$@f\"]}],[\"$\",\"$L10\",null,{\"children\":\"$L11\"}],null,[\"$\",\"$L12\",null,{\"children\":[\"$L13\",\"$L14\",[\"$\",\"$L15\",null,{\"promise\":\"$@16\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"3GDEG3XC853sqVw5RLNkx\",{\"children\":[[\"$\",\"$L17\",null,{\"children\":\"$L18\"}],null]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$19\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"1a:I[74911,[],\"AsyncMetadata\"]\ne:{}\nf:{}\n11:[\"$\",\"$2\",null,{\"fallback\":null,\"children\":[\"$\",\"$L1a\",null,{\"promise\":\"$@1b\"}]}]\n"])</script><script>self.__next_f.push([1,"14:null\n"])</script><script>self.__next_f.push([1,"18:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n13:null\n"])</script><script>self.__next_f.push([1,"1b:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"RoKey - Smart LLM Key Router\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Advanced LLM API key routing and management\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]],\"error\":null,\"digest\":\"$undefined\"}\n16:{\"metadata\":\"$1b:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>
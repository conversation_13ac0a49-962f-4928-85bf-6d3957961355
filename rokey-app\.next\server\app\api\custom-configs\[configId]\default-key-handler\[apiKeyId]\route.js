(()=>{var e={};e.id=8825,e.ids=[8825],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{x:()=>i});var s=r(34386),a=r(44999);async function i(){let e=await (0,a.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70261:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{PUT:()=>d});var a=r(96559),i=r(48088),o=r(37719),u=r(32190),n=r(2507);async function d(e,{params:t}){let r=await (0,n.x)(),{configId:s,apiKeyId:a}=await t;if(!s||!a)return u.NextResponse.json({error:"Configuration ID and API Key ID are required"},{status:400});try{let{data:e,error:t}=await r.from("api_keys").select("id, custom_api_config_id").eq("id",a).eq("custom_api_config_id",s).single();if(t||!e)return u.NextResponse.json({error:"API Key not found in the specified configuration or error verifying."},{status:404});let{error:i}=await r.from("api_keys").update({is_default_general_chat_model:!1,updated_at:new Date().toISOString()}).eq("custom_api_config_id",s).neq("id",a);if(i)return u.NextResponse.json({error:"Failed to unset other default keys",details:i.message},{status:500});let{data:o,error:n}=await r.from("api_keys").update({is_default_general_chat_model:!0,updated_at:new Date().toISOString()}).eq("id",a).eq("custom_api_config_id",s).select("id, label, is_default_general_chat_model").single();if(n){if("23505"===n.code)return u.NextResponse.json({error:"Failed to set default key. Another key might already be set as default, or an issue with unsetting previous default.",details:n.message},{status:409});return u.NextResponse.json({error:"Failed to set API key as default general chat model",details:n.message},{status:500})}return u.NextResponse.json({message:"API key successfully set as default general chat model (new path).",data:o},{status:200})}catch(e){return u.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/custom-configs/[configId]/default-key-handler/[apiKeyId]/route",pathname:"/api/custom-configs/[configId]/default-key-handler/[apiKeyId]",filename:"route",bundlePath:"app/api/custom-configs/[configId]/default-key-handler/[apiKeyId]/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\custom-configs\\[configId]\\default-key-handler\\[apiKeyId]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:l,serverHooks:f}=p;function g(){return(0,o.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:l})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,3410],()=>r(70261));module.exports=s})();
"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6060],{8413:(e,s,a)=>{a.d(s,{A:()=>i});var t=a(95155),r=a(12115),l=a(57514);let n=new Map;function i(e){let{configId:s,onRetry:a,className:i="",disabled:d=!1}=e,[o,c]=(0,r.useState)(!1),[m,u]=(0,r.useState)([]),[x,g]=(0,r.useState)(!1),[h,p]=(0,r.useState)(!1),b=(0,r.useRef)(null),v=(0,r.useCallback)(async function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(s){if(e){let e=n.get(s);if(e&&Date.now()-e.timestamp<3e5){u(e.keys),p(!0);return}}g(!0);try{let e=await fetch("/api/keys?custom_config_id=".concat(s));if(e.ok){let a=(await e.json()).filter(e=>"active"===e.status);n.set(s,{keys:a,timestamp:Date.now()}),u(a),p(!0)}}catch(e){}finally{g(!1)}}},[s]);(0,r.useEffect)(()=>{s&&!h&&v(!0)},[s,v,h]),(0,r.useEffect)(()=>{let e=e=>{b.current&&!b.current.contains(e.target)&&c(!1)};return o&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[o]);let f=e=>{c(!1),a(e)};return(0,t.jsxs)("div",{className:"relative ".concat(i),ref:b,children:[(0,t.jsxs)("button",{onClick:()=>{o||0!==m.length||h||v(!0),c(!o)},disabled:d,className:"\n          flex items-center space-x-1 p-1.5 rounded transition-all duration-200 cursor-pointer\n          ".concat(d?"text-gray-300 cursor-not-allowed":"text-gray-500 hover:text-gray-700 hover:bg-white/20","\n        "),title:"Retry with different model",children:[(0,t.jsx)(l.E,{className:"w-4 h-4 stroke-2 ".concat(x?"animate-spin":"")}),(0,t.jsx)(l.D,{className:"w-3 h-3 stroke-2"})]}),o&&(0,t.jsx)("div",{className:"absolute bottom-full left-0 mb-1 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50 animate-scale-in origin-bottom-left",children:(0,t.jsxs)("div",{className:"py-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between px-3 py-2 border-b border-gray-100",children:[(0,t.jsx)("span",{className:"text-xs font-medium text-gray-600",children:"Retry Options"}),(0,t.jsx)("button",{onClick:e=>{e.stopPropagation(),v(!1)},disabled:x,className:"p-1 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50",title:"Refresh available models",children:(0,t.jsx)(l.E,{className:"w-3 h-3 ".concat(x?"animate-spin":"")})})]}),(0,t.jsxs)("button",{onClick:()=>f(),className:"w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2",children:[(0,t.jsx)(l.E,{className:"w-4 h-4 text-gray-500"}),(0,t.jsx)("span",{children:"Retry with same model"})]}),(m.length>0||x)&&(0,t.jsx)("div",{className:"border-t border-gray-100 my-1"}),x&&(0,t.jsxs)("div",{className:"px-3 py-2 text-sm text-gray-500 flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"}),(0,t.jsx)("span",{children:"Loading models..."})]}),m.map(e=>(0,t.jsxs)("button",{onClick:()=>f(e.id),className:"w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex flex-col disabled:opacity-50",disabled:x,children:[(0,t.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,t.jsx)("span",{className:"font-medium",children:e.label}),(0,t.jsx)("span",{className:"text-xs text-gray-500 capitalize",children:e.provider})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 mt-0.5",children:["Temperature: ",e.temperature]})]},e.id)),!x&&0===m.length&&h&&(0,t.jsx)("div",{className:"px-3 py-2 text-sm text-gray-500",children:"No alternative models available"}),m.length>0&&!x&&(0,t.jsxs)("div",{className:"px-3 py-1 text-xs text-gray-400 border-t border-gray-100 flex items-center justify-between",children:[(0,t.jsxs)("span",{children:[m.length," model",1!==m.length?"s":""," available"]}),(()=>{let e=n.get(s);return e&&Date.now()-e.timestamp<3e5?(0,t.jsx)("span",{className:"text-green-500 text-xs",children:"●"}):null})()]})]})})]})}},14446:(e,s,a)=>{a.d(s,{Ay:()=>r,CE:()=>l});var t=a(95155);function r(){return(0,t.jsx)("div",{className:"min-h-screen bg-cream animate-fade-in",children:(0,t.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"})}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-64 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-48 animate-pulse"})]})]})}),(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded-lg animate-pulse"})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsxs)("div",{className:"card p-6",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-32 mb-6 animate-pulse"}),(0,t.jsx)("div",{className:"space-y-4",children:[1,2,3,4].map(e=>(0,t.jsx)("div",{className:"border border-gray-200 rounded-xl p-4 animate-pulse",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded-lg animate-pulse"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"h-5 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-full animate-pulse"})]})]})},e))})]})}),(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsxs)("div",{className:"card p-8 min-h-[600px]",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("div",{className:"h-7 bg-gray-200 rounded w-48 mb-3 animate-pulse"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-full animate-pulse"})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-32 bg-gray-200 rounded animate-pulse"})]})]}),(0,t.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,t.jsx)("div",{className:"h-5 bg-gray-200 rounded w-40 mb-4 animate-pulse"}),(0,t.jsx)("div",{className:"space-y-3",children:[1,2,3].map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-1 animate-pulse"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-32 animate-pulse"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-16 animate-pulse"}),(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-12 animate-pulse"})]})]},e))})]}),(0,t.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,t.jsx)("div",{className:"h-5 bg-gray-200 rounded w-48 mb-4 animate-pulse"}),(0,t.jsx)("div",{className:"grid grid-cols-5 gap-3 mb-4",children:[1,2,3,4,5].map(e=>(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 mx-auto mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse"})]},e))}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})]}),(0,t.jsx)("div",{className:"border-t border-gray-200 pt-6",children:(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-40 animate-pulse"})})]})]})})]})]})})}function l(){return(0,t.jsx)("div",{className:"min-h-screen bg-cream animate-fade-in",children:(0,t.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-48 mb-1 animate-pulse"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 animate-pulse"})]})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8",children:[1,2,3,4].map(e=>(0,t.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-1 animate-pulse"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-16 animate-pulse"})]})]})},e))}),(0,t.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-40 mb-4 animate-pulse"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"}),(0,t.jsx)("div",{className:"h-20 bg-gray-200 rounded animate-pulse"}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})]})]})]})})}a(12115)},16910:(e,s,a)=>{a.d(s,{w:()=>m});var t=a(95155),r=a(12115),l=a(70036),n=a(52399),i=a(82880);let d=e=>{let{senderName:s,roleId:a}=e,r=(e=>{if(!e||"moderator"===e)return"from-blue-500 to-blue-600";let s=["from-green-500 to-green-600","from-purple-500 to-purple-600","from-orange-500 to-orange-600","from-pink-500 to-pink-600","from-indigo-500 to-indigo-600","from-teal-500 to-teal-600","from-red-500 to-red-600","from-yellow-500 to-yellow-600"];return s[e.split("").reduce((e,s)=>e+s.charCodeAt(0),0)%s.length]})(a),l=!a||"moderator"===a;return(0,t.jsx)("div",{className:"flex justify-start mb-4 opacity-75",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3 max-w-[85%]",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r ".concat(r," flex items-center justify-center text-white shadow-sm animate-pulse"),children:(e=>e&&"moderator"!==e?(0,t.jsx)(i.Y,{className:"w-4 h-4"}):(0,t.jsx)(i.B,{className:"w-4 h-4"}))(a)}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,t.jsx)("span",{className:"text-sm font-semibold ".concat(l?"text-blue-700":"text-gray-700"),children:s}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:(e=>{let s=["is thinking...","is working on this...","is analyzing...","is processing...","is crafting a response..."];return s[e.split("").reduce((e,s)=>e+s.charCodeAt(0),0)%s.length]})(s)})]}),(0,t.jsx)("div",{className:"inline-block px-4 py-3 rounded-2xl shadow-sm ".concat(l?"bg-blue-50 border border-blue-100":"bg-gray-50 border border-gray-100"),children:(0,t.jsx)("div",{className:"flex items-center space-x-1",children:(0,t.jsxs)("div",{className:"flex space-x-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0ms"}}),(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"150ms"}}),(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"300ms"}})]})})})]})]})})},o=e=>{let{executionId:s,events:a,isConnected:l,error:i,isComplete:o}=e,[c,m]=(0,r.useState)([]),[u,x]=(0,r.useState)(new Set),g=(0,r.useRef)(null);return((0,r.useEffect)(()=>{var e;null==(e=g.current)||e.scrollIntoView({behavior:"smooth"})},[c]),(0,r.useEffect)(()=>{let e=[],t=new Set;a.forEach((a,r)=>{var l,n,i,d,o,c,m,u,x,g,h;let p=new Date(a.timestamp||Date.now()),b="".concat(s,"-").concat(r);switch(a.type){case"orchestration_started":e.push({id:b,sender:"moderator",senderName:"Moderator",content:"\uD83C\uDFAC Welcome to the AI Team Collaboration! I'm assembling the perfect team for this task.",timestamp:p,type:"message"});break;case"task_decomposed":let v=((null==(l=a.data)?void 0:l.steps)||[]).map(e=>"\uD83E\uDD16 @".concat(e.roleId," - ").concat(e.modelName||"AI Specialist")).join("\n");e.push({id:b,sender:"moderator",senderName:"Moderator",content:"\uD83D\uDCCB I've analyzed the task and assembled this expert team:\n\n".concat(v,"\n\nLet's begin the collaboration!"),timestamp:p,type:"assignment"});break;case"step_assigned":e.push({id:b,sender:"moderator",senderName:"Moderator",roleId:a.role_id,content:"\uD83C\uDFAF @".concat(a.role_id,", you're up! ").concat((null==(n=a.data)?void 0:n.commentary)||"Please begin your specialized work on this task."),timestamp:p,type:"assignment"});break;case"moderator_assignment":e.push({id:b,sender:"moderator",senderName:"Moderator",roleId:a.role_id,content:(null==(i=a.data)?void 0:i.message)||"\uD83C\uDFAF @".concat(a.role_id,", you're up! Please begin your specialized work on this task."),timestamp:p,type:"assignment"});break;case"specialist_acknowledgment":e.push({id:b,sender:"specialist",senderName:a.role_id||"Specialist",roleId:a.role_id,content:(null==(d=a.data)?void 0:d.message)||"✅ Understood! I'm ".concat(a.role_id," and I'll handle this task with expertise. Starting work now..."),timestamp:p,type:"message"});break;case"step_started":case"step_progress":a.role_id&&t.add(a.role_id);break;case"specialist_message":e.push({id:b,sender:"specialist",senderName:a.role_id||"Specialist",roleId:a.role_id,content:"".concat((null==(o=a.data)?void 0:o.message)||"\uD83C\uDF89 Perfect! I've completed my part of the task. Here's what I've delivered:","\n\n").concat((null==(c=a.data)?void 0:c.output)||"Task completed successfully!"),timestamp:p,type:"completion"});break;case"step_completed":a.role_id&&t.delete(a.role_id);break;case"handoff_message":e.push({id:b,sender:"moderator",senderName:"Moderator",content:(null==(m=a.data)?void 0:m.message)||"✨ Excellent work, @".concat(null==(u=a.data)?void 0:u.fromRole,"! Quality looks great. Now passing to @").concat(null==(x=a.data)?void 0:x.toRole,"..."),timestamp:p,type:"handoff"});break;case"synthesis_started":e.push({id:b,sender:"moderator",senderName:"Moderator",content:(null==(g=a.data)?void 0:g.message)||"\uD83E\uDDE9 Fantastic teamwork everyone! Now I'll synthesize all your excellent contributions into the final deliverable...",timestamp:p,type:"message"}),t.add("moderator");break;case"synthesis_complete":t.delete("moderator"),e.push({id:b,sender:"moderator",senderName:"Moderator",content:(null==(h=a.data)?void 0:h.message)||"\uD83C\uDF8A Mission accomplished! The team has delivered an outstanding result. Great collaboration everyone!",timestamp:p,type:"completion"})}}),m(e),x(t)},[a,s]),i)?(0,t.jsx)("div",{className:"flex-1 flex items-center justify-center p-8",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)("svg",{className:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Connection Error"}),(0,t.jsx)("p",{className:"text-gray-600",children:i})]})}):(0,t.jsxs)("div",{className:"flex flex-col h-full",children:[(0,t.jsx)("div",{className:"px-4 py-2 text-xs font-medium ".concat(l?"bg-green-50 text-green-700 border-b border-green-100":"bg-yellow-50 text-yellow-700 border-b border-yellow-100"),children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(l?"bg-green-500":"bg-yellow-500")}),(0,t.jsx)("span",{children:l?"Connected to AI Team":"Connecting..."})]})}),(0,t.jsxs)("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:[0===c.length&&(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)("svg",{className:"w-6 h-6 text-blue-600 animate-pulse",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),(0,t.jsx)("p",{className:"text-gray-500",children:"Waiting for AI team to start collaboration..."})]}),c.map(e=>(0,t.jsx)(n.c,{message:e},e.id)),Array.from(u).map(e=>(0,t.jsx)(d,{senderName:e,roleId:"moderator"!==e?e:void 0},e)),(0,t.jsx)("div",{ref:g})]})]})};var c=a(306);let m=e=>{let{executionId:s,onComplete:a,onError:n,onCanvasStateChange:i,forceMaximize:d=!1}=e,[m,u]=(0,r.useState)(!0),[x,g]=(0,r.useState)(!1),[h,p]=(0,r.useState)(!1),[b,v]=(0,r.useState)(""),{events:f,isConnected:y,error:j}=(0,l.LJ)(s);(0,r.useEffect)(()=>{let e=f.find(e=>"synthesis_complete"===e.type);if(e&&!h){var s;p(!0);let t=(null==(s=e.data)?void 0:s.result)||"Orchestration completed successfully";v(t),a&&a(t)}},[f,h,a]),(0,r.useEffect)(()=>{j&&n&&n(j)},[j,n]);let N=()=>{g(!1),u(!0),null==i||i(!0,!1)};return((0,r.useEffect)(()=>{null==i||i(m,x)},[m,x,i]),(0,r.useEffect)(()=>{d&&x&&N()},[d,x,N]),x||!m)?null:(0,t.jsx)(t.Fragment,{children:(0,t.jsxs)("div",{className:"fixed top-0 right-0 h-full w-1/2 bg-gradient-to-br from-gray-900 via-black to-gray-900 shadow-2xl z-[9999] transform transition-all duration-500 ease-out border-l border-orange-500/20 ".concat(m&&!x?"translate-x-0":"translate-x-full"),children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-orange-500/10 via-transparent to-orange-500/10 pointer-events-none"}),(0,t.jsx)("div",{className:"absolute left-0 top-0 h-full w-[2px] bg-gradient-to-b from-transparent via-orange-500 to-transparent animate-pulse"}),(0,t.jsxs)("div",{className:"relative flex items-center justify-between p-6 border-b border-orange-500/20 bg-gradient-to-r from-black/80 via-gray-900/90 to-black/80 backdrop-blur-sm",children:[(0,t.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-orange-500/50 to-transparent"}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg",children:(0,t.jsx)(c.vQ,{className:"w-5 h-5 text-white"})}),(0,t.jsx)("div",{className:"absolute inset-0 bg-orange-500/30 rounded-xl blur-md -z-10 animate-pulse"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"font-bold text-white text-lg tracking-wide",children:"AI Team Collaboration"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(h?"bg-green-400":"bg-orange-400"," animate-pulse")}),(0,t.jsx)("p",{className:"text-sm text-gray-300 font-medium",children:h?"Mission Complete":"Team Active"})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-1.5 bg-orange-500/10 border border-orange-500/20 rounded-full",children:[(0,t.jsx)(c.BZ,{className:"w-4 h-4 text-orange-400"}),(0,t.jsx)("span",{className:"text-xs text-orange-300 font-medium",children:"LIVE"})]}),(0,t.jsxs)("button",{onClick:()=>{g(!0),null==i||i(!1,!0)},className:"group relative p-2.5 text-gray-400 hover:text-white hover:bg-orange-500/20 rounded-xl transition-all duration-300 border border-transparent hover:border-orange-500/30","aria-label":"Minimize canvas",children:[(0,t.jsx)(c.QG,{className:"w-5 h-5 transition-transform group-hover:scale-110"}),(0,t.jsx)("div",{className:"absolute inset-0 bg-orange-500/20 rounded-xl opacity-0 group-hover:opacity-100 blur-sm transition-opacity duration-300 -z-10"})]})]})]}),(0,t.jsxs)("div",{className:"flex-1 h-full overflow-hidden relative",children:[(0,t.jsxs)("div",{className:"absolute inset-0 opacity-5",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,107,53,0.1),transparent_50%)]"}),(0,t.jsx)("div",{className:"absolute inset-0 bg-[linear-gradient(90deg,transparent_0%,rgba(255,107,53,0.05)_50%,transparent_100%)]"})]}),(0,t.jsx)(o,{executionId:s,events:f,isConnected:y,error:j,isComplete:h})]})]})})}},38050:(e,s,a)=>{a.d(s,{default:()=>i});var t=a(12115),r=a(35695),l=a(5777),n=a(44042);function i(e){let{enableUserBehaviorTracking:s=!0,enableNavigationTracking:a=!0,enableInteractionTracking:i=!0}=e,d=(0,r.usePathname)(),o=(0,t.useRef)(""),c=(0,t.useRef)(0),{exportMetrics:m}=(0,n.D)("PerformanceTracker");return(0,t.useEffect)(()=>{if(!a)return;let e=o.current;e&&e!==d&&(l.zf.trackNavigation(e,d),performance.now(),c.current),o.current=d,c.current=performance.now()},[d,a]),(0,t.useEffect)(()=>{if(!i)return;let e=e=>{let s=e.target;if("mouseenter"===e.type&&"A"===s.tagName){let e=s.getAttribute("href");e&&e.startsWith("/")&&l.zf.schedulePrefetch(e)}if("click"===e.type&&("BUTTON"===s.tagName||s.closest("button"))){var a;let e=(null==(a=s.textContent)?void 0:a.trim())||"Unknown";e.toLowerCase().includes("get started")||e.toLowerCase().includes("sign up")?l.zf.schedulePrefetch("/auth/signup"):e.toLowerCase().includes("pricing")?l.zf.schedulePrefetch("/pricing"):e.toLowerCase().includes("features")&&l.zf.schedulePrefetch("/features")}};return document.addEventListener("mouseenter",e,!0),document.addEventListener("click",e,!0),()=>{document.removeEventListener("mouseenter",e,!0),document.removeEventListener("click",e,!0)}},[i]),(0,t.useEffect)(()=>{let e;if(!s)return;let a=!1,t=0,r=()=>{a||(a=!0,performance.now());let s=window.scrollY/(document.body.scrollHeight-window.innerHeight)*100;t=Math.max(t,s),clearTimeout(e),e=setTimeout(()=>{a=!1,performance.now(),t>80&&("/"===d?(l.zf.schedulePrefetch("/pricing"),l.zf.schedulePrefetch("/features")):"/features"===d&&l.zf.schedulePrefetch("/auth/signup")),t=0},150)},n=performance.now(),i=()=>{performance.now()-n>1e4&&("/"===d?l.zf.schedulePrefetch("/auth/signup"):"/pricing"===d&&l.zf.schedulePrefetch("/auth/signup"))};window.addEventListener("scroll",r,{passive:!0});let o=()=>{document.hidden&&i()};document.addEventListener("visibilitychange",o);let c=()=>{i()};return window.addEventListener("beforeunload",c),()=>{clearTimeout(e),window.removeEventListener("scroll",r),document.removeEventListener("visibilitychange",o),window.removeEventListener("beforeunload",c),i()}},[d,s,m]),(0,t.useEffect)(()=>{if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{let s=e.getEntries();s[s.length-1].startTime});e.observe({entryTypes:["largest-contentful-paint"]});let s=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{e.processingStart&&e.startTime&&(e.processingStart,e.startTime)})});s.observe({entryTypes:["first-input"]});let a=0,t=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{e.hadRecentInput||(a+=e.value)})});return t.observe({entryTypes:["layout-shift"]}),()=>{e.disconnect(),s.disconnect(),t.disconnect()}}},[]),null}},50956:(e,s,a)=>{a.d(s,{A:()=>d});var t=a(95155),r=a(12115),l=a(6874),n=a.n(l),i=a(35695);function d(e){let{href:s,children:a,className:l="",prefetch:d=!0}=e,o=(0,i.useRouter)();return(0,t.jsx)(n(),{href:s,className:l,onClick:e=>{e.preventDefault(),(0,r.startTransition)(()=>{o.push(s)})},prefetch:d,children:a})}},69903:(e,s,a)=>{a.d(s,{A:()=>x});var t=a(95155),r=a(12115),l=a(35695),n=a(99323);let i=()=>(0,t.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[1,2,3].map(e=>(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-4"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"})]},e))}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,t.jsx)("div",{className:"space-y-3",children:[1,2,3,4].map(e=>(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))})]})]}),d=()=>(0,t.jsxs)("div",{className:"space-y-8 animate-pulse",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded w-1/2 mx-auto mb-4"}),(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[1,2,3].map(e=>(0,t.jsxs)("div",{className:"bg-white p-8 rounded-2xl border-2",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2 mx-auto mb-2"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mx-auto mb-4"}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-1/3 mx-auto"})]}),(0,t.jsx)("div",{className:"space-y-3 mb-8",children:[1,2,3,4,5].map(e=>(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))}),(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded"})]},e))})]}),o=()=>(0,t.jsxs)("div",{className:"space-y-8 animate-pulse",children:[(0,t.jsxs)("div",{className:"text-center py-20 bg-gradient-to-br from-slate-50 to-blue-50",children:[(0,t.jsx)("div",{className:"h-16 bg-gray-200 rounded w-2/3 mx-auto mb-6"}),(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,t.jsx)("div",{className:"py-20",children:(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-12",children:[1,2,3,4,5,6].map(e=>(0,t.jsx)("div",{className:"bg-white rounded-2xl p-8 border",children:(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-xl"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2 mb-3"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded mb-4"}),(0,t.jsx)("div",{className:"space-y-2",children:[1,2,3].map(e=>(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-3/4"},e))})]})]})},e))})})]}),c=()=>(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50",children:(0,t.jsxs)("div",{className:"bg-white p-8 rounded-2xl shadow-lg border w-full max-w-md animate-pulse",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/2 mx-auto mb-2"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded"})]})]})}),m=()=>(0,t.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 mb-4"}),(0,t.jsx)("div",{className:"space-y-3",children:[1,2,3].map(e=>(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))}),(0,t.jsx)("div",{className:"h-32 bg-gray-200 rounded mt-4"}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded mt-4"})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 mb-4"}),(0,t.jsx)("div",{className:"h-64 bg-gray-200 rounded"})]})]})]}),u=()=>(0,t.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-2/3"}),(0,t.jsx)("div",{className:"bg-white p-6 rounded-lg border",children:(0,t.jsx)("div",{className:"space-y-4",children:[1,2,3,4,5].map(e=>(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))})})]});function x(e){let s,{targetRoute:a,children:x}=e,[g,h]=(0,r.useState)(!0),[p,b]=(0,r.useState)(!1),v=(0,l.usePathname)(),f=(0,r.useRef)(),{isPageCached:y}=(0,n.bu)()||{isPageCached:()=>!1};return((0,r.useEffect)(()=>(v===a&&(f.current=setTimeout(()=>{b(!0),setTimeout(()=>h(!1),100)},y(a)?50:200)),()=>{f.current&&clearTimeout(f.current)}),[v,a,y]),(0,r.useEffect)(()=>{h(!0),b(!1)},[a]),v!==a&&g||v===a&&g&&!p)?(0,t.jsx)("div",{className:"optimistic-loading-container",children:(s=a).startsWith("/dashboard")?(0,t.jsx)(i,{}):s.startsWith("/pricing")?(0,t.jsx)(d,{}):s.startsWith("/features")?(0,t.jsx)(o,{}):s.startsWith("/auth/")?(0,t.jsx)(c,{}):s.startsWith("/playground")?(0,t.jsx)(m,{}):(0,t.jsx)(u,{})}):(0,t.jsx)("div",{className:"transition-opacity duration-300 ".concat(p?"opacity-100":"opacity-0"),children:x})}},71848:(e,s,a)=>{a.d(s,{A:()=>i});var t=a(95155),r=a(23405);let l=e=>{let{label:s,value:a}=e;return(0,t.jsxs)("div",{className:"py-2 sm:grid sm:grid-cols-3 sm:gap-4",children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-700",children:s}),(0,t.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 break-words",children:null!=a&&""!==a?a:"N/A"})]})},n=e=>{let s,{title:a,data:r}=e;if(null==r)s="N/A";else if("string"==typeof r)s=r;else try{s=JSON.stringify(r,null,2)}catch(e){s="Invalid JSON data"}return(0,t.jsxs)("div",{className:"py-2",children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-700 mb-1",children:a}),(0,t.jsx)("dd",{className:"mt-1 text-sm text-gray-900 bg-gray-50 p-3 rounded-lg border",children:(0,t.jsx)("pre",{className:"whitespace-pre-wrap break-all",children:s})})]})};function i(e){var s;let{log:a,onClose:i,apiConfigNameMap:d}=e;if(!a)return null;let o=a.custom_api_config_id?d[a.custom_api_config_id]||"Unknown Model":"N/A";return(0,t.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50 transition-opacity duration-300 ease-in-out",onClick:i,children:(0,t.jsxs)("div",{className:"card max-w-2xl w-full max-h-[90vh] flex flex-col",onClick:e=>e.stopPropagation(),children:[(0,t.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-200",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["Log Details (ID: ",a.id.substring(0,8),"...)"]}),(0,t.jsx)("button",{onClick:i,className:"text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded-lg transition-colors duration-200",children:(0,t.jsx)(r.f,{className:"h-6 w-6"})})]}),(0,t.jsxs)("div",{className:"p-6 space-y-4 overflow-y-auto",children:[(0,t.jsxs)("dl",{className:"divide-y divide-gray-200",children:[(0,t.jsx)(l,{label:"Timestamp",value:new Date(a.request_timestamp).toLocaleString()}),(0,t.jsx)(l,{label:"API Model Used",value:o}),(0,t.jsx)(l,{label:"Role Requested",value:a.role_requested}),(0,t.jsx)(l,{label:"Role Used",value:a.role_used}),(0,t.jsx)(l,{label:"Status",value:null===(s=a.status_code)?(0,t.jsx)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-gray-600 text-gray-100",children:"N/A"}):s>=200&&s<300?(0,t.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-green-600 text-green-100",children:["Success (",s,")"]}):s>=400?(0,t.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-red-600 text-red-100",children:["Error (",s,")"]}):(0,t.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-yellow-500 text-yellow-100",children:["Other (",s,")"]})}),(0,t.jsx)(l,{label:"LLM Provider",value:a.llm_provider_name}),(0,t.jsx)(l,{label:"LLM Model Name",value:a.llm_model_name}),(0,t.jsx)(l,{label:"LLM Latency",value:null!==a.llm_provider_latency_ms?"".concat(a.llm_provider_latency_ms," ms"):"N/A"}),(0,t.jsx)(l,{label:"RoKey Latency",value:null!==a.processing_duration_ms?"".concat(a.processing_duration_ms," ms"):"N/A"}),(0,t.jsx)(l,{label:"Input Tokens",value:null!==a.input_tokens?a.input_tokens:"N/A"}),(0,t.jsx)(l,{label:"Output Tokens",value:null!==a.output_tokens?a.output_tokens:"N/A"}),(0,t.jsx)(l,{label:"Cost",value:null!==a.cost?"$".concat(a.cost.toFixed(6)):"N/A"}),(0,t.jsx)(l,{label:"Multimodal Request",value:a.is_multimodal?"Yes":"No"}),(0,t.jsx)(l,{label:"IP Address",value:a.ip_address}),a.user_id&&(0,t.jsx)(l,{label:"User ID",value:a.user_id}),a.error_message&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(l,{label:"Error Message",value:a.error_message}),(0,t.jsx)(l,{label:"Error Source",value:a.error_source})]}),a.llm_provider_status_code&&(0,t.jsx)(l,{label:"LLM Provider Status",value:a.llm_provider_status_code})]}),a.request_payload_summary&&(0,t.jsx)(n,{title:"Request Payload Summary",data:a.request_payload_summary}),a.response_payload_summary&&(0,t.jsx)(n,{title:"Response Payload Summary",data:a.response_payload_summary}),a.error_details_zod&&(0,t.jsx)(n,{title:"Zod Validation Error Details",data:a.error_details_zod})]}),(0,t.jsx)("div",{className:"p-6 border-t border-gray-200 text-right",children:(0,t.jsx)("button",{onClick:i,className:"btn-secondary",children:"Close"})})]})})}},74338:(e,s,a)=>{a.d(s,{B0:()=>r,F6:()=>l});var t=a(95155);function r(e){let{className:s=""}=e;return(0,t.jsx)("div",{className:"glass rounded-2xl p-6 animate-pulse ".concat(s),children:(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gray-700 rounded-xl"}),(0,t.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-700 rounded w-3/4"}),(0,t.jsx)("div",{className:"h-3 bg-gray-700 rounded w-1/2"})]})]})})}function l(e){let{rows:s=5,columns:a=4}=e;return(0,t.jsx)("div",{className:"glass rounded-2xl overflow-hidden",children:(0,t.jsxs)("div",{className:"animate-pulse",children:[(0,t.jsx)("div",{className:"bg-gray-800 p-4 border-b border-gray-700",children:(0,t.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(a,", 1fr)")},children:Array.from({length:a}).map((e,s)=>(0,t.jsx)("div",{className:"h-4 bg-gray-700 rounded"},s))})}),Array.from({length:s}).map((e,s)=>(0,t.jsx)("div",{className:"p-4 border-b border-gray-700 last:border-b-0",children:(0,t.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(a,", 1fr)")},children:Array.from({length:a}).map((e,s)=>(0,t.jsx)("div",{className:"h-4 bg-gray-700 rounded"},s))})},s))]})})}},80377:(e,s,a)=>{a.d(s,{A:()=>n});var t=a(95155),r=a(12115),l=a(38152);function n(e){let{isOpen:s,onClose:a,onConfirm:n,title:i,message:d,confirmText:o="Delete",cancelText:c="Cancel",type:m="danger",isLoading:u=!1}=e;(0,r.useEffect)(()=>{let e=e=>{"Escape"===e.key&&s&&!u&&a()};return s&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[s,u,a]);let x=(()=>{switch(m){case"danger":return{iconBg:"bg-red-100",iconColor:"text-red-600",confirmButton:"bg-red-600 hover:bg-red-700 focus:ring-red-500",icon:l.uc};case"warning":return{iconBg:"bg-yellow-100",iconColor:"text-yellow-600",confirmButton:"bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500",icon:l.Pi};default:return{iconBg:"bg-blue-100",iconColor:"text-blue-600",confirmButton:"bg-blue-600 hover:bg-blue-700 focus:ring-blue-500",icon:l.Pi}}})(),g=x.icon;return s?(0,t.jsxs)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:[(0,t.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300",onClick:u?void 0:a}),(0,t.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"relative w-full max-w-md transform overflow-hidden rounded-2xl bg-white shadow-2xl transition-all duration-300 scale-100 opacity-100",children:[(0,t.jsx)("div",{className:"relative px-6 pt-6",children:(0,t.jsx)("button",{onClick:a,disabled:u,className:"absolute top-4 right-4 p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,t.jsx)(l.fK,{className:"h-5 w-5"})})}),(0,t.jsxs)("div",{className:"px-6 pb-6",children:[(0,t.jsx)("div",{className:"mx-auto flex h-16 w-16 items-center justify-center rounded-full mb-6",children:(0,t.jsx)("div",{className:"".concat(x.iconBg," rounded-full p-3"),children:(0,t.jsx)(g,{className:"h-8 w-8 ".concat(x.iconColor)})})}),(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3 text-center",children:i}),(0,t.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed mb-8 text-center",children:d}),(0,t.jsxs)("div",{className:"flex flex-col-reverse sm:flex-row sm:justify-center gap-3",children:[(0,t.jsx)("button",{type:"button",onClick:a,disabled:u,className:"w-full sm:w-auto px-6 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200",children:c}),(0,t.jsx)("button",{type:"button",onClick:n,disabled:u,className:"w-full sm:w-auto px-6 py-3 text-sm font-medium text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 ".concat(x.confirmButton),children:u?(0,t.jsxs)("div",{className:"flex items-center justify-center",children:[(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Processing..."]}):o})]})]})]})})]}):null}},95060:(e,s,a)=>{a.d(s,{A:()=>b});var t=a(95155),r=a(6874),l=a.n(r),n=a(66766),i=a(35695),d=a(12115),o=a(42597),c=a(14097),m=a(22261),u=a(99323),x=a(37843),g=a(24403),h=a(42724);let p=[{href:"/dashboard",label:"Dashboard",icon:o.fA,iconSolid:c.fA,description:"Overview & analytics"},{href:"/my-models",label:"My Models",icon:o.RY,iconSolid:c.RY,description:"API key management"},{href:"/playground",label:"Playground",icon:o.cu,iconSolid:c.cu,description:"Test your models"},{href:"/routing-setup",label:"Routing Setup",icon:o.sR,iconSolid:c.sR,description:"Configure routing"},{href:"/logs",label:"Logs",icon:o.AQ,iconSolid:c.AQ,description:"Request history"},{href:"/training",label:"Prompt Engineering",icon:o.tl,iconSolid:c.tl,description:"Custom prompts"},{href:"/analytics",label:"Analytics",icon:o.r9,iconSolid:c.r9,description:"Advanced insights"}];function b(){let e=(0,i.usePathname)(),{isCollapsed:s,isHovered:a,isHoverDisabled:r,setHovered:o}=(0,m.c)(),{navigateOptimistically:c}=(0,u.bu)()||{navigateOptimistically:()=>{}},{prefetchOnHover:b}=(0,x.C)(),{prefetchWhenIdle:v}=(0,x.e)(),{prefetchChatHistory:f}=(0,g.l2)(),{predictions:y,isLearning:j}=(0,h.x)(),N=(0,h.G)();(0,d.useEffect)(()=>{let s=p.map(e=>e.href),a=y.slice(0,2),t=N.filter(e=>"high"===e.priority).map(e=>e.route).slice(0,2);return v([...a,...t,...s.filter(s=>s!==e&&!a.includes(s)&&!t.includes(s)),"/playground","/logs"].slice(0,6))},[e,v,y,N,j]);let w=!s||a;return(0,t.jsx)("aside",{className:"sidebar flex flex-col h-full flex-shrink-0 transition-all duration-200 ease-out bg-gray-900 ".concat(w?"w-64":"w-16"),onMouseEnter:()=>!r&&o(!0),onMouseLeave:()=>!r&&o(!1),children:(0,t.jsx)("div",{className:"flex-1 relative overflow-hidden",children:(0,t.jsxs)("div",{className:"p-6 transition-all duration-200 ease-out ".concat(w?"px-6":"px-3"),children:[(0,t.jsx)("div",{className:"mb-8 pt-4 transition-all duration-200 ease-out ".concat(w?"":"text-center"),children:(0,t.jsxs)("div",{className:"relative overflow-hidden",children:[(0,t.jsx)("div",{className:"transition-all duration-200 ease-out ".concat(w?"opacity-0 scale-75 -translate-y-2":"opacity-100 scale-100 translate-y-0"," ").concat(w?"absolute":"relative"," w-8 h-8 bg-white rounded-lg flex items-center justify-center mx-auto p-0.5"),children:(0,t.jsx)(n.default,{src:"/roukey_logo.png",alt:"RouKey",width:28,height:28,className:"object-cover"})}),(0,t.jsxs)("div",{className:"transition-all duration-200 ease-out ".concat(w?"opacity-100 scale-100 translate-y-0":"opacity-0 scale-75 translate-y-2"," ").concat(w?"relative":"absolute top-0 left-0 w-full"),children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-white tracking-tight whitespace-nowrap",children:"RouKey"}),(0,t.jsx)("p",{className:"text-sm text-gray-400 mt-1 whitespace-nowrap",children:"Smart LLM Router"})]})]})}),(0,t.jsx)("nav",{className:"space-y-2",children:p.map(s=>{let a=e===s.href||e.startsWith(s.href+"/"),r=a?s.iconSolid:s.icon,n=y.includes(s.href),i=N.find(e=>e.route===s.href),d="/playground"===s.href?{onMouseEnter:()=>{if("/playground"===s.href){b(s.href,50).onMouseEnter();let e=new URLSearchParams(window.location.search).get("config");e&&f(e)}}}:b(s.href,50);return(0,t.jsx)(l(),{href:s.href,onClick:e=>{e.preventDefault(),c(s.href)},className:"sidebar-nav-item group flex items-center transition-all duration-200 ease-out w-full text-left ".concat(a?"active":""," ").concat(w?"":"collapsed"),title:w?void 0:s.label,...d,children:(0,t.jsxs)("div",{className:"relative flex items-center w-full overflow-hidden",children:[(0,t.jsxs)("div",{className:"relative flex items-center justify-center transition-all duration-200 ease-out ".concat(w?"w-5 h-5 mr-3":"w-10 h-10 rounded-xl"," ").concat(!w&&a?"bg-white shadow-sm":w?"":"bg-transparent hover:bg-white/10"),children:[(0,t.jsx)(r,{className:"transition-all duration-200 ease-out ".concat("h-5 w-5"," ").concat(a?"text-orange-500":"text-white")}),n&&!a&&(0,t.jsx)("div",{className:"absolute rounded-full bg-blue-400 animate-pulse transition-all duration-200 ease-out ".concat(w?"-top-1 -right-1 w-2 h-2":"-top-1 -right-1 w-3 h-3"),title:"Predicted next destination"})]}),(0,t.jsxs)("div",{className:"flex-1 transition-all duration-200 ease-out ".concat(w?"opacity-100 translate-x-0 max-w-full":"opacity-0 translate-x-4 max-w-0"),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between whitespace-nowrap",children:[(0,t.jsx)("div",{className:"font-medium text-sm",children:s.label}),i&&!a&&(0,t.jsx)("span",{className:"text-xs px-1.5 py-0.5 rounded-full ml-2 ".concat("high"===i.priority?"bg-blue-500/20 text-blue-300":"bg-gray-500/20 text-gray-300"),children:"high"===i.priority?"!":"\xb7"})]}),(0,t.jsx)("div",{className:"text-xs transition-colors duration-200 whitespace-nowrap ".concat(a?"text-orange-400":"text-gray-400"),children:i?i.reason:s.description})]})]})},s.href)})})]})})})}},95494:(e,s,a)=>{a.d(s,{A:()=>u});var t=a(95155),r=a(6874),l=a.n(r),n=a(12115),i=a(41045),d=a(22261),o=a(34962),c=a(83298),m=a(52643);function u(){var e,s,a,r,u,x,g;let{toggleSidebar:h}=(0,d.c)(),{breadcrumb:p}=(0,o.rT)(),{user:b,subscriptionStatus:v}=(0,c.R)(),[f,y]=(0,n.useState)(!1),j=(0,m.u)(),N=(null==b||null==(e=b.user_metadata)?void 0:e.first_name)||(null==b||null==(a=b.user_metadata)||null==(s=a.full_name)?void 0:s.split(" ")[0])||"User",w=N.charAt(0).toUpperCase()+((null==b||null==(x=b.user_metadata)||null==(u=x.last_name)||null==(r=u.charAt(0))?void 0:r.toUpperCase())||(null==(g=N.charAt(1))?void 0:g.toUpperCase())||"U"),k=(null==v?void 0:v.hasActiveSubscription)?(null==v?void 0:v.tier)==="starter"?"Starter Plan":(null==v?void 0:v.tier)==="professional"?"Professional Plan":(null==v?void 0:v.tier)==="enterprise"?"Enterprise Plan":"Starter Plan":"Starter Plan",_=async()=>{try{await j.auth.signOut(),window.location.href="/auth/signin"}catch(e){}};return(0,t.jsx)("nav",{className:"header border-b border-gray-200 bg-white/95 backdrop-blur-sm w-full",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("button",{onClick:h,className:"lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200",title:"Toggle sidebar",children:(0,t.jsx)(i.tK,{className:"h-6 w-6 text-gray-600"})}),(0,t.jsx)("div",{className:"lg:hidden",children:(0,t.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"RouKey"})}),(0,t.jsxs)("div",{className:"hidden lg:flex items-center space-x-2 text-sm text-gray-600",children:[(0,t.jsx)("span",{children:p.title}),(0,t.jsx)("span",{children:"/"}),(0,t.jsx)("span",{className:"text-gray-900 font-medium",children:p.subtitle})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-4",children:[(0,t.jsx)("div",{className:"hidden xl:block",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"text",placeholder:"Search...",className:"w-64 pl-10 pr-4 py-2.5 text-sm bg-white border border-gray-200 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-500 transition-all duration-200"}),(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("svg",{className:"h-4 w-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})})]})}),(0,t.jsxs)("button",{className:"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 relative",children:[(0,t.jsx)(i.XF,{className:"h-5 w-5 text-gray-600"}),(0,t.jsx)("span",{className:"absolute -top-1 -right-1 h-3 w-3 bg-orange-500 rounded-full"})]}),(0,t.jsxs)("div",{className:"hidden sm:block relative",children:[(0,t.jsxs)("button",{onClick:()=>y(!f),className:"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 flex items-center space-x-1",children:[(0,t.jsx)(i.Vy,{className:"h-5 w-5 text-gray-600"}),(0,t.jsx)(i.D3,{className:"h-3 w-3 text-gray-600 transition-transform duration-200 ".concat(f?"rotate-180":"")})]}),f&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"fixed inset-0 z-10",onClick:()=>y(!1)}),(0,t.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-20",children:[(0,t.jsxs)(l(),{href:"/dashboard/settings",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200",onClick:()=>y(!1),children:[(0,t.jsx)(i.Vy,{className:"h-4 w-4 mr-3 text-gray-500"}),"Settings"]}),(0,t.jsxs)(l(),{href:"/dashboard/billing",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200",onClick:()=>y(!1),children:[(0,t.jsx)(i.rM,{className:"h-4 w-4 mr-3 text-gray-500"}),"Billing"]}),(0,t.jsx)("hr",{className:"my-1 border-gray-200"}),(0,t.jsxs)("button",{onClick:_,className:"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200",children:[(0,t.jsx)(i.Rz,{className:"h-4 w-4 mr-3 text-red-500"}),"Sign Out"]})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3 px-2 sm:px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full bg-gradient-to-br from-orange-400 to-orange-500 flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-white font-semibold text-sm",children:w})}),(0,t.jsxs)("div",{className:"hidden md:block",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900",children:N}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:k})]})]})]})]})})})}}}]);
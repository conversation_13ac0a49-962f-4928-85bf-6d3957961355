{"/_not-found/page": "/_not-found", "/api/activity/route": "/api/activity", "/api/system-status/route": "/api/system-status", "/favicon.ico/route": "/favicon.ico", "/dashboard/page": "/dashboard", "/api/analytics/summary/route": "/api/analytics/summary", "/api/chat/conversations/route": "/api/chat/conversations", "/api/chat/messages/delete-after-timestamp/route": "/api/chat/messages/delete-after-timestamp", "/api/chat/messages/update-by-timestamp/route": "/api/chat/messages/update-by-timestamp", "/api/chat/messages/route": "/api/chat/messages", "/api/cleanup/pending-users/route": "/api/cleanup/pending-users", "/api/custom-configs/[configId]/default-chat-key/route": "/api/custom-configs/[configId]/default-chat-key", "/api/custom-configs/[configId]/default-key-handler/[apiKeyId]/route": "/api/custom-configs/[configId]/default-key-handler/[apiKeyId]", "/api/custom-configs/[configId]/route": "/api/custom-configs/[configId]", "/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments/route": "/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments", "/api/debug/checkout/route": "/api/debug/checkout", "/api/custom-configs/[configId]/routing/route": "/api/custom-configs/[configId]/routing", "/api/custom-configs/route": "/api/custom-configs", "/api/keys/[apiKeyId]/roles/[roleName]/route": "/api/keys/[apiKeyId]/roles/[roleName]", "/api/keys/[apiKeyId]/roles/route": "/api/keys/[apiKeyId]/roles", "/api/keys/[apiKeyId]/route": "/api/keys/[apiKeyId]", "/api/logs/route": "/api/logs", "/api/keys/route": "/api/keys", "/api/orchestration/stream/[executionId]/route": "/api/orchestration/stream/[executionId]", "/api/orchestration/process-step/route": "/api/orchestration/process-step", "/api/orchestration/start/route": "/api/orchestration/start", "/api/orchestration/status/[executionId]/route": "/api/orchestration/status/[executionId]", "/api/orchestration/synthesis-fallback/[executionId]/route": "/api/orchestration/synthesis-fallback/[executionId]", "/api/orchestration/synthesis-stream-direct/[executionId]/route": "/api/orchestration/synthesis-stream-direct/[executionId]", "/api/playground/route": "/api/playground", "/api/providers/list-models/route": "/api/providers/list-models", "/api/orchestration/synthesis-stream/[executionId]/route": "/api/orchestration/synthesis-stream/[executionId]", "/api/stripe/create-checkout-session/route": "/api/stripe/create-checkout-session", "/api/stripe/customer-portal/route": "/api/stripe/customer-portal", "/api/stripe/subscription-status/route": "/api/stripe/subscription-status", "/api/stripe/webhooks/route": "/api/stripe/webhooks", "/api/training/jobs/upsert/route": "/api/training/jobs/upsert", "/api/training/jobs/route": "/api/training/jobs", "/api/user/custom-roles/[customRoleId]/route": "/api/user/custom-roles/[customRoleId]", "/api/user/custom-roles/route": "/api/user/custom-roles", "/auth/callback/route": "/auth/callback", "/api/v1/chat/completions/route": "/api/v1/chat/completions", "/about/page": "/about", "/auth/signin/page": "/auth/signin", "/analytics/page": "/analytics", "/auth/signup/page": "/auth/signup", "/auth/verify-email/page": "/auth/verify-email", "/debug-session/page": "/debug-session", "/features/page": "/features", "/checkout/page": "/checkout", "/page": "/", "/logs/page": "/logs", "/my-models/[configId]/page": "/my-models/[configId]", "/my-models/page": "/my-models", "/playground/page": "/playground", "/routing-setup/[configId]/page": "/routing-setup/[configId]", "/pricing/page": "/pricing", "/routing-setup/page": "/routing-setup", "/training/page": "/training", "/add-keys/page": "/add-keys"}